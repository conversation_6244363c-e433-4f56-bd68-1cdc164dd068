steps:
  # 1. Setup and basic validation
  - name: 'python:3.12-slim'
    id: 'setup-environment'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🚀 Setting up build environment..."
        apt-get update && apt-get install -y git curl
        echo "✅ Environment setup complete"

  # 2. Build Backend Docker Image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-backend'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '-f'
      - 'rayuela_backend/Dockerfile'
      - 'rayuela_backend'

  # 3. Push Backend image (needed for temporary deployment)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-backend-temp'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
    waitFor: ['build-backend']

  # 4. Build Frontend (Static OpenAPI - No Backend Required)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-frontend'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🏗️ Building frontend with static OpenAPI (no backend dependency)"
        echo "📂 Current directory: $(pwd)"
        echo "📋 Directory contents:"
        ls -la rayuela_frontend/
        
        # Check if OpenAPI file exists
        if [ -f "rayuela_frontend/src/lib/openapi/openapi.json" ]; then
          echo "✅ OpenAPI specification found"
          echo "📊 File size: $(wc -c < rayuela_frontend/src/lib/openapi/openapi.json) bytes"
        else
          echo "⚠️ No OpenAPI file found at expected location"
          echo "📁 Searching for OpenAPI files:"
          find rayuela_frontend/ -name "*.json" -path "*/openapi/*" 2>/dev/null || echo "No OpenAPI files found"
        fi
        
        # Verify Dockerfile exists
        if [ -f "rayuela_frontend/Dockerfile" ]; then
          echo "✅ Dockerfile found"
        else
          echo "❌ Dockerfile not found!"
          exit 1
        fi
        
        # Verify package.json exists
        if [ -f "rayuela_frontend/package.json" ]; then
          echo "✅ package.json found"
        else
          echo "❌ package.json not found!"
          exit 1
        fi
        
        echo "🔧 Starting Docker build with detailed output..."
        set -x
        
        # Build frontend with static configuration and avoid backend connection
        docker build \
          -t us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID \
          --build-arg NEXT_PUBLIC_API_URL="https://rayuela-backend-*************.us-central1.run.app" \
          --build-arg ORVAL_USE_STATIC=true \
          --build-arg NODE_ENV=production \
          --build-arg SKIP_OPENAPI_FETCH=true \
          --build-arg EMERGENCY_DEPLOY=${_EMERGENCY_DEPLOY:-false} \
          -f rayuela_frontend/Dockerfile \
          rayuela_frontend
          
        set +x
        echo "✅ Frontend Docker build completed successfully"
    waitFor: ['push-backend-temp']

  # 5. Push Frontend image  
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-frontend'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'
    waitFor: ['build-frontend']

  # 10. SECURE: Run Database Migrations as Pre-deployment Step
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'run-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🔄 EJECUTANDO MIGRACIONES DE BASE DE DATOS PRE-DESPLIEGUE..."
        echo "🏗️ Esta es una implementación de mejores prácticas para sistemas escalables"
        echo "✅ Las migraciones se ejecutan ANTES del despliegue de la aplicación"
        echo "🔒 Mayor control y robustez en el proceso de despliegue"
        
        # Install Python and dependencies
        echo "📦 Instalando dependencias para migraciones..."
        apt-get update -qq && apt-get install -y -qq python3 python3-pip python3-venv git
        
        # Use backend code from workspace
        echo "📥 Usando código del backend desde workspace..."
        cd /workspace/rayuela_backend || { echo "❌ No se pudo acceder al código del backend"; exit 1; }
        
        # Create virtual environment and install dependencies properly
        echo "🐍 Configurando entorno Python para migraciones..."
        python3 -m venv migration_env
        source migration_env/bin/activate
        
        # Upgrade pip first
        pip install --upgrade pip
        
        # Install specific dependencies needed for migrations
        echo "📦 Instalando dependencias específicas..."
        pip install alembic==1.13.2 asyncpg==0.29.0 psycopg2-binary==2.9.9 python-dotenv==1.0.1 sqlalchemy==2.0.40 pydantic-settings==2.8.1 google-cloud-storage==3.1.0 google-cloud-redis==2.18.1 google-cloud-logging==3.11.4 google-cloud-secret-manager==2.16.4
        
        # Verify alembic installation
        echo "🔍 Verificando instalación de Alembic..."
        alembic --version || { echo "❌ Alembic no se instaló correctamente"; exit 1; }
        
        # Add the source directory to Python path
        export PYTHONPATH="/workspace/rayuela_backend/src"

        # Set minimal environment variables for migrations
        export ENV=production
        export GCS_BUCKET_NAME=rayuela-production-bucket
        export ALLOWED_ORIGINS='["https://rayuela-frontend-*************.us-central1.run.app"]'
        
        # Setup environment for migrations
        export ENV=production
        export GCP_PROJECT_ID=$PROJECT_ID
        export GCP_REGION=us-central1

        # Load database credentials from Secret Manager
        echo "🔐 Cargando credenciales de base de datos..."
        export POSTGRES_USER=$(gcloud secrets versions access latest --secret="POSTGRES_USER")
        export POSTGRES_DB=$(gcloud secrets versions access latest --secret="POSTGRES_DB")
        export POSTGRES_PASSWORD=$(gcloud secrets versions access latest --secret="POSTGRES_PASSWORD")
        export SECRET_KEY=$(gcloud secrets versions access latest --secret="SECRET_KEY")

        # Get Cloud SQL instance connection name
        SQL_INSTANCE_NAME="rayuela-production-db"
        CONNECTION_NAME="$PROJECT_ID:us-central1:$$SQL_INSTANCE_NAME"

        echo "🔗 Configurando Cloud SQL Proxy para conectividad segura..."
        echo "   Instancia: $CONNECTION_NAME"

        # Download and setup Cloud SQL Proxy
        wget -q https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy
        chmod +x cloud_sql_proxy

        # Start Cloud SQL Proxy in background
        echo "🚀 Iniciando Cloud SQL Proxy..."
        ./cloud_sql_proxy -instances=$CONNECTION_NAME=tcp:5432 &
        PROXY_PID=$!

        # Wait for proxy to be ready
        echo "⏳ Esperando que Cloud SQL Proxy esté listo..."
        sleep 10

        # Configure connection to use proxy
        export POSTGRES_SERVER="127.0.0.1"
        export POSTGRES_PORT="5432"

        echo "📋 Configuración de conexión (via Cloud SQL Proxy):"
        echo "   DB Host: $POSTGRES_SERVER (proxy local)"
        echo "   DB Name: $POSTGRES_DB"
        echo "   DB User: $POSTGRES_USER"
        echo "   Instancia remota: $CONNECTION_NAME"
        
        # Check current migration status
        echo "📋 Verificando estado actual de migraciones..."
        alembic current || echo "⚠️ Estado de migración no disponible"

        # Run migrations
        echo "🚀 Ejecutando migraciones con Alembic..."
        alembic upgrade head

        if [ $? -eq 0 ]; then
          echo "✅ MIGRACIONES COMPLETADAS EXITOSAMENTE"
          echo "🎯 Base de datos lista para nuevo despliegue"

          # Verify migration status after upgrade
          echo "📊 Estado final de migraciones:"
          alembic current || echo "✅ Migraciones ejecutadas correctamente"
        else
          echo "❌ ERROR EN MIGRACIONES - DETENIENDO DESPLIEGUE"
          echo "🚨 El despliegue no puede continuar sin migraciones exitosas"
          exit 1
        fi
        
        echo "🔒 VENTAJAS DE ESTA IMPLEMENTACIÓN:"
        echo "   ✓ Migraciones ejecutadas ANTES del despliegue de la aplicación"
        echo "   ✓ Mayor control sobre el proceso de actualización de BD"
        echo "   ✓ Reduce riesgo de fallos en startup de la aplicación"
        echo "   ✓ Permite despliegues blue/green más seguros"
        echo "   ✓ Facilita rollbacks controlados si es necesario"

        # Cleanup: Stop Cloud SQL Proxy
        echo "🧹 Limpiando Cloud SQL Proxy..."
        kill $PROXY_PID 2>/dev/null || echo "Proxy ya terminado"
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-central1'
    waitFor: ['push-frontend']

  # 11. Deploy Backend to Cloud Run (Main API Service)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-backend'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-backend'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--timeout=300s'
      - '--concurrency=80'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=api'
      - '--set-env-vars=ALLOWED_HOSTS=rayuela-backend-*************.us-central1.run.app'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['run-migrations']

  # 12. Get Backend URL for Frontend
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'get-backend-url'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)")
        echo "Backend URL: $$BACKEND_URL"
        echo "$$BACKEND_URL" > /workspace/backend_url.txt
    waitFor: ['deploy-backend']

  # 13. Deploy Frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-frontend'
    entrypoint: bash
    args:
      - '-c'
      - |
        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        echo "Deploying frontend with backend URL: $$BACKEND_URL"
        gcloud run deploy rayuela-frontend \
          --image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID \
          --region=us-central1 \
          --platform=managed \
          --allow-unauthenticated \
          --memory=1Gi \
          --cpu=1 \
          --min-instances=0 \
          --max-instances=5 \
          --timeout=300s \
          --set-env-vars=NEXT_PUBLIC_API_BASE_URL="$$BACKEND_URL",NODE_ENV=production \
          --vpc-connector=rayuela-vpc-connector \
          --service-account=rayuela-frontend-sa@$PROJECT_ID.iam.gserviceaccount.com
    waitFor: ['get-backend-url']

  # 14. Verify Database Migration Status (Optional Check)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'verify-migrations'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "✅ Database migrations were securely executed in step 9"
        echo "🔒 No public endpoints were used for migration execution"
        echo "📊 Migration verification completed"
    waitFor: ['get-backend-url']

  # 15. Deploy Celery Worker for Maintenance Tasks
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-worker-maintenance'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-worker-maintenance'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--min-instances=1'
      - '--max-instances=2'
      - '--timeout=3600s'  # 1 hour timeout for long-running maintenance tasks
      - '--concurrency=1'
      - '--command=bash'
      - '--args=/app/start.sh'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=maintenance'
      - '--set-env-vars=GCS_BUCKET_NAME=rayuela-storage-production,GCP_SERVICE_ACCOUNT_KEY_PATH=/secrets/gcp/service-account.json'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['verify-migrations']

  # 16. Deploy Celery Beat Scheduler
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-celery-beat'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'rayuela-beat'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=0.5'
      - '--min-instances=1'
      - '--max-instances=1'
      - '--timeout=3600s'
      - '--concurrency=1'
      - '--command=bash'
      - '--args=/app/start.sh'
      - '--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO,WORKER_TYPE=beat'
      - '--set-env-vars=GCS_BUCKET_NAME=rayuela-storage-production,GCP_SERVICE_ACCOUNT_KEY_PATH=/secrets/gcp/service-account.json'
      - '--set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest'
      - '--set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest'
      - '--vpc-connector=rayuela-vpc-connector'
      - '--service-account=rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['verify-migrations']

  # 17. Health check
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'health-check'
    entrypoint: bash
    args:
      - '-c'
      - |
        echo "🏥 Performing health checks..."

        BACKEND_URL=$$(cat /workspace/backend_url.txt)
        FRONTEND_URL=$$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)")

        echo "Backend URL: $$BACKEND_URL"
        echo "Frontend URL: $$FRONTEND_URL"

        # Test Backend
        echo "Testing backend health..."
        curl -f "$$BACKEND_URL/health" || echo "Backend health check failed"

        # Verify Celery services are running
        echo "🔧 Verifying Celery services..."

        # Check worker status
        WORKER_STATUS=$$(gcloud run services describe rayuela-worker-maintenance --region=us-central1 --format="value(status.conditions[0].status)" 2>/dev/null || echo "Unknown")
        echo "Worker Maintenance Status: $$WORKER_STATUS"

        # Check beat scheduler status
        BEAT_STATUS=$$(gcloud run services describe rayuela-beat --region=us-central1 --format="value(status.conditions[0].status)" 2>/dev/null || echo "Unknown")
        echo "Celery Beat Status: $$BEAT_STATUS"

        echo "✅ Deployment completed successfully!"
        echo "🌐 Frontend: $$FRONTEND_URL"
        echo "🔧 Backend API: $$BACKEND_URL"
        echo "⚙️ Worker Maintenance: $$WORKER_STATUS"
        echo "⏰ Celery Beat: $$BEAT_STATUS (manages partition tasks daily)"
    waitFor: ['deploy-frontend', 'deploy-worker-maintenance', 'deploy-celery-beat']

# Images to store in registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-frontend:$BUILD_ID'

# Timeout
timeout: '3600s'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  env: 
    - 'CLOUDSDK_COMPUTE_REGION=us-central1'
  substitutionOption: ALLOW_LOOSE

# SECURITY: Use dedicated service account with minimal permissions (least privilege)
serviceAccount: 'projects/$PROJECT_ID/serviceAccounts/rayuela-cloudbuild-sa@$PROJECT_ID.iam.gserviceaccount.com'
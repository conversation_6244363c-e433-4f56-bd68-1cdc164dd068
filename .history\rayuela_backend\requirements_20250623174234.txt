# =============================================================================
# API Framework
# =============================================================================
fastapi==0.115.12
fastapi-cache2==0.2.2
fastapi-limiter==0.1.6
uvicorn==0.34.0
python-multipart==0.0.20
asgiref==3.8.1
itsdangerous==2.1.2

# =============================================================================
# Data Validation & Settings
# =============================================================================
pydantic==2.9.2
email-validator==2.2.0
pydantic-settings==2.8.1

# =============================================================================
# Database
# =============================================================================
sqlalchemy==2.0.40
asyncpg==0.30.0

# =============================================================================
# Authentication & Security
# =============================================================================
python-jose[cryptography]==3.4.0
passlib[bcrypt]==1.7.4

# =============================================================================
# Google Cloud
# =============================================================================
google-cloud-storage==3.1.0
google-cloud-logging==3.11.4
google-cloud-redis==2.18.1
google-cloud-secret-manager==2.16.4
# google-cloud-bigquery==3.25.0

# =============================================================================
# Caching & Task Queue
# =============================================================================
redis==5.2.1
celery==5.5.1
flower==2.0.1

# =============================================================================
# Data Science & Machine Learning
# =============================================================================
numpy==2.1.3
pandas==2.2.3
pyarrow==18.1.0
scipy==1.15.3
joblib==1.5.0
scikit-learn==1.6.1
implicit==0.7.2
torch==2.7.0
# tensorflow-cpu==2.19.0
# tf-keras==2.19.0
transformers==4.51.3
sentence-transformers[torch]==4.1.0

# =============================================================================
# Payment Processing
# =============================================================================
mercadopago==2.3.0

# =============================================================================
# Utilities
# =============================================================================
python-dotenv==1.1.0
tenacity==9.1.2
click==8.1.8

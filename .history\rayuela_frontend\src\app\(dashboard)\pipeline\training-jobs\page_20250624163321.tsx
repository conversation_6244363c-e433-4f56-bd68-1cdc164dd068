"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Brain,
  ChevronLeft,
  AlertCircle,
  RotateCcw,
  Eye,
  Filter,
  Search,
  Play
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useTrainingJobs } from '@/lib/hooks/useTrainingJobs';
import { TrainingModal } from '@/components/pipeline/TrainingModal';
import { getStatusIcon, getStatusBadge, formatDuration } from '@/lib/utils/format';

// Types for training jobs
interface TrainingJob {
  id: number;
  model_name: string;
  model_version: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration?: number;
  error_message?: string;
  parameters?: Record<string, number | string>;
  metrics?: Record<string, number>;
  task_id?: string;
}

type FilterStatus = 'all' | 'pending' | 'processing' | 'completed' | 'failed';

export default function TrainingJobsPage() {
  const { jobs, isLoading, error, startTraining } = useTrainingJobs();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<FilterStatus>('all');
  const [selectedJob, setSelectedJob] = useState<TrainingJob | null>(null);

  const filteredJobs = jobs.filter(job => {
    const matchesStatus = statusFilter === 'all' || job.status.toLowerCase() === statusFilter;
    const matchesSearch = searchQuery === '' || 
      job.model_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.model_version.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.id.toString().includes(searchQuery);
    
    return matchesStatus && matchesSearch;
  });



  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
  };

  const canRetryJob = (job: TrainingJob) => {
    return job.status === 'FAILED';
  };

  const handleRetryJob = (jobId: number) => {
    // Implement retry logic here
    console.log('Retrying job:', jobId);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        <div className="bg-card/50 border border-border/50 rounded-lg p-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section */}
      <div className="bg-card/50 border border-border/50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Link href="/pipeline" className="text-muted-foreground hover:text-foreground">
                <ChevronLeft className="h-5 w-5" />
              </Link>
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Brain className="h-8 w-8 text-purple-500" />
                Historial de Entrenamientos
              </h1>
            </div>
            <p className="text-muted-foreground">
              Seguimiento completo de todos tus trabajos de entrenamiento de modelos
            </p>
            <div className="flex gap-4 mt-4 text-sm text-muted-foreground">
              <span>Total: {jobs.length}</span>
              <span>Completados: {jobs.filter(j => j.status === 'COMPLETED').length}</span>
              <span>En proceso: {jobs.filter(j => j.status === 'PROCESSING').length}</span>
              <span>Fallidos: {jobs.filter(j => j.status === 'FAILED').length}</span>
            </div>
          </div>
          <TrainingModal 
            onTrainingStart={startTraining}
            trigger={
              <Button>
                <Play className="h-4 w-4 mr-2" />
                Nuevo Entrenamiento
              </Button>
            }
          />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters Section */}
      <Card>
        <CardHeader className="border-b border-border/20 bg-muted/20">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Buscar por modelo, versión o ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Label htmlFor="status-filter" className="text-sm font-medium whitespace-nowrap">Estado:</Label>
              <Select value={statusFilter} onValueChange={(value: FilterStatus) => setStatusFilter(value)}>
                <SelectTrigger id="status-filter" className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendientes</SelectItem>
                  <SelectItem value="processing">Procesando</SelectItem>
                  <SelectItem value="completed">Completados</SelectItem>
                  <SelectItem value="failed">Fallidos</SelectItem>
                </SelectContent>
              </Select>
              {(searchQuery || statusFilter !== 'all') && (
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Limpiar
                </Button>
              )}
            </div>
          </div>
          {(searchQuery || statusFilter !== 'all') && (
            <div className="mt-2 text-sm text-muted-foreground">
              Mostrando {filteredJobs.length} de {jobs.length} trabajos
            </div>
          )}
        </CardContent>
      </Card>

      {/* Jobs Table */}
      <Card>
        <CardHeader className="border-b border-border/20 bg-muted/20">
          <CardTitle>Trabajos de Entrenamiento</CardTitle>
          <CardDescription>
            Lista completa de entrenamientos con detalles y métricas
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-hidden">
            <Table>
              <TableHeader className="bg-muted/10">
                <TableRow className="border-b border-border/30">
                  <TableHead className="font-semibold">Job ID</TableHead>
                  <TableHead className="font-semibold">Modelo / Versión</TableHead>
                  <TableHead className="font-semibold">Estado</TableHead>
                  <TableHead className="font-semibold">Fecha Inicio</TableHead>
                  <TableHead className="font-semibold">Duración</TableHead>
                  <TableHead className="font-semibold">Métricas</TableHead>
                  <TableHead className="text-right font-semibold">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredJobs.length > 0 ? (
                  filteredJobs.map((job, index) => (
                    <TableRow 
                      key={job.id} 
                      className={`
                        border-b border-border/20 
                        hover:bg-muted/30 
                        transition-colors
                        ${index % 2 === 0 ? 'bg-background' : 'bg-muted/5'}
                      `}
                    >
                      <TableCell className="font-medium py-4">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          #{job.id}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-medium">{job.model_name}</div>
                          <div className="text-sm text-muted-foreground">{job.model_version}</div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        {getStatusBadge(job.status)}
                      </TableCell>
                      <TableCell className="py-4 text-muted-foreground">
                        {format(new Date(job.created_at), "d 'de' MMM, yyyy HH:mm", { locale: es })}
                      </TableCell>
                      <TableCell className="py-4 text-muted-foreground">
                        {job.duration ? formatDuration(job.duration) : job.status === 'PROCESSING' ? '⏳ En curso' : '—'}
                      </TableCell>
                      <TableCell className="py-4">
                        {job.metrics ? (
                          <div className="text-sm">
                            <div>Acc: {(job.metrics.accuracy * 100).toFixed(1)}%</div>
                            <div className="text-muted-foreground">F1: {(job.metrics.f1_score * 100).toFixed(1)}%</div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedJob(job)}
                                className="h-8 w-8 p-0 hover:bg-muted/50"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Detalles del Job #{job.id}</DialogTitle>
                                <DialogDescription>
                                  Información completa del trabajo de entrenamiento
                                </DialogDescription>
                              </DialogHeader>
                              {selectedJob && (
                                <div className="space-y-4 max-h-96 overflow-y-auto">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium">Modelo</Label>
                                      <p className="text-sm">{selectedJob.model_name} {selectedJob.model_version}</p>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Estado</Label>
                                      <div className="mt-1">{getStatusBadge(selectedJob.status)}</div>
                                    </div>
                                  </div>
                                  
                                  {selectedJob.parameters && (
                                    <div>
                                      <Label className="text-sm font-medium">Parámetros</Label>
                                      <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                                        {JSON.stringify(selectedJob.parameters, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                  
                                  {selectedJob.metrics && (
                                    <div>
                                      <Label className="text-sm font-medium">Métricas</Label>
                                      <div className="grid grid-cols-2 gap-2 mt-1">
                                        {Object.entries(selectedJob.metrics).map(([key, value]) => (
                                          <div key={key} className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">{key}</div>
                                            <div className="text-sm">{(value * 100).toFixed(2)}%</div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                  
                                  {selectedJob.error_message && (
                                    <div>
                                      <Label className="text-sm font-medium text-destructive">Error</Label>
                                      <Alert variant="destructive" className="mt-1">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription className="text-sm">
                                          {selectedJob.error_message}
                                        </AlertDescription>
                                      </Alert>
                                    </div>
                                  )}
                                  
                                  {selectedJob.task_id && (
                                    <div>
                                      <Label className="text-sm font-medium">Task ID</Label>
                                      <code className="text-xs bg-muted p-1 rounded block mt-1">
                                        {selectedJob.task_id}
                                      </code>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          
                          {canRetryJob(job) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRetryJob(job.id)}
                              className="h-8 w-8 p-0 hover:bg-muted/50"
                              disabled
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        {jobs.length === 0 ? (
                          <>
                            <Brain className="h-8 w-8" />
                            <p>No hay trabajos de entrenamiento aún</p>
                            <p className="text-sm">Los trabajos aparecerán aquí cuando inicies entrenamientos</p>
                          </>
                        ) : (
                          <>
                            <Search className="h-8 w-8" />
                            <p>No se encontraron trabajos con los filtros aplicados</p>
                            <Button variant="outline" size="sm" onClick={clearFilters}>
                              Limpiar filtros
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información sobre entrenamientos</AlertTitle>
        <AlertDescription>
          <div className="space-y-2 text-sm mt-2">
            <p>
              Los trabajos de entrenamiento pueden tomar desde minutos hasta horas dependiendo 
              del tamaño de los datos y la complejidad del modelo.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li><strong>Pendiente:</strong> El trabajo está en cola esperando recursos</li>
              <li><strong>Procesando:</strong> El entrenamiento está en curso</li>
              <li><strong>Completado:</strong> El modelo se entrenó exitosamente</li>
              <li><strong>Fallido:</strong> Ocurrió un error durante el entrenamiento</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
} 
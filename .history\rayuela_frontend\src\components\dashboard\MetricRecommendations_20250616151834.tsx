"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import {
  LightbulbIcon,
  TrendingUpIcon,
  BarChart2Icon,
  ShuffleIcon,
  ZapIcon,
  UsersIcon,
  DatabaseIcon,
  RefreshCwIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from 'lucide-react';
import { SemanticIcon } from '@/components/ui/icon';
import { RecommendationPerformanceMetrics, ConfidenceMetrics } from '@/lib/api/recommendation-metrics';
import { generateMetricRecommendations, Recommendation } from '@/lib/analysis';
import { formatString, metricRecommendationsStrings } from '@/lib/constants';

interface MetricRecommendationsProps {
  performanceData?: RecommendationPerformanceMetrics;
  confidenceData?: ConfidenceMetrics;
  isLoading?: boolean;
}

export default function MetricRecommendations({
  performanceData,
  confidenceData,
  isLoading = false
}: MetricRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [expandedCategory, setExpandedCategory] = useState<string | undefined>(undefined);

  // Generar recomendaciones basadas en las métricas actuales
  useEffect(() => {
    if (!performanceData || !confidenceData) return;

    // Usar la función de utilidad para generar recomendaciones
    const iconComponents = {
      BarChart2Icon,
      ShuffleIcon,
      ZapIcon,
      AlertCircleIcon,
      DatabaseIcon,
      UsersIcon,
      TrendingUpIcon,
      LightbulbIcon
    };

    const newRecommendations = generateMetricRecommendations(
      performanceData,
      confidenceData,
      iconComponents
    );

    setRecommendations(newRecommendations);
  }, [performanceData, confidenceData]);

  // Agrupar recomendaciones por categoría
  const recommendationsByCategory = recommendations.reduce((acc, rec) => {
    if (!acc[rec.category]) {
      acc[rec.category] = [];
    }
    acc[rec.category].push(rec);
    return acc;
  }, {} as Record<string, Recommendation[]>);

  // Mapeo de categorías a nombres legibles
  const categoryNames: Record<string, { name: string, icon: React.ReactNode }> = {
    accuracy: {
      name: metricRecommendationsStrings.CATEGORY_ACCURACY,
      icon: <SemanticIcon icon={BarChart2Icon} size="md" context="metric" />
    },
    diversity: {
      name: metricRecommendationsStrings.CATEGORY_DIVERSITY,
      icon: <SemanticIcon icon={ShuffleIcon} size="md" context="metric" />
    },
    confidence: {
      name: metricRecommendationsStrings.CATEGORY_CONFIDENCE,
      icon: <SemanticIcon icon={CheckCircleIcon} size="md" context="metric" />
    },
    performance: {
      name: metricRecommendationsStrings.CATEGORY_PERFORMANCE,
      icon: <SemanticIcon icon={ZapIcon} size="md" context="metric" />
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SemanticIcon icon={LightbulbIcon} size="md" context="warning" />
            {metricRecommendationsStrings.COMPONENT_TITLE}
          </CardTitle>
          <CardDescription>
            {metricRecommendationsStrings.LOADING_TEXT}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!performanceData || !confidenceData || recommendations.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SemanticIcon icon={CheckCircleIcon} size="md" context="success" />
            {metricRecommendationsStrings.OPTIMIZED_TITLE}
          </CardTitle>
          <CardDescription>
            {metricRecommendationsStrings.OPTIMIZED_DESCRIPTION}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            {metricRecommendationsStrings.OPTIMIZED_MESSAGE}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <SemanticIcon icon={LightbulbIcon} size="md" context="warning" />
          {metricRecommendationsStrings.COMPONENT_TITLE}
        </CardTitle>
        <CardDescription>
          {formatString(metricRecommendationsStrings.COMPONENT_DESCRIPTION, { count: Object.values(recommendationsByCategory).flat().length })}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Accordion
          type="single"
          collapsible
          value={expandedCategory}
          onValueChange={setExpandedCategory}
          className="space-y-4"
        >
          {Object.entries(recommendationsByCategory).map(([category, recs]) => (
            <AccordionItem
              key={category}
              value={category}
              className="border rounded-lg overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="flex items-center gap-2 text-left">
                  {categoryNames[category]?.icon}
                  <div>
                    <h3 className="font-medium">{categoryNames[category]?.name || category}</h3>
                    <p className="text-sm text-muted-foreground">
                      {formatString(metricRecommendationsStrings.RECOMMENDATION_COUNT, { count: recs.length })}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-0">
                <div className="space-y-4 pt-2">
                  {recs.map((rec) => (
                    <div
                      key={rec.id}
                      className="px-4 py-3 border-t border-gray-100 dark:border-gray-800"
                    >
                      <div className="flex items-start gap-3">
                        <div className="mt-1">{rec.icon}</div>
                        <div className="space-y-2 w-full">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{rec.title}</h4>
                            <Badge
                              variant={
                                rec.priority === 'high' ? 'destructive' :
                                  rec.priority === 'medium' ? 'default' : 'secondary'
                              }
                              className="capitalize"
                            >
                              {rec.priority}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{rec.description}</p>

                          {/* Métricas relacionadas */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 my-3">
                            {rec.metrics.map((metric, idx) => (
                              <div
                                key={idx}
                                className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md"
                              >
                                <div className="text-xs text-muted-foreground">{metric.name}</div>
                                <div className="flex items-center justify-between">
                                  <div className="font-medium">{metric.value.toFixed(1)}{metric.unit}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {formatString(metricRecommendationsStrings.METRIC_TARGET, { value: metric.target, unit: metric.unit })}
                                  </div>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                                  <div
                                    className={`h-1.5 rounded-full ${metric.value >= metric.target
                                      ? 'bg-green-500'
                                      : metric.value >= metric.target * 0.8
                                        ? 'bg-amber-500'
                                        : 'bg-red-500'
                                      }`}
                                    style={{ width: `${Math.min(100, (metric.value / metric.target) * 100)}%` }}
                                  ></div>
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Acciones recomendadas */}
                          <div>
                            <h5 className="text-sm font-medium mb-2">{metricRecommendationsStrings.ACTIONS_TITLE}</h5>
                            <ul className="space-y-1">
                              {rec.actions.map((action, idx) => (
                                <li key={idx} className="text-sm flex items-start gap-2">
                                  <SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" className="mt-0.5 flex-shrink-0" />
                                  <span>{action}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}

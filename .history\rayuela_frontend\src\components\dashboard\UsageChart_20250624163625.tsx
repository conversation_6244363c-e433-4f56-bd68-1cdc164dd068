"use client";

import { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  chartColors,
  formatPercentage,
  UsageDataPoint,
  transformUsageData
} from '@/lib/chart-utils';
import { formatBytes, formatNumber } from '@/lib/utils/format';
import { Badge } from '@/components/ui/badge';
import { InfoIcon, AlertTriangle, BarChart3 } from 'lucide-react';
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

// Registrar los componentes necesarios de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

interface UsageChartProps {
  data?: UsageDataPoint[];
  isLoading?: boolean;
  error?: Error | null;
  title?: string;
  description?: string;
  apiCallsLimit?: number;
  storageLimit?: number;
}

export default function UsageChart({
  data = [],
  isLoading = false,
  error = null,
  title = "Uso de API",
  description = "Estadísticas de uso en los últimos 30 días",
  apiCallsLimit,
  storageLimit
}: UsageChartProps) {
  const [activeTab, setActiveTab] = useState('apiCalls');
  const [chartData, setChartData] = useState<UsageDataPoint[]>([]);

  useEffect(() => {
    // Use the utility function to transform or generate data
    setChartData(data && data.length > 0 ? data : transformUsageData([]));
  }, [data]);

  // Extend the base chart options with specific settings for usage charts
  const commonOptions: ChartOptions<'line' | 'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          title: (context) => {
            return `Fecha: ${context[0].label}`;
          },
          label: (context) => {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (label.includes('Almacenamiento')) {
                label += formatBytes(context.parsed.y * 1024 * 1024); // Convert MB back to bytes for formatting
              } else {
                label += formatNumber(context.parsed.y);
              }
            }
            return label;
          },
          footer: (context) => {
            const datasetLabel = context[0].dataset.label || '';
            if (datasetLabel.includes('Llamadas') && apiCallsLimit) {
              const percentage = Math.min(((context[0].parsed.y / apiCallsLimit) * 100), 100);
              return `${formatPercentage(percentage)} del límite (${formatNumber(apiCallsLimit)})`;
            } else if (datasetLabel.includes('Almacenamiento') && storageLimit) {
              const limitInMB = storageLimit / (1024 * 1024);
              const percentage = Math.min(((context[0].parsed.y / limitInMB) * 100), 100);
              return `${formatPercentage(percentage)} del límite (${formatBytes(storageLimit)})`;
            }
            return '';
          }
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          font: {
            size: 11
          },
          callback: function (value) {
            return formatNumber(value as number);
          }
        }
      },
    },
  };

  // Datos para el gráfico de llamadas a la API
  const apiCallsData = {
    labels: chartData.map(d => d.date.slice(5)), // Formato MM-DD
    datasets: [
      {
        label: 'Llamadas a la API',
        data: chartData.map(d => d.apiCalls),
        borderColor: chartColors.blue.primary,
        backgroundColor: chartColors.blue.background,
        tension: 0.3,
        fill: true,
      },
    ],
  };

  // Datos para el gráfico de almacenamiento
  const storageData = {
    labels: chartData.map(d => d.date.slice(5)), // Formato MM-DD
    datasets: [
      {
        label: 'Almacenamiento (MB)',
        data: chartData.map(d => d.storage / (1024 * 1024)), // Convertir bytes a MB
        borderColor: chartColors.green.primary,
        backgroundColor: chartColors.green.background,
        tension: 0.3,
      },
    ],
  };

  // Opciones específicas para cada gráfico
  const apiCallsOptions = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'Llamadas a la API por día',
        font: {
          size: 13,
          weight: 'normal'
        },
        padding: {
          top: 10,
          bottom: 10
        }
      },
      annotation: apiCallsLimit ? {
        annotations: {
          limitLine: {
            type: 'line',
            yMin: apiCallsLimit,
            yMax: apiCallsLimit,
            borderColor: chartColors.red.primary,
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              display: true,
              content: `Límite: ${formatNumber(apiCallsLimit)}`,
              position: 'end',
              backgroundColor: 'rgba(239, 68, 68, 0.7)',
              font: {
                size: 11
              }
            }
          }
        }
      } : undefined,
    },
  };

  const storageOptions = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'Almacenamiento utilizado (MB)',
        font: {
          size: 13,
          weight: 'normal'
        },
        padding: {
          top: 10,
          bottom: 10
        }
      },
      annotation: storageLimit ? {
        annotations: {
          limitLine: {
            type: 'line',
            yMin: storageLimit / (1024 * 1024),
            yMax: storageLimit / (1024 * 1024),
            borderColor: chartColors.red.primary,
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              display: true,
              content: `Límite: ${formatBytes(storageLimit)}`,
              position: 'end',
              backgroundColor: 'rgba(239, 68, 68, 0.7)',
              font: {
                size: 11
              }
            }
          }
        }
      } : undefined,
    },
  };

  // Renderizar un skeleton durante la carga
  if (isLoading) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <Skeleton className="h-full w-full rounded-md" />
        </CardContent>
      </Card>
    );
  }

  // Renderizar un mensaje de error
  if (error) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-destructive flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Error al cargar datos
          </CardTitle>
          <CardDescription className="text-destructive/70">
            No se pudieron cargar los datos de uso
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-destructive mb-2">Ocurrió un error al cargar los datos de uso.</p>
          <p className="text-muted-foreground text-sm">
            Intenta refrescar la página o contacta al soporte técnico.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>
              Monitoreo de uso en el rango seleccionado
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="info">
              Período: Últimos 30 días
            </Badge>
            {chartData.some(d => d.apiCalls > 0) && (
              <Badge variant="success">
                Datos disponibles
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="chart" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger 
              value="chart"
              className="transition-all duration-200 data-[state=active]:bg-info/10 data-[state=active]:text-info"
            >
              Gráfico
            </TabsTrigger>
            <TabsTrigger 
              value="table"
              className="transition-all duration-200 data-[state=active]:bg-success/10 data-[state=active]:text-success"
            >
              Tabla
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardContent>
    </Card>
  );
}

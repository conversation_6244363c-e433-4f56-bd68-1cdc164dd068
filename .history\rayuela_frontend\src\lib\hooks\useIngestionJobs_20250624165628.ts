import { useState, useEffect } from 'react';
import { getRayuela } from '@/lib/generated/rayuelaAPI';

export interface IngestionJob {
  id: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration?: number;
  error_message?: string;
  file_path?: string;
  records_processed?: {
    users?: number;
    products?: number;
    interactions?: number;
    total?: number;
  };
  file_size?: number;
  task_id?: string;
}

export function useIngestionJobs() {
  const [jobs, setJobs] = useState<IngestionJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Implement batch jobs listing endpoint
      try {
        // const response = await getRayuela().listBatchJobsApiV1IngestionBatchGet();
        // const jobsList = response.data;
        const jobsList: unknown[] = [];
        
        // Transform the backend response to match our interface
        const jobsData: IngestionJob[] = jobsList.map((jobStatus: unknown) => {
          // Type assertion for API response structure
          const apiJobStatus = jobStatus as {
            job_id: number;
            status: string;
            created_at: string;
            started_at?: string;
            completed_at?: string;
            error_message?: string;
            task_id?: string;
            processed_count?: {
              users?: number;
              products?: number;
              interactions?: number;
              total?: number;
            };
          };

          const job: IngestionJob = {
            id: apiJobStatus.job_id,
            status: apiJobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            created_at: apiJobStatus.created_at,
            started_at: apiJobStatus.started_at || undefined,
            completed_at: apiJobStatus.completed_at || undefined,
            error_message: apiJobStatus.error_message || undefined,
            task_id: apiJobStatus.task_id || undefined,
            records_processed: apiJobStatus.processed_count ? {
              users: typeof apiJobStatus.processed_count.users === 'number' ? apiJobStatus.processed_count.users : undefined,
              products: typeof apiJobStatus.processed_count.products === 'number' ? apiJobStatus.processed_count.products : undefined,
              interactions: typeof apiJobStatus.processed_count.interactions === 'number' ? apiJobStatus.processed_count.interactions : undefined,
              total: typeof apiJobStatus.processed_count.total === 'number' ? apiJobStatus.processed_count.total : undefined
            } : undefined,
          };
          
          // Calculate duration if both started_at and completed_at are available
          if (job.started_at && job.completed_at) {
            const startTime = new Date(job.started_at).getTime();
            const endTime = new Date(job.completed_at).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          return job;
        });
        
        setJobs(jobsData);
        return; // Exit early if successful
      } catch (listingError) {
        console.warn('Listing endpoint not available, falling back to localStorage approach:', listingError);
      }
      
      // Fallback: Get recent batch ingestion jobs from localStorage and individual status calls
      // Since there's no dedicated listing endpoint, we'll simulate it by
      // checking if we have any stored job IDs or use a pattern based on known job IDs
      
      const storedJobIds = localStorage.getItem('ingestionJobIds');
      const jobIds: number[] = storedJobIds ? JSON.parse(storedJobIds) : [];
      
      const jobsData: IngestionJob[] = [];
      
      // Fetch status for each known job ID
      for (const jobId of jobIds) {
        try {
          const response = await getRayuela().getBatchJobStatusApiV1IngestionBatchJobIdGet(jobId);
          const jobStatus = response.data;
          
          // Transform the backend response to match our interface
          const job: IngestionJob = {
            id: jobStatus.job_id,
            status: jobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            created_at: jobStatus.created_at,
            started_at: jobStatus.started_at || undefined,
            completed_at: jobStatus.completed_at || undefined,
            error_message: jobStatus.error_message || undefined,
            task_id: jobStatus.task_id || undefined,
            records_processed: jobStatus.processed_count ? {
              users: typeof jobStatus.processed_count.users === 'number' ? jobStatus.processed_count.users : undefined,
              products: typeof jobStatus.processed_count.products === 'number' ? jobStatus.processed_count.products : undefined,
              interactions: typeof jobStatus.processed_count.interactions === 'number' ? jobStatus.processed_count.interactions : undefined,
              total: typeof jobStatus.processed_count.total === 'number' ? jobStatus.processed_count.total : undefined
            } : undefined,
          };
          
          // Calculate duration if both started_at and completed_at are available
          if (job.started_at && job.completed_at) {
            const startTime = new Date(job.started_at).getTime();
            const endTime = new Date(job.completed_at).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          jobsData.push(job);
        } catch (jobError) {
          console.warn(`Error fetching job ${jobId}:`, jobError);
          // Continue with other jobs even if one fails
        }
      }
      
      // Sort jobs by creation date (newest first)
      jobsData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setJobs(jobsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading ingestion jobs');
      console.error('Error loading ingestion jobs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getJobStatus = async (jobId: number) => {
    try {
      const response = await getRayuela().getBatchJobStatusApiV1IngestionBatchJobIdGet(jobId);
      return response.data;
    } catch (err) {
      console.error('Error fetching job status:', err);
      throw err;
    }
  };

  const startBatchIngestion = async (data: Record<string, unknown>) => {
    try {
      const response = await getRayuela().batchDataIngestionApiV1IngestionBatchPost(data);
      
      // Store the new job ID for future retrieval
      const jobId = response.data.job_id;
      const storedJobIds = localStorage.getItem('ingestionJobIds');
      const jobIds: number[] = storedJobIds ? JSON.parse(storedJobIds) : [];
      jobIds.unshift(jobId); // Add to beginning of array
      
      // Keep only the last 50 job IDs to avoid localStorage bloat
      const limitedJobIds = jobIds.slice(0, 50);
      localStorage.setItem('ingestionJobIds', JSON.stringify(limitedJobIds));
      
      // Refresh jobs list after starting new ingestion
      await fetchJobs();
      return response.data;
    } catch (err) {
      console.error('Error starting batch ingestion:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return {
    jobs,
    isLoading,
    error,
    fetchJobs,
    getJobStatus,
    startBatchIngestion
  };
} 
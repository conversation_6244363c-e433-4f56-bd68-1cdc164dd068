// src/lib/useRecommendationMetrics.ts
import useSWR from 'swr';
import { 
  getRecommendationPerformance, 
  getConfidenceMetrics,
  RecommendationPerformanceMetrics,
  ConfidenceMetrics as ApiConfidenceMetrics
} from '@/lib/api/recommendation-metrics';
import { useAuth } from '@/lib/auth';
import { useCallback } from 'react';

// Local type definitions
export interface ModelMetrics {
  model_id: number;
  model_name: string;
  precision: number;
  recall: number;
  ndcg: number;
  coverage: number;
  novelty: number;
  serendipity: number;
}

// Tipos mejorados con tipado estricto
export interface RecommendationPerformanceResponse {
  offlineMetrics: {
    precision: number;
    recall: number;
    ndcg: number;
    mapScore: number;
    catalogCoverage: number;
    diversity: number;
    novelty: number;
    serendipity: number;
    models: ModelMetrics[];
  };
  onlineMetrics: {
    ctr: number;
    cvr: number;
    userEngagement: number;
    sessionDuration: number;
    bounceRate: number;
  };
  modelId?: number | null;
}

// Type guard function for backward compatibility
function isRecommendationPerformanceResponse(data: unknown): data is RecommendationPerformanceResponse {
  if (!data || typeof data !== 'object') return false;
  const d = data as Record<string, unknown>;
  return (
    typeof d.offlineMetrics === 'object' && d.offlineMetrics !== null &&
    typeof d.onlineMetrics === 'object' && d.onlineMetrics !== null
  );
}

/**
 * Hook para obtener y gestionar las métricas de rendimiento de recomendaciones.
 * 
 * @param modelId ID del modelo específico (opcional)
 * @param metricType Tipo de métrica a filtrar (opcional)
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de métricas de rendimiento, estado de carga, errores y funciones de utilidad
 */
export function useRecommendationPerformance(
  modelId?: number,
  metricType?: string,
  options: {
    revalidateOnFocus?: boolean;
    refreshInterval?: number;
    dedupingInterval?: number;
    errorRetryCount?: number;
  } = {}
) {
  const { token, apiKey } = useAuth();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR<RecommendationPerformanceMetrics>(
    token && apiKey ? ['recommendation-performance', modelId, metricType] : null,
    async () => 
      await getRecommendationPerformance(modelId, metricType),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 300000, // 5 minutos por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para obtener la precisión general
  const getPrecision = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.precision || 0;
  }, [data]);

  // Función para obtener la diversidad general
  const getDiversity = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.diversity || 0;
  }, [data]);

  // Función para obtener la novedad general
  const getNovelty = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.novelty || 0;
  }, [data]);

  // Función para obtener el NDCG general
  const getNDCG = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.ndcg || 0;
  }, [data]);

  // Función para obtener la serendipia general
  const getSerendipity = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.serendipity || 0;
  }, [data]);

  // Función para obtener la cobertura del catálogo
  const getCatalogCoverage = useCallback((): number => {
    if (!data || !isRecommendationPerformanceResponse(data)) return 0;
    return data.offlineMetrics?.catalogCoverage || 0;
  }, [data]);

  // Función para obtener datos formateados para gráficos
  const getChartData = useCallback(() => {
    if (!data || !isRecommendationPerformanceResponse(data)) return [];
    
    const metrics = data.offlineMetrics;
    if (!metrics) return [];
    
    return [
      { name: 'Precisión', value: (metrics.precision || 0) * 100 },
      { name: 'Diversidad', value: (metrics.diversity || 0) * 100 },
      { name: 'Novedad', value: (metrics.novelty || 0) * 100 },
      { name: 'NDCG', value: (metrics.ndcg || 0) * 100 },
      { name: 'Serendipia', value: (metrics.serendipity || 0) * 100 },
      { name: 'Cobertura', value: (metrics.catalogCoverage || 0) * 100 }
    ];
  }, [data]);

  return {
    performanceData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: null, // SWR doesn't expose this directly
    getPrecision,
    getDiversity,
    getNovelty,
    getNDCG,
    getSerendipity,
    getCatalogCoverage,
    getChartData
  };
}

// Definición de tipos para métricas de confianza
interface ConfidenceDistribution {
  low: number;
  medium: number;
  high: number;
  avg: number;
}

interface ModelConfidenceMetrics {
  collaborative?: ConfidenceDistribution;
  content?: ConfidenceDistribution;
  hybrid?: ConfidenceDistribution;
}

interface ConfidenceMetrics {
  confidenceDistribution: ModelConfidenceMetrics;
  globalConfidence?: number;
  trend?: 'increasing' | 'decreasing' | 'stable';
  lastUpdated?: string;
}

// Type guard para métricas de confianza de la API
function isApiConfidenceMetrics(data: unknown): data is ApiConfidenceMetrics {
  if (!data || typeof data !== 'object') return false;
  return true; // La API respuesta es simplemente un objeto genérico por ahora
}

/**
 * Hook para obtener y gestionar las métricas de confianza de recomendaciones.
 * 
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de métricas de confianza, estado de carga, errores y funciones de utilidad
 */
export function useConfidenceMetrics(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}) {
  const { token, apiKey } = useAuth();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate
  } = useSWR<ApiConfidenceMetrics>(
    token && apiKey ? ['confidence-metrics'] : null,
    async () => await getConfidenceMetrics(),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 300000, // 5 minutos por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para obtener la confianza global
  const getGlobalConfidence = useCallback((): number => {
    if (!data || !isApiConfidenceMetrics(data)) return 0;
    
    // Safe access to API response structure
    const apiData = data as Record<string, unknown>;
    const confidenceDistribution = apiData.confidenceDistribution as Record<string, Record<string, number>> | undefined;
    
    if (!confidenceDistribution) return 0;
    
    // Calculate average from all model types
    const models = ['collaborative', 'content', 'hybrid'];
    const avgConfidences = models
      .map(model => {
        const modelData = confidenceDistribution[model];
        return modelData?.avg || 0;
      })
      .filter(avg => avg > 0);
    
    return avgConfidences.length > 0 
      ? avgConfidences.reduce((sum, avg) => sum + avg, 0) / avgConfidences.length 
      : 0;
  }, [data]);

  // Función para obtener la confianza promedio por tipo de modelo
  const getAverageConfidenceByModelType = useCallback((): Record<string, number> => {
    if (!data || !isApiConfidenceMetrics(data)) return {};
    
    // Safe access to API response structure
    const apiData = data as Record<string, unknown>;
    const confidenceDistribution = apiData.confidenceDistribution as Record<string, Record<string, number>> | undefined;
    
    if (!confidenceDistribution) return {};
    
    const result: Record<string, number> = {};
    const models = ['collaborative', 'content', 'hybrid'];
    
    models.forEach(model => {
      const distribution = confidenceDistribution[model];
      if (distribution && typeof distribution.avg === 'number') {
        result[model] = distribution.avg;
      }
    });
    
    return result;
  }, [data]);

  // Función para obtener datos formateados para gráficos
  const getConfidenceChartData = useCallback(() => {
    if (!data || !isApiConfidenceMetrics(data)) return [];
    
    // Safe access to API response structure
    const apiData = data as Record<string, unknown>;
    const confidenceDistribution = apiData.confidenceDistribution as Record<string, Record<string, number>> | undefined;
    
    if (!confidenceDistribution) return [];
    
    const models = ['collaborative', 'content', 'hybrid'];
    
    return models.map(model => {
      const distribution = confidenceDistribution[model] || {};
      return {
        name: model.charAt(0).toUpperCase() + model.slice(1),
        avg: distribution.avg || 0,
        low: distribution.low || 0,
        medium: distribution.medium || 0,
        high: distribution.high || 0
      };
    }).filter(item => item.avg > 0);
  }, [data]);

  return {
    confidenceData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    getGlobalConfidence,
    getAverageConfidenceByModelType,
    getConfidenceChartData
  };
}

#!/bin/bash

# Script para ejecutar migraciones manualmente desde Cloud Shell
# Usa la imagen del backend ya construida para ejecutar las migraciones

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"us-central1"}
BUILD_ID="9c69ef41-ba7d-4568-9d6c-13558be58e58"  # Latest successful build
IMAGE_URL="us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID"

echo -e "${BLUE}🔄 EJECUTANDO MIGRACIONES MANUALMENTE${NC}"
echo "============================================================"
echo -e "${BLUE}Proyecto: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Región: ${REGION}${NC}"
echo -e "${BLUE}Imagen: ${IMAGE_URL}${NC}"
echo ""

# Verify the image exists
echo -e "${BLUE}🔍 Verificando que la imagen existe...${NC}"
if ! gcloud container images describe $IMAGE_URL >/dev/null 2>&1; then
    echo -e "${RED}❌ La imagen no existe: $IMAGE_URL${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Imagen encontrada${NC}"

# Create a temporary Cloud Run job to run migrations
JOB_NAME="rayuela-migrations-$(date +%s)"
echo -e "${BLUE}🚀 Creando job temporal para migraciones: $JOB_NAME${NC}"

# Create the job
gcloud run jobs create $JOB_NAME \
    --image=$IMAGE_URL \
    --region=$REGION \
    --task-timeout=1800 \
    --memory=2Gi \
    --cpu=1 \
    --max-retries=0 \
    --parallelism=1 \
    --task-count=1 \
    --set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=$REGION,WORKER_TYPE=migration \
    --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest \
    --set-secrets=REDIS_HOST=REDIS_HOST:latest,REDIS_PORT=REDIS_PORT:latest,REDIS_DB=REDIS_DB:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,REDIS_URL=REDIS_URL:latest \
    --vpc-connector=rayuela-vpc-connector \
    --service-account=rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com \
    --command=bash \
    --args="-c,cd /app && python -c 'import sys; sys.path.append(\"/app/src\")' && alembic upgrade head"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Job creado exitosamente${NC}"
else
    echo -e "${RED}❌ Error creando el job${NC}"
    exit 1
fi

# Execute the job
echo -e "${BLUE}🔄 Ejecutando migraciones...${NC}"
EXECUTION_NAME=$(gcloud run jobs execute $JOB_NAME --region=$REGION --format="value(metadata.name)")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Migraciones iniciadas${NC}"
    echo -e "${BLUE}📋 Execution: $EXECUTION_NAME${NC}"
    
    # Wait for completion and show logs
    echo -e "${BLUE}⏳ Esperando completación...${NC}"
    gcloud run jobs executions describe $EXECUTION_NAME --region=$REGION --format="value(status.completionTime)" --wait
    
    # Get the final status
    STATUS=$(gcloud run jobs executions describe $EXECUTION_NAME --region=$REGION --format="value(status.conditions[0].type)")
    
    if [ "$STATUS" = "Completed" ]; then
        echo -e "${GREEN}🎉 MIGRACIONES COMPLETADAS EXITOSAMENTE${NC}"
        
        # Show logs
        echo -e "${BLUE}📋 Logs de la ejecución:${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME" --limit=50 --format="value(textPayload)" --freshness=10m
        
    else
        echo -e "${RED}❌ MIGRACIONES FALLARON${NC}"
        echo -e "${BLUE}📋 Logs de error:${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME AND severity>=ERROR" --limit=20 --format="value(textPayload)" --freshness=10m
        exit 1
    fi
else
    echo -e "${RED}❌ Error ejecutando el job${NC}"
    exit 1
fi

# Cleanup: Delete the temporary job
echo -e "${BLUE}🧹 Limpiando job temporal...${NC}"
gcloud run jobs delete $JOB_NAME --region=$REGION --quiet

echo ""
echo -e "${GREEN}🎉 PROCESO COMPLETADO${NC}"
echo ""
echo -e "${BLUE}📋 Próximos pasos:${NC}"
echo -e "${BLUE}   1. Verificar que el backend responde correctamente${NC}"
echo -e "${BLUE}   2. Habilitar servicios de Celery${NC}"
echo -e "${BLUE}   3. Ejecutar health checks completos${NC}"

#!/bin/bash

# Script para ejecutar migraciones manualmente
# Enfoque simplificado usando Cloud Run Jobs

set -e

# Configuration
PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"
BUILD_ID="9c69ef41-ba7d-4568-9d6c-13558be58e58"
IMAGE_URL="us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID"

echo "🔄 EJECUTANDO MIGRACIONES MANUALMENTE"
echo "============================================================"
echo "Proyecto: $PROJECT_ID"
echo "Región: $REGION"
echo "Imagen: $IMAGE_URL"
echo ""

# Delete existing job if it exists
echo "🧹 Limpiando job anterior si existe..."
gcloud run jobs delete rayuela-migrations --region=$REGION --quiet 2>/dev/null || echo "No hay job anterior"

# Create the job with working directory set correctly
echo "🚀 Creando job para migraciones..."
gcloud run jobs create rayuela-migrations \
    --image=$IMAGE_URL \
    --region=$REGION \
    --task-timeout=1800 \
    --memory=2Gi \
    --cpu=1 \
    --max-retries=0 \
    --parallelism=1 \
    --set-env-vars=ENV=production,PYTHONPATH=/app/src,WORKER_TYPE=migration \
    --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest \
    --vpc-connector=rayuela-vpc-connector \
    --command=bash \
    --args="-c,cd /app && /usr/local/bin/python -m alembic upgrade head"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Job creado exitosamente${NC}"
else
    echo -e "${RED}❌ Error creando el job${NC}"
    exit 1
fi

# Execute the job
echo -e "${BLUE}🔄 Ejecutando migraciones...${NC}"
EXECUTION_NAME=$(gcloud run jobs execute $JOB_NAME --region=$REGION --format="value(metadata.name)")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Migraciones iniciadas${NC}"
    echo -e "${BLUE}📋 Execution: $EXECUTION_NAME${NC}"
    
    # Wait for completion and show logs
    echo -e "${BLUE}⏳ Esperando completación...${NC}"
    gcloud run jobs executions describe $EXECUTION_NAME --region=$REGION --format="value(status.completionTime)" --wait
    
    # Get the final status
    STATUS=$(gcloud run jobs executions describe $EXECUTION_NAME --region=$REGION --format="value(status.conditions[0].type)")
    
    if [ "$STATUS" = "Completed" ]; then
        echo -e "${GREEN}🎉 MIGRACIONES COMPLETADAS EXITOSAMENTE${NC}"
        
        # Show logs
        echo -e "${BLUE}📋 Logs de la ejecución:${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME" --limit=50 --format="value(textPayload)" --freshness=10m
        
    else
        echo -e "${RED}❌ MIGRACIONES FALLARON${NC}"
        echo -e "${BLUE}📋 Logs de error:${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME AND severity>=ERROR" --limit=20 --format="value(textPayload)" --freshness=10m
        exit 1
    fi
else
    echo -e "${RED}❌ Error ejecutando el job${NC}"
    exit 1
fi

# Cleanup: Delete the temporary job
echo -e "${BLUE}🧹 Limpiando job temporal...${NC}"
gcloud run jobs delete $JOB_NAME --region=$REGION --quiet

echo ""
echo -e "${GREEN}🎉 PROCESO COMPLETADO${NC}"
echo ""
echo -e "${BLUE}📋 Próximos pasos:${NC}"
echo -e "${BLUE}   1. Verificar que el backend responde correctamente${NC}"
echo -e "${BLUE}   2. Habilitar servicios de Celery${NC}"
echo -e "${BLUE}   3. Ejecutar health checks completos${NC}"

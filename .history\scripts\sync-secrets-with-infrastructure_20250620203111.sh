#!/bin/bash

# Script para sincronizar secretos con la infraestructura real
# Detecta automáticamente las IPs y configuraciones actuales

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"us-central1"}

echo -e "${BLUE}🔄 SINCRONIZANDO SECRETOS CON INFRAESTRUCTURA REAL${NC}"
echo "============================================================"
echo -e "${BLUE}Proyecto: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Región: ${REGION}${NC}"
echo ""

# Function to update or create secret
update_secret() {
    local secret_name=$1
    local secret_value=$2
    local description=$3
    
    echo -e "${YELLOW}🔧 Actualizando secreto: $secret_name${NC}"
    echo -e "${BLUE}   Valor: $secret_value${NC}"
    echo -e "${BLUE}   Descripción: $description${NC}"
    
    if gcloud secrets describe $secret_name &>/dev/null; then
        echo -n "$secret_value" | gcloud secrets versions add $secret_name --data-file=-
        echo -e "${GREEN}   ✅ Secreto actualizado${NC}"
    else
        echo -n "$secret_value" | gcloud secrets create $secret_name --data-file=-
        echo -e "${GREEN}   ✅ Secreto creado${NC}"
    fi
    echo ""
}

# 1. Detectar instancias de Cloud SQL
echo -e "${BLUE}🔍 Detectando instancias de Cloud SQL...${NC}"
SQL_INSTANCES=$(gcloud sql instances list --format="value(name)" 2>/dev/null || echo "")

if [ -z "$SQL_INSTANCES" ]; then
    echo -e "${RED}❌ No se encontraron instancias de Cloud SQL${NC}"
    exit 1
fi

echo -e "${GREEN}📋 Instancias encontradas:${NC}"
for instance in $SQL_INSTANCES; do
    # Try different methods to get private IP
    PRIVATE_IP=$(gcloud sql instances describe $instance --format="value(ipAddresses[0].ipAddress)" 2>/dev/null || echo "")
    if [ -z "$PRIVATE_IP" ]; then
        PRIVATE_IP=$(gcloud sql instances describe $instance --format="json" | grep -A 10 '"type": "PRIVATE"' | grep '"ipAddress"' | cut -d'"' -f4 2>/dev/null || echo "")
    fi
    STATE=$(gcloud sql instances describe $instance --format="value(state)" 2>/dev/null || echo "UNKNOWN")
    echo -e "${BLUE}   • $instance: $PRIVATE_IP (Estado: $STATE)${NC}"
done

# Determinar cuál instancia usar para producción
PRODUCTION_INSTANCE=""
PRODUCTION_IP=""

# Prioridad: rayuela-production-db > rayuela-postgres > primera disponible
for instance in $SQL_INSTANCES; do
    if [[ $instance == *"production"* ]]; then
        PRODUCTION_INSTANCE=$instance
        break
    fi
done

if [ -z "$PRODUCTION_INSTANCE" ]; then
    for instance in $SQL_INSTANCES; do
        if [[ $instance == *"postgres"* ]]; then
            PRODUCTION_INSTANCE=$instance
            break
        fi
    done
fi

if [ -z "$PRODUCTION_INSTANCE" ]; then
    PRODUCTION_INSTANCE=$(echo $SQL_INSTANCES | head -n1)
fi

# Try different methods to get private IP
PRODUCTION_IP=$(gcloud sql instances describe $PRODUCTION_INSTANCE --format="value(ipAddresses[0].ipAddress)" 2>/dev/null || echo "")
if [ -z "$PRODUCTION_IP" ]; then
    PRODUCTION_IP=$(gcloud sql instances describe $PRODUCTION_INSTANCE --format="json" | grep -A 10 '"type": "PRIVATE"' | grep '"ipAddress"' | cut -d'"' -f4 2>/dev/null || echo "")
fi

echo ""
echo -e "${GREEN}🎯 Instancia seleccionada para producción:${NC}"
echo -e "${BLUE}   Instancia: $PRODUCTION_INSTANCE${NC}"
echo -e "${BLUE}   IP Privada: $PRODUCTION_IP${NC}"

if [ -z "$PRODUCTION_IP" ]; then
    echo -e "${RED}❌ No se pudo obtener la IP privada de la instancia${NC}"
    exit 1
fi

# 2. Detectar instancias de Redis
echo ""
echo -e "${BLUE}🔍 Detectando instancias de Redis...${NC}"
REDIS_INSTANCES=$(gcloud redis instances list --region=$REGION --format="value(name)" 2>/dev/null || echo "")

if [ -z "$REDIS_INSTANCES" ]; then
    echo -e "${RED}❌ No se encontraron instancias de Redis${NC}"
    exit 1
fi

echo -e "${GREEN}📋 Instancias Redis encontradas:${NC}"
for instance in $REDIS_INSTANCES; do
    REDIS_HOST=$(gcloud redis instances describe $instance --region=$REGION --format="value(host)" 2>/dev/null || echo "")
    REDIS_PORT=$(gcloud redis instances describe $instance --region=$REGION --format="value(port)" 2>/dev/null || echo "")
    echo -e "${BLUE}   • $instance: $REDIS_HOST:$REDIS_PORT${NC}"
done

# Determinar cuál instancia Redis usar para producción
PRODUCTION_REDIS=""
PRODUCTION_REDIS_HOST=""
PRODUCTION_REDIS_PORT=""

for instance in $REDIS_INSTANCES; do
    if [[ $instance == *"production"* ]]; then
        PRODUCTION_REDIS=$instance
        break
    fi
done

if [ -z "$PRODUCTION_REDIS" ]; then
    PRODUCTION_REDIS=$(echo $REDIS_INSTANCES | head -n1)
fi

PRODUCTION_REDIS_HOST=$(gcloud redis instances describe $PRODUCTION_REDIS --region=$REGION --format="value(host)" 2>/dev/null || echo "")
PRODUCTION_REDIS_PORT=$(gcloud redis instances describe $PRODUCTION_REDIS --region=$REGION --format="value(port)" 2>/dev/null || echo "")

echo ""
echo -e "${GREEN}🎯 Instancia Redis seleccionada para producción:${NC}"
echo -e "${BLUE}   Instancia: $PRODUCTION_REDIS${NC}"
echo -e "${BLUE}   Host: $PRODUCTION_REDIS_HOST${NC}"
echo -e "${BLUE}   Puerto: $PRODUCTION_REDIS_PORT${NC}"

# 3. Actualizar secretos con valores detectados
echo ""
echo -e "${BLUE}🔐 Actualizando secretos...${NC}"

# PostgreSQL secrets
update_secret "POSTGRES_SERVER" "$PRODUCTION_IP" "IP privada de la instancia Cloud SQL de producción"
update_secret "POSTGRES_USER" "postgres" "Usuario de PostgreSQL"
update_secret "POSTGRES_DB" "rayuela_production" "Nombre de la base de datos de producción"
update_secret "POSTGRES_PORT" "5432" "Puerto de PostgreSQL"

# Redis secrets
update_secret "REDIS_HOST" "$PRODUCTION_REDIS_HOST" "Host de Redis de producción"
update_secret "REDIS_PORT" "$PRODUCTION_REDIS_PORT" "Puerto de Redis"
update_secret "REDIS_URL" "redis://$PRODUCTION_REDIS_HOST:$PRODUCTION_REDIS_PORT/0" "URL completa de Redis"

# 4. Verificar que todos los secretos necesarios existen
echo -e "${BLUE}🔍 Verificando secretos requeridos...${NC}"

REQUIRED_SECRETS=(
    "POSTGRES_USER" "POSTGRES_PASSWORD" "POSTGRES_SERVER" "POSTGRES_PORT" "POSTGRES_DB"
    "REDIS_HOST" "REDIS_PORT" "REDIS_URL" "REDIS_PASSWORD"
    "SECRET_KEY"
)

MISSING_SECRETS=()

for secret in "${REQUIRED_SECRETS[@]}"; do
    if gcloud secrets describe "$secret" >/dev/null 2>&1; then
        echo -e "${GREEN}   ✅ $secret${NC}"
    else
        echo -e "${RED}   ❌ $secret${NC}"
        MISSING_SECRETS+=("$secret")
    fi
done

if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}⚠️ Secretos faltantes detectados. Ejecutando configuración...${NC}"
    
    for secret in "${MISSING_SECRETS[@]}"; do
        case $secret in
            "POSTGRES_PASSWORD")
                echo -e "${YELLOW}🔧 Generando contraseña para PostgreSQL...${NC}"
                DB_PASSWORD=$(openssl rand -base64 32)
                update_secret "POSTGRES_PASSWORD" "$DB_PASSWORD" "Contraseña de PostgreSQL"
                # También actualizar DB_PASSWORD para compatibilidad
                update_secret "DB_PASSWORD" "$DB_PASSWORD" "Contraseña de base de datos (alias)"
                ;;
            "REDIS_PASSWORD")
                update_secret "REDIS_PASSWORD" "" "Contraseña de Redis (vacía para instancias sin auth)"
                ;;
            "SECRET_KEY")
                echo -e "${YELLOW}🔧 Generando clave secreta...${NC}"
                SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(50))" 2>/dev/null || openssl rand -base64 50)
                update_secret "SECRET_KEY" "$SECRET_KEY" "Clave secreta de la aplicación"
                ;;
        esac
    done
fi

echo ""
echo -e "${GREEN}🎉 SINCRONIZACIÓN COMPLETADA${NC}"
echo ""
echo -e "${BLUE}📋 Configuración final:${NC}"
echo -e "${BLUE}   • PostgreSQL: $PRODUCTION_INSTANCE ($PRODUCTION_IP)${NC}"
echo -e "${BLUE}   • Redis: $PRODUCTION_REDIS ($PRODUCTION_REDIS_HOST:$PRODUCTION_REDIS_PORT)${NC}"
echo -e "${BLUE}   • Secretos: Sincronizados con infraestructura real${NC}"
echo ""
echo -e "${GREEN}✅ Listo para despliegue!${NC}"

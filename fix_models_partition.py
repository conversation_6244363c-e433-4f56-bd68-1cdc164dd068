#!/usr/bin/env python3
"""
Script temporal para corregir automáticamente todos los modelos que tienen 
declaraciones redundantes de postgresql_partition_by
"""

import os
import re

# Lista de archivos que necesitan corrección
models_to_fix = [
    "rayuela_backend/src/db/models/audit_log.py",
    "rayuela_backend/src/db/models/model_metadata.py", 
    "rayuela_backend/src/db/models/model_metric.py",
    "rayuela_backend/src/db/models/recommendation.py",
    "rayuela_backend/src/db/models/search.py",
    "rayuela_backend/src/db/models/system_user_role.py",
    "rayuela_backend/src/db/models/training_metrics.py",
    "rayuela_backend/src/db/models/training_job.py",
]

def fix_model_file(file_path):
    """Corrige un archivo de modelo individual"""
    print(f"Procesando {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar y reemplazar patrones de importación
    import_patterns = [
        (r'from \.mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE', 
         'from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args'),
        (r'from \.mixins import TenantMixin, ACCOUNT_RANGE', 
         'from .mixins import TenantMixin, get_tenant_table_args'),
        (r'from src\.db\.models\.mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE',
         'from src.db.models.mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args'),
    ]
    
    for pattern, replacement in import_patterns:
        content = re.sub(pattern, replacement, content)
    
    # Reemplazar __table_args__ con get_tenant_table_args
    # Buscar patrones como: __table_args__ = (constraints..., {"postgresql_partition_by": ACCOUNT_RANGE},)
    table_args_pattern = r'__table_args__ = \((.*?),\s*\{"postgresql_partition_by": ACCOUNT_RANGE\},?\s*\)'
    
    def replace_table_args(match):
        constraints = match.group(1).strip()
        if constraints.endswith(','):
            constraints = constraints[:-1]  # Remover coma final si existe
        return f'__table_args__ = get_tenant_table_args(\n        {constraints}\n    )'
    
    content = re.sub(table_args_pattern, replace_table_args, content, flags=re.DOTALL)
    
    # También manejar casos donde postgresql_partition_by está en línea separada
    content = re.sub(r',\s*\{"postgresql_partition_by": ACCOUNT_RANGE\}', '', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} corregido")

def main():
    print("🔧 Corrigiendo modelos con declaraciones redundantes de postgresql_partition_by...")
    
    for model_file in models_to_fix:
        if os.path.exists(model_file):
            try:
                fix_model_file(model_file)
            except Exception as e:
                print(f"❌ Error procesando {model_file}: {e}")
        else:
            print(f"⚠️  Archivo no encontrado: {model_file}")
    
    print("✅ Proceso completado")

if __name__ == "__main__":
    main() 
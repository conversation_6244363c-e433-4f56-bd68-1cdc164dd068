# Build stage
FROM python:3.12-slim AS builder

WORKDIR /app

# Install build dependencies including PostgreSQL dev libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	build-essential \
	gcc \
	g++ \
	gfortran \
	libgomp1 \
	libopenblas-dev \
	libpq-dev \
	pkg-config \
	&& rm -rf /var/lib/apt/lists/*

# Copy requirements and add gunicorn
COPY requirements.txt .
RUN echo "gunicorn==21.2.0" >> requirements.txt

# Build wheels for all dependencies
RUN pip install --upgrade pip setuptools wheel
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Final stage
FROM python:3.12-slim

# Install runtime dependencies including PostgreSQL client libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	libgomp1 \
	libopenblas0 \
	libpq5 \
	curl \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 appuser

WORKDIR /app

# Copy wheels and requirements from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install ALL dependencies from wheels
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir /wheels/* && \
    pip install --no-cache-dir asgiref==3.8.1 && \
    pip install --no-cache-dir itsdangerous && \
    pip install --no-cache-dir aiohttp && \
    rm -rf /wheels /root/.cache/pip/*

# Critical dependency verification during build
RUN python -c "
try:
    import fastapi
    import uvicorn
    import sqlalchemy
    import asyncpg
    import pydantic
    import dotenv
    import google.cloud.storage
    import redis
    import celery
    print('✅ All critical imports successful during build!')
except ImportError as e:
    print(f'❌ Import failed during build: {e}')
    raise
"

# Copy application code
COPY ./src ./src
COPY ./alembic ./alembic
COPY ./scripts ./scripts
COPY alembic.ini .
COPY main.py .
COPY gunicorn_conf.py .
COPY health_server.py .
COPY start.sh .
COPY test_deps.py .

# Make scripts executable and change ownership
RUN chmod +x start.sh && \
    chmod +x test_deps.py && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Final dependency test as non-root user
RUN python test_deps.py

# Environment variables for production
ENV ENV=production \
	PYTHONUNBUFFERED=1 \
	PYTHONDONTWRITEBYTECODE=1 \
	API_HOST=0.0.0.0 \
	PORT=8080

# Expose application port
EXPOSE 8080

# Health check
HEALTHCHECK CMD curl --fail http://localhost:8080/health || exit 1

# Use gunicorn with uvicorn workers for production
CMD ["gunicorn", "main:app", "-c", "gunicorn_conf.py"]

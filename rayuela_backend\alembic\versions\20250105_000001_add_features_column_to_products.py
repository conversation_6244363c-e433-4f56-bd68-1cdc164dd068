"""Add features column to products table

Revision ID: 20250105_000001
Revises: 20250105_000000
Create Date: 2025-01-05 00:00:01.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '20250105_000001'
down_revision: Union[str, None] = '20250105_000000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add features column to products table
    op.add_column('products', sa.Column(
        'features',
        postgresql.JSONB(astext_type=sa.Text()),
        nullable=True,
        comment='Structured product attributes (tags, colors, brand, review_summary, questions_and_answers, etc.) for enhanced content-based recommendations'
    ))
    
    # Add GIN index for efficient JSONB querying
    op.create_index(
        'idx_product_features',
        'products',
        ['features'],
        postgresql_using='gin'
    )


def downgrade() -> None:
    # Drop index first
    op.drop_index('idx_product_features', table_name='products')
    
    # Drop features column
    op.drop_column('products', 'features') 
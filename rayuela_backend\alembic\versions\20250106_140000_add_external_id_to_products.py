"""add external_id to products

Revision ID: 20250106_140000
Revises: 20250105_000001
Create Date: 2025-01-06 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250106_140000'
down_revision = '20250105_000001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add external_id column to products table
    op.add_column('products', sa.Column('external_id', sa.String(length=255), nullable=False))
    
    # Add unique constraint for account_id + external_id
    op.create_unique_constraint('uq_product_external_id', 'products', ['account_id', 'external_id'])
    
    # Create index for external_id lookups
    op.create_index('idx_product_external_id', 'products', ['account_id', 'external_id'], unique=False)


def downgrade() -> None:
    # Drop index first
    op.drop_index('idx_product_external_id', table_name='products')
    
    # Drop unique constraint
    op.drop_constraint('uq_product_external_id', 'products', type_='unique')
    
    # Drop external_id column
    op.drop_column('products', 'external_id') 
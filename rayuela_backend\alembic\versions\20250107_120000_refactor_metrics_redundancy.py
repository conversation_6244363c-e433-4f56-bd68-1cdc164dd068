"""Refactor metrics redundancy

Revision ID: 20250107_120000
Revises: 20250106_140000
Create Date: 2025-01-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250107_120000'
down_revision = '20250106_140000'
branch_labels = None
depends_on = None


def upgrade():
    """
    Migrate redundant JSONB metrics data to dedicated tables and modify
    JSONB fields to store only summaries/references
    """
    
    # Step 1: Create temporary function to migrate data from JSONB to ModelMetric
    op.execute("""
        CREATE OR REPLACE FUNCTION migrate_performance_metrics_to_model_metrics()
        RETURNS void AS $$
        DECLARE
            metadata_row RECORD;
            metric_key TEXT;
            metric_value FLOAT;
        BEGIN
            -- Iterate through all ModelMetadata records with performance_metrics
            FOR metadata_row IN 
                SELECT account_id, id, performance_metrics 
                FROM artifact_metadata 
                WHERE performance_metrics IS NOT NULL AND performance_metrics != '{}'::jsonb
            LOOP
                -- Insert each metric from the JSONB into ModelMetric table
                FOR metric_key, metric_value IN 
                    SELECT key, (value::text)::float 
                    FROM jsonb_each_text(metadata_row.performance_metrics)
                    WHERE value ~ '^[0-9]*\.?[0-9]+$'  -- Only numeric values
                LOOP
                    INSERT INTO model_metrics (
                        account_id, 
                        model_metadata_id, 
                        metric_name, 
                        metric_value, 
                        metric_type,
                        timestamp
                    ) VALUES (
                        metadata_row.account_id,
                        metadata_row.id,
                        metric_key,
                        metric_value,
                        'offline',  -- Default to offline metrics
                        CURRENT_TIMESTAMP
                    ) ON CONFLICT DO NOTHING;  -- Avoid duplicates
                END LOOP;
                
                -- Update performance_metrics to contain only summary info
                UPDATE artifact_metadata 
                SET performance_metrics = jsonb_build_object(
                    'summary_generated_at', CURRENT_TIMESTAMP::text,
                    'detailed_metrics_in_model_metrics', true,
                    'metrics_count', (
                        SELECT COUNT(*) 
                        FROM model_metrics 
                        WHERE account_id = metadata_row.account_id 
                        AND model_metadata_id = metadata_row.id
                    )
                )
                WHERE account_id = metadata_row.account_id AND id = metadata_row.id;
            END LOOP;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Step 2: Execute the migration function
    op.execute("SELECT migrate_performance_metrics_to_model_metrics();")
    
    # Step 3: Create function to migrate TrainingJob metrics to TrainingMetrics
    op.execute("""
        CREATE OR REPLACE FUNCTION migrate_training_job_metrics_to_training_metrics()
        RETURNS void AS $$
        DECLARE
            job_row RECORD;
            metric_key TEXT;
            metric_value FLOAT;
        BEGIN
            -- Iterate through all TrainingJob records with metrics
            FOR job_row IN 
                SELECT account_id, id, artifact_metadata_id, metrics 
                FROM training_jobs 
                WHERE metrics IS NOT NULL AND metrics != '{}'::json
            LOOP
                -- Insert training metrics
                INSERT INTO training_metrics (
                    account_id,
                    model_id,
                    accuracy,
                    precision,
                    recall,
                    f1,
                    training_time,
                    additional_metrics,
                    timestamp
                ) VALUES (
                    job_row.account_id,
                    job_row.artifact_metadata_id,
                    COALESCE((job_row.metrics->>'accuracy')::float, NULL),
                    COALESCE((job_row.metrics->>'precision')::float, NULL),
                    COALESCE((job_row.metrics->>'recall')::float, NULL),
                    COALESCE((job_row.metrics->>'f1')::float, NULL),
                    COALESCE((job_row.metrics->>'training_time')::float, NULL),
                    job_row.metrics,  -- Keep original for now
                    CURRENT_TIMESTAMP
                ) ON CONFLICT DO NOTHING;
                
                -- Update TrainingJob metrics to summary
                UPDATE training_jobs 
                SET metrics = json_build_object(
                    'summary_generated_at', CURRENT_TIMESTAMP,
                    'detailed_metrics_in_training_metrics', true,
                    'primary_metric', COALESCE(job_row.metrics->>'accuracy', job_row.metrics->>'precision', 'N/A')
                )
                WHERE account_id = job_row.account_id AND id = job_row.id;
            END LOOP;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Step 4: Execute training metrics migration
    op.execute("SELECT migrate_training_job_metrics_to_training_metrics();")
    
    # Step 5: Add comments to clarify the new purpose of JSONB fields
    op.execute("""
        COMMENT ON COLUMN artifact_metadata.performance_metrics IS 
        'Summary/reference data only. Detailed metrics stored in model_metrics table.';
    """)
    
    op.execute("""
        COMMENT ON COLUMN artifact_metadata.parameters IS 
        'Model configuration parameters. Training-specific parameters in training_jobs.parameters.';
    """)
    
    op.execute("""
        COMMENT ON COLUMN training_jobs.metrics IS 
        'Summary metrics only. Detailed training metrics stored in training_metrics table.';
    """)
    
    op.execute("""
        COMMENT ON COLUMN training_jobs.parameters IS 
        'Training execution parameters for this specific run.';
    """)
    
    # Step 6: Clean up temporary functions
    op.execute("DROP FUNCTION IF EXISTS migrate_performance_metrics_to_model_metrics();")
    op.execute("DROP FUNCTION IF EXISTS migrate_training_job_metrics_to_training_metrics();")


def downgrade():
    """
    Restore original JSONB data from detailed tables (if needed for rollback)
    """
    
    # Restore performance_metrics from model_metrics
    op.execute("""
        UPDATE artifact_metadata 
        SET performance_metrics = (
            SELECT jsonb_object_agg(metric_name, metric_value)
            FROM model_metrics 
            WHERE model_metrics.account_id = artifact_metadata.account_id 
            AND model_metrics.model_metadata_id = artifact_metadata.id
        )
        WHERE EXISTS (
            SELECT 1 FROM model_metrics 
            WHERE model_metrics.account_id = artifact_metadata.account_id 
            AND model_metrics.model_metadata_id = artifact_metadata.id
        );
    """)
    
    # Restore training_jobs metrics from training_metrics
    op.execute("""
        UPDATE training_jobs 
        SET metrics = (
            SELECT json_build_object(
                'accuracy', accuracy,
                'precision', precision,
                'recall', recall,
                'f1', f1,
                'training_time', training_time
            )
            FROM training_metrics 
            WHERE training_metrics.account_id = training_jobs.account_id 
            AND training_metrics.model_id = training_jobs.artifact_metadata_id
            ORDER BY timestamp DESC 
            LIMIT 1
        )
        WHERE EXISTS (
            SELECT 1 FROM training_metrics 
            WHERE training_metrics.account_id = training_jobs.account_id 
            AND training_metrics.model_id = training_jobs.artifact_metadata_id
        );
    """)
    
    # Remove comments
    op.execute("COMMENT ON COLUMN artifact_metadata.performance_metrics IS NULL;")
    op.execute("COMMENT ON COLUMN artifact_metadata.parameters IS NULL;")
    op.execute("COMMENT ON COLUMN training_jobs.metrics IS NULL;")
    op.execute("COMMENT ON COLUMN training_jobs.parameters IS NULL;") 
#!/usr/bin/env python3
"""
Script de verificación de deployment para Rayuela.
Valida configuración crítica antes y durante el despliegue.
"""

import os
import sys
import importlib
import logging
from typing import Dict, Any, List, Tuple

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_environment_variables() -> Dict[str, Any]:
    """Verifica que las variables de entorno críticas estén configuradas."""
    logger.info("🔍 Checking environment variables...")
    
    critical_vars = [
        "ENV",
        "PORT", 
        "POSTGRES_USER",
        "POSTGRES_PASSWORD", 
        "POSTGRES_SERVER",
        "POSTGRES_PORT",
        "POSTGRES_DB",
        "REDIS_HOST",
        "REDIS_PORT",
        "SECRET_KEY"
    ]
    
    results = {}
    missing = []
    
    for var in critical_vars:
        value = os.getenv(var)
        if value:
            # Only show first few chars of sensitive vars
            if var in ["POSTGRES_PASSWORD", "SECRET_KEY"]:
                display_value = value[:4] + "..." if len(value) > 4 else "***"
            else:
                display_value = value
            results[var] = display_value
            logger.info(f"  ✅ {var}: {display_value}")
        else:
            missing.append(var)
            logger.error(f"  ❌ {var}: NOT SET")
    
    if missing:
        logger.error(f"Missing critical environment variables: {', '.join(missing)}")
        return {"status": "error", "missing": missing, "found": results}
    
    logger.info("✅ All critical environment variables are set")
    return {"status": "ok", "variables": results}

def check_critical_imports() -> Dict[str, Any]:
    """Verifica que todas las dependencias críticas se puedan importar."""
    logger.info("🧪 Testing critical imports...")
    
    critical_modules = [
        "fastapi",
        "uvicorn", 
        "sqlalchemy",
        "asyncpg",
        "pydantic",
        "google.cloud.storage",
        "redis",
        "celery"
    ]
    
    results = {}
    failed = []
    
    for module in critical_modules:
        try:
            importlib.import_module(module)
            results[module] = "OK"
            logger.info(f"  ✅ {module}")
        except ImportError as e:
            failed.append(module)
            results[module] = str(e)
            logger.error(f"  ❌ {module}: {e}")
    
    if failed:
        logger.error(f"Failed to import critical modules: {', '.join(failed)}")
        return {"status": "error", "failed": failed, "results": results}
    
    logger.info("✅ All critical modules imported successfully")
    return {"status": "ok", "modules": results}

def check_database_config() -> Dict[str, Any]:
    """Verifica la configuración de la base de datos."""
    logger.info("🗄️ Checking database configuration...")
    
    try:
        from src.core.config import settings
        
        # Build database URL
        db_url = settings.database_url
        logger.info(f"  📍 Database URL format: postgresql+asyncpg://{settings.POSTGRES_USER}:***@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")
        
        # Basic validation
        if not all([settings.POSTGRES_USER, settings.POSTGRES_PASSWORD, settings.POSTGRES_SERVER, settings.POSTGRES_DB]):
            logger.error("  ❌ Missing required database parameters")
            return {"status": "error", "message": "Missing database parameters"}
        
        logger.info("  ✅ Database configuration looks valid")
        return {"status": "ok", "config": {
            "user": settings.POSTGRES_USER,
            "server": settings.POSTGRES_SERVER,
            "port": settings.POSTGRES_PORT,
            "database": settings.POSTGRES_DB
        }}
        
    except Exception as e:
        logger.error(f"  ❌ Error loading database config: {e}")
        return {"status": "error", "message": str(e)}

def check_fastapi_app() -> Dict[str, Any]:
    """Verifica que la aplicación FastAPI se pueda crear."""
    logger.info("🚀 Testing FastAPI app creation...")
    
    try:
        # Try to import the main app
        from main import app
        logger.info(f"  ✅ FastAPI app created: {app.title}")
        
        # Check if health endpoint exists
        routes = [route.path for route in app.routes]
        if "/health" in routes:
            logger.info("  ✅ Health endpoint found")
        else:
            logger.warning("  ⚠️ Health endpoint not found")
        
        return {"status": "ok", "app_title": app.title, "routes_count": len(routes)}
        
    except Exception as e:
        logger.error(f"  ❌ Failed to create FastAPI app: {e}")
        return {"status": "error", "message": str(e)}

def check_worker_type() -> Dict[str, Any]:
    """Verifica el tipo de worker configurado."""
    logger.info("👷 Checking worker type configuration...")
    
    worker_type = os.getenv("WORKER_TYPE", "api")
    logger.info(f"  📋 Worker type: {worker_type}")
    
    valid_types = ["api", "worker", "beat", "maintenance"]
    if worker_type not in valid_types:
        logger.warning(f"  ⚠️ Unknown worker type: {worker_type}. Valid types: {', '.join(valid_types)}")
        return {"status": "warning", "worker_type": worker_type, "valid_types": valid_types}
    
    logger.info(f"  ✅ Valid worker type: {worker_type}")
    return {"status": "ok", "worker_type": worker_type}

def run_deployment_check() -> bool:
    """Ejecuta todas las verificaciones de deployment."""
    logger.info("🏗️ Starting Rayuela deployment verification...")
    logger.info("=" * 50)
    
    checks = [
        ("Environment Variables", check_environment_variables),
        ("Critical Imports", check_critical_imports),
        ("Database Config", check_database_config),
        ("FastAPI App", check_fastapi_app),
        ("Worker Type", check_worker_type)
    ]
    
    all_passed = True
    results = {}
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 {check_name}")
        logger.info("-" * 30)
        
        try:
            result = check_func()
            results[check_name] = result
            
            if result["status"] == "error":
                all_passed = False
                logger.error(f"❌ {check_name} FAILED")
            elif result["status"] == "warning":
                logger.warning(f"⚠️ {check_name} has warnings")
            else:
                logger.info(f"✅ {check_name} PASSED")
                
        except Exception as e:
            logger.error(f"❌ {check_name} FAILED with exception: {e}")
            results[check_name] = {"status": "error", "exception": str(e)}
            all_passed = False
    
    logger.info("\n" + "=" * 50)
    if all_passed:
        logger.info("🎉 All deployment checks PASSED!")
        logger.info("✅ Rayuela is ready for deployment")
    else:
        logger.error("🚨 Some deployment checks FAILED!")
        logger.error("❌ Fix issues before deploying")
    
    return all_passed

if __name__ == "__main__":
    success = run_deployment_check()
    sys.exit(0 if success else 1) 
# Estrategia Centralizada de Invalidación de Caché

## Resumen

Este documento describe la implementación de una estrategia centralizada para la invalidación de caché en Redis, que reemplaza la estrategia dispersa anterior y elimina las dependencias frágiles del middleware.

## Problema Anterior

### Lógica Dispersa
- **CacheInvalidationMiddleware**: Intentaba extraer `user_id` de forma frágil
- **ModelArtifactManager**: Manejo independiente de caché de modelos
- **UsageMeterCacheInvalidator**: Invalidación específica para usage meter
- **Servicios individuales**: Cada servicio implementaba su propia lógica

### Problemas Identificados
- **Extracción frágil de `user_id`**: El middleware intentaba extraer IDs de solicitudes HTTP de manera poco confiable
- **Inconsistencias temporales**: Sin invalidación coordinada, podían existir inconsistencias entre cachés
- **Acoplamiento**: Servicios de negocio acoplados a lógica de caché específica
- **Mantenimiento**: Lógica dispersa difícil de mantener y evolucionar

## Solución Implementada

### CacheService Centralizado

Se ha implementado un `CacheService` centralizado en `src/services/cache_service.py` que:

#### Funcionalidades Principales

1. **Invalidación de Recomendaciones**
   ```python
   # Invalidar recomendaciones de usuario específico
   await cache_service.invalidate_user_recommendations(account_id, user_id)
   
   # Invalidar todas las recomendaciones de una cuenta
   await cache_service.invalidate_account_recommendations(account_id)
   ```

2. **Invalidación de Modelos ML**
   ```python
   # Invalidar modelos ML
   await cache_service.invalidate_model_cache(account_id, model_type)
   
   # Invalidar métricas
   await cache_service.invalidate_metrics_cache(account_id, metric_type)
   ```

3. **Invalidación de Usage Meter**
   ```python
   # Invalidar caché de uso de cuenta
   await cache_service.invalidate_account_usage_cache(account_id)
   
   # Invalidar caché de API key específica
   await cache_service.invalidate_api_key_cache(api_key)
   ```

4. **Métodos de Conveniencia para Servicios**
   ```python
   # Después de interacciones
   await cache_service.invalidate_after_interaction(account_id, user_id)
   
   # Después de actualizar productos
   await cache_service.invalidate_after_product_update(account_id, product_id)
   
   # Después de entrenar modelos
   await cache_service.invalidate_after_model_training(account_id)
   ```

### Patrón de Uso Recomendado

Los servicios de negocio deben llamar explícitamente al `CacheService` después de operaciones relevantes:

```python
# En InteractionService.create_interaction()
async def create_interaction(self, interaction_data):
    # ... lógica de negocio ...
    
    # Invalidar caché explícitamente
    try:
        cache_service = await get_cache_service()
        await cache_service.invalidate_after_interaction(
            account_id=self.account_id, 
            user_id=interaction_data.user_id
        )
    except Exception as cache_error:
        # No fallar la operación principal por errores de caché
        log_error(f"Error invalidando caché: {str(cache_error)}")
```

## Servicios Actualizados

### 1. InteractionService
- **Cambio**: Usa `cache_service.invalidate_after_interaction()` después de crear interacciones
- **Beneficio**: Invalidación explícita y predecible
- **Archivo**: `src/services/interaction_service.py`

### 2. ProductService
- **Cambio**: Usa `cache_service.invalidate_after_product_update()` después de actualizaciones
- **Beneficio**: Invalidación automática cuando productos cambian
- **Archivo**: `src/services/product_service.py`

### 3. ModelArtifactManager
- **Cambio**: Añadido método `invalidate_user_cache()` para compatibilidad
- **Beneficio**: Compatibilidad con código existente mientras se migra
- **Archivo**: `src/ml_pipeline/model_artifact_manager.py`

### 4. CacheInvalidationMiddleware
- **Cambio**: Simplificado para actuar solo como último recurso
- **Beneficio**: Eliminada lógica frágil de extracción de `user_id`
- **Archivo**: `src/middleware/cache_invalidation_middleware.py`

## Patrones de Caché

### Nomenclatura Estándar
```
recommendations:{account_id}:{user_id}:*
user_factors:{account_id}:{user_id}:*
model:{account_id}:{model_type}:*
metric:{account_id}:{metric_type}:*
subscription:{account_id}
usage:{account_id}:*
```

### Invalidación por Contexto

| Operación | Cachés Invalidadas |
|-----------|-------------------|
| Crear interacción | Recomendaciones del usuario específico |
| Actualizar producto | Todas las recomendaciones de la cuenta |
| Entrenar modelo | Modelos, métricas y recomendaciones |
| Cambiar suscripción | Usage meter y límites |

## Beneficios de la Estrategia Centralizada

### 1. Consistencia
- **Invalidación coordinada**: Todas las cachés relacionadas se invalidan juntas
- **Atomicidad**: Operaciones de invalidación agrupadas lógicamente

### 2. Mantenibilidad
- **Punto único de verdad**: Toda la lógica de invalidación en un lugar
- **Fácil evolución**: Agregar nuevos tipos de caché es straightforward

### 3. Rendimiento
- **Invalidación selectiva**: Solo se invalida lo necesario
- **Operaciones batch**: Múltiples claves invalidadas en una operación

### 4. Observabilidad
- **Logging centralizado**: Todas las operaciones de caché se registran consistentemente
- **Métricas unificadas**: Facilita el monitoreo y debugging

## Migración y Compatibilidad

### Métodos Deprecated
Se mantienen métodos deprecated para compatibilidad:
```python
# Deprecated - usar invalidate_user_recommendations()
await cache_service.invalidate_user_cache(account_id, user_id)

# Deprecated - usar invalidate_account_recommendations()
await cache_service.invalidate_account_cache(account_id)
```

### Guía de Migración
1. **Identificar** servicios que invalidan caché
2. **Reemplazar** llamadas directas a Redis con llamadas al `CacheService`
3. **Usar** métodos de conveniencia apropiados
4. **Eliminar** lógica de caché dispersa
5. **Probar** que la invalidación funciona correctamente

## Configuración Redis

### Patrones de Claves Recomendados
```python
# Recomendaciones
f"recommendations:{account_id}:{user_id}:{model_type}"
f"user_factors:{account_id}:{user_id}:{factor_type}"

# Modelos
f"model:{account_id}:{model_type}:{artifact_type}"
f"metric:{account_id}:{metric_type}"

# Usage Meter
f"subscription:{account_id}"
f"usage:{account_id}:{usage_type}"
f"rate_limit:{account_id}:{time_window}"
```

## Ejemplos de Uso

### Servicio de Negocio
```python
class RecommendationService:
    async def generate_recommendations(self, account_id: int, user_id: int):
        # ... generar recomendaciones ...
        
        # Invalidar caché antigua si existe
        cache_service = await get_cache_service()
        await cache_service.invalidate_user_recommendations(account_id, user_id)
```

### Endpoint API
```python
@router.post("/interactions")
async def create_interaction(interaction_data: InteractionCreate):
    # Crear interacción usando el servicio
    interaction = await interaction_service.create_interaction(interaction_data)
    
    # El servicio ya maneja la invalidación de caché automáticamente
    return interaction
```

## Monitoreo y Debugging

### Logs
```python
# Éxito
log_info(f"Caché invalidada para user_id={user_id}, account_id={account_id}. Claves eliminadas: {deleted_count}")

# Error
log_error(f"Error invalidando caché: {str(e)}")

# Advertencia para métodos deprecated
log_warning("invalidate_user_cache está deprecated. Use invalidate_user_recommendations en su lugar.")
```

### Métricas Recomendadas
- Número de invalidaciones por tipo
- Tiempo de respuesta de invalidaciones
- Errores de invalidación
- Tasa de hits/miss después de invalidaciones

## Consideraciones de Rendimiento

### Optimizaciones Implementadas
1. **Batch operations**: Múltiples patrones invalidados en paralelo
2. **Conexión singleton**: Reutilización de conexiones Redis
3. **Error isolation**: Errores de caché no afectan operaciones principales
4. **Logging eficiente**: Solo logs necesarios para debugging

### Recomendaciones
- Monitorear impacto en latencia de operaciones
- Considerar invalidación asíncrona para operaciones críticas
- Usar TTL apropiados para reducir necesidad de invalidación manual

## Próximos Pasos

1. **Migrar servicios restantes** al nuevo patrón
2. **Eliminar código legacy** de invalidación dispersa
3. **Implementar métricas** para monitoreo
4. **Optimizar patrones de caché** basado en uso real
5. **Documentar patrones específicos** por dominio de negocio 
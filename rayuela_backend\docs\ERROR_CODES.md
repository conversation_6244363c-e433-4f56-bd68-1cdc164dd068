# Códigos de Error de la API

Este documento describe los códigos de error estandarizados que puede devolver la API de Rayuela. Estos códigos permiten a los desarrolladores manejar errores específicos de forma programática, sin depender del mensaje de error que podría cambiar.

## Formato de Respuesta de Error

Todas las respuestas de error siguen un formato consistente:

```json
{
  "message": "Descripción del error",
  "error_code": "CÓDIGO_DE_ERROR",
  "request_id": "uuid-único-de-la-solicitud",
  "timestamp": 1640123456,
  "path": "/api/v1/ruta/del/endpoint",
  "retry_after": 60
}
```

### Campos de la Respuesta de Error

- `message`: Descripción legible del error en español
- `error_code`: Código de error estandarizado (ver lista a continuación)
- `request_id`: UUID único de la solicitud para debugging y soporte
- `timestamp`: Timestamp UNIX del momento del error
- `path`: Ruta del endpoint que generó el error
- `retry_after`: (Solo en errores 429) Segundos antes de poder reintentar

### Headers de Respuesta

- `X-Request-ID`: Mismo UUID que aparece en el cuerpo de la respuesta
- `Retry-After`: (Solo en errores 429) Segundos antes de poder reintentar

## Códigos de Error Detallados

### Errores de Autenticación y Autorización (401, 403)

#### INVALID_CREDENTIALS
- **Descripción**: Las credenciales proporcionadas (usuario/contraseña) son incorrectas
- **HTTP Status**: 401
- **Causas Comunes**:
  - Email o contraseña incorrectos
  - Cuenta desactivada o suspendida
  - Error de tipeo en las credenciales
- **Pasos de Resolución**:
  1. Verificar que el email sea correcto
  2. Verificar que la contraseña sea correcta
  3. Intentar resetear la contraseña si es necesario
  4. Contactar soporte si la cuenta está suspendida

#### INVALID_TOKEN
- **Descripción**: Token JWT inválido, malformado o expirado
- **HTTP Status**: 401
- **Causas Comunes**:
  - Token expirado (TTL: 24 horas)
  - Token malformado o corrupto
  - Token revocado por cambio de contraseña
- **Pasos de Resolución**:
  1. Verificar que el token no haya expirado
  2. Autenticarse nuevamente para obtener un token fresco
  3. Verificar que el token se incluya correctamente en el header `Authorization: Bearer <token>`

#### INVALID_API_KEY
- **Descripción**: API Key inválida, malformada o no proporcionada
- **HTTP Status**: 401
- **Causas Comunes**:
  - API Key no proporcionada en el header
  - API Key incorrecta o desactivada
  - API Key pertenece a otra cuenta
- **Pasos de Resolución**:
  1. Verificar que la API Key se incluya en el header `X-API-Key`
  2. Verificar que la API Key sea correcta
  3. Generar una nueva API Key si es necesario
  4. Verificar que la API Key pertenezca a la cuenta correcta

#### INSUFFICIENT_PERMISSIONS
- **Descripción**: Permisos insuficientes para ejecutar la acción solicitada
- **HTTP Status**: 403
- **Causas Comunes**:
  - Intento de acceder a recursos de otra cuenta
  - Funcionalidad restringida por plan de suscripción
  - Rol de usuario insuficiente
- **Pasos de Resolución**:
  1. Verificar que esté accediendo a recursos de su propia cuenta
  2. Revisar las limitaciones de su plan de suscripción
  3. Contactar al administrador de la cuenta para permisos adicionales

#### EMAIL_NOT_VERIFIED
- **Descripción**: El email de la cuenta no ha sido verificado
- **HTTP Status**: 401
- **Causas Comunes**:
  - Email no verificado después del registro
  - Link de verificación expirado
- **Pasos de Resolución**:
  1. Revisar la bandeja de entrada y spam
  2. Solicitar un nuevo email de verificación
  3. Verificar que el email sea correcto en el perfil

### Errores de Recursos No Encontrados (404)

#### ACCOUNT_NOT_FOUND
- **Descripción**: La cuenta especificada no existe
- **HTTP Status**: 404
- **Causas Comunes**:
  - ID de cuenta incorrecto
  - Cuenta eliminada o suspendida
  - Error en la URL o parámetros
- **Pasos de Resolución**:
  1. Verificar el ID de cuenta
  2. Verificar que la cuenta no haya sido eliminada
  3. Contactar soporte si la cuenta debería existir

#### MODEL_NOT_FOUND
- **Descripción**: El modelo especificado no existe o no está entrenado
- **HTTP Status**: 404
- **Causas Comunes**:
  - Modelo nunca entrenado
  - ID de modelo incorrecto
  - Modelo eliminado
- **Pasos de Resolución**:
  1. Verificar que el modelo haya sido entrenado
  2. Listar modelos disponibles con `GET /api/v1/pipeline/models`
  3. Entrenar un nuevo modelo si es necesario

#### PRODUCT_NOT_FOUND
- **Descripción**: El producto especificado no existe en el catálogo
- **HTTP Status**: 404
- **Causas Comunes**:
  - ID de producto incorrecto
  - Producto eliminado del catálogo
  - Producto no pertenece a la cuenta
- **Pasos de Resolución**:
  1. Verificar el ID del producto
  2. Verificar que el producto exista en el catálogo
  3. Verificar que el producto pertenezca a su cuenta

### Errores de Validación y Conflictos (400, 409, 422)

#### VALIDATION_ERROR
- **Descripción**: Los datos proporcionados no pasan la validación
- **HTTP Status**: 422
- **Causas Comunes**:
  - Campos requeridos faltantes
  - Formato de datos incorrecto
  - Valores fuera de rango permitido
- **Pasos de Resolución**:
  1. Revisar la estructura de datos requerida en la documentación
  2. Verificar que todos los campos requeridos estén presentes
  3. Verificar tipos de datos y formatos
  4. Revisar limitaciones de longitud y valores

#### DUPLICATE_ENTRY
- **Descripción**: Intento de crear un recurso que ya existe
- **HTTP Status**: 409
- **Causas Comunes**:
  - Email ya registrado
  - Nombre de producto duplicado
  - ID único ya en uso
- **Pasos de Resolución**:
  1. Verificar que el recurso no exista ya
  2. Usar un identificador único diferente
  3. Actualizar el recurso existente en lugar de crear uno nuevo

### Errores de Límites (429)

#### RATE_LIMIT_EXCEEDED
- **Descripción**: Demasiadas solicitudes en un periodo de tiempo
- **HTTP Status**: 429
- **Headers**: `Retry-After: 60`
- **Causas Comunes**:
  - Demasiadas solicitudes simultáneas
  - Límite de tasa por minuto/hora excedido
  - Uso anormal de la API
- **Pasos de Resolución**:
  1. Esperar el tiempo indicado en `retry_after`
  2. Implementar rate limiting en el cliente
  3. Reducir la frecuencia de solicitudes
  4. Considerar actualizar plan si es necesario

#### RESOURCE_LIMIT_EXCEEDED
- **Descripción**: Límite de recursos del plan de suscripción excedido
- **HTTP Status**: 429
- **Headers**: `Retry-After: 3600`
- **Causas Comunes**:
  - Límite mensual de API calls alcanzado
  - Límite de productos en catálogo
  - Límite de modelos entrenados
- **Pasos de Resolución**:
  1. Revisar el uso actual en el dashboard
  2. Esperar al siguiente ciclo de facturación
  3. Considerar actualizar el plan de suscripción
  4. Optimizar el uso de la API

#### TRAINING_FREQUENCY_LIMIT
- **Descripción**: Límite de frecuencia de entrenamiento excedido
- **HTTP Status**: 429
- **Headers**: `Retry-After: 86400`
- **Causas Comunes**:
  - Intentar entrenar más frecuentemente que lo permitido por el plan
  - Múltiples entrenamientos en paralelo
- **Pasos de Resolución**:
  1. Esperar el tiempo especificado en el plan (ej. 24 horas)
  2. Revisar la frecuencia de entrenamiento permitida
  3. Considerar actualizar el plan para entrenamientos más frecuentes

### Errores de Recomendación (500)

#### MODEL_NOT_TRAINED
- **Descripción**: Intento de obtener recomendaciones sin modelo entrenado
- **HTTP Status**: 500
- **Causas Comunes**:
  - Cuenta nueva sin entrenamientos
  - Modelo en entrenamiento
  - Error durante el entrenamiento
- **Pasos de Resolución**:
  1. Verificar el estado del entrenamiento con `GET /api/v1/pipeline/status`
  2. Iniciar un entrenamiento con `POST /api/v1/pipeline/train`
  3. Esperar a que el entrenamiento se complete

#### TRAINING_ERROR
- **Descripción**: Error durante el proceso de entrenamiento
- **HTTP Status**: 500
- **Causas Comunes**:
  - Datos de entrenamiento insuficientes
  - Datos de entrenamiento malformados
  - Error interno del algoritmo
- **Pasos de Resolución**:
  1. Verificar que haya suficientes datos de interacción
  2. Verificar la calidad de los datos de productos
  3. Contactar soporte con el `request_id` para diagnosticar

### Errores de Sistema (500)

#### INTERNAL_ERROR
- **Descripción**: Error interno del servidor
- **HTTP Status**: 500
- **Causas Comunes**:
  - Error de base de datos
  - Error de configuración
  - Error de infraestructura
- **Pasos de Resolución**:
  1. Reintentar la solicitud después de unos minutos
  2. Verificar el estado del servicio en la página de status
  3. Contactar soporte con el `request_id` si persiste

## Ejemplos de Manejo de Errores

### Ejemplo en JavaScript/TypeScript

```javascript
interface ErrorResponse {
  message: string;
  error_code: string;
  request_id: string;
  timestamp: number;
  path: string;
  retry_after?: number;
}

async function fetchRecommendations(userId: string): Promise<any> {
  try {
    const response = await fetch(`/api/v1/recommendations/${userId}`, {
      headers: {
        'X-API-Key': 'your-api-key',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const error: ErrorResponse = await response.json();
      
      switch(error.error_code) {
        case 'INVALID_API_KEY':
          console.error('API Key inválida, verifica tu configuración');
          throw new Error('Authentication failed');
          
        case 'MODEL_NOT_TRAINED':
          console.warn('Modelo no entrenado, mostrando recomendaciones populares');
          return await fetchPopularItems();
          
        case 'RATE_LIMIT_EXCEEDED':
          const retryAfter = error.retry_after || 60;
          console.warn(`Rate limit excedido, reintentando en ${retryAfter} segundos`);
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          return await fetchRecommendations(userId);
          
        case 'RESOURCE_LIMIT_EXCEEDED':
          console.error('Límite de plan excedido, considera actualizar');
          throw new Error('Plan limit exceeded');
          
        case 'INTERNAL_ERROR':
          console.error(`Error interno del servidor. Request ID: ${error.request_id}`);
          throw new Error(`Server error (${error.request_id})`);
          
        default:
          console.error(`Error desconocido: ${error.message} (${error.request_id})`);
          throw new Error(error.message);
      }
    }

    return await response.json();
  } catch (error) {
    console.error('Error de red:', error);
    throw error;
  }
}
```

### Ejemplo en Python

```python
import requests
import time
from typing import Optional, Dict, Any

class RayuelaAPIError(Exception):
    """Excepción base para errores de la API de Rayuela."""
    def __init__(self, message: str, error_code: str, request_id: str):
        self.message = message
        self.error_code = error_code
        self.request_id = request_id
        super().__init__(f"{error_code}: {message} (Request ID: {request_id})")

class RayuelaClient:
    def __init__(self, api_key: str, base_url: str = "https://api.rayuela.ai"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        })
    
    def _handle_error_response(self, response: requests.Response) -> None:
        """Maneja respuestas de error de la API."""
        try:
            error_data = response.json()
        except ValueError:
            error_data = {"message": "Error desconocido", "error_code": "UNKNOWN_ERROR"}
        
        error_code = error_data.get("error_code", "UNKNOWN_ERROR")
        message = error_data.get("message", "Error desconocido")
        request_id = error_data.get("request_id", "unknown")
        
        if error_code == "RATE_LIMIT_EXCEEDED":
            retry_after = error_data.get("retry_after", 60)
            print(f"Rate limit excedido, esperando {retry_after} segundos...")
            time.sleep(retry_after)
            return  # Permitir reintentar
        
        elif error_code == "MODEL_NOT_TRAINED":
            print("Modelo no entrenado, considera entrenar uno primero")
        
        elif error_code == "INVALID_API_KEY":
            print("API Key inválida, verifica tu configuración")
        
        elif error_code == "INTERNAL_ERROR":
            print(f"Error interno del servidor. Contacta soporte con ID: {request_id}")
        
        raise RayuelaAPIError(message, error_code, request_id)
    
    def get_recommendations(self, user_id: str, limit: int = 10) -> Dict[str, Any]:
        """Obtiene recomendaciones para un usuario."""
        url = f"{self.base_url}/api/v1/recommendations/{user_id}"
        params = {"limit": limit}
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, params=params)
                
                if response.status_code == 200:
                    return response.json()
                
                elif response.status_code == 429:  # Rate limit
                    if attempt < max_retries - 1:
                        self._handle_error_response(response)
                        continue  # Reintentar después del sleep
                    else:
                        self._handle_error_response(response)
                
                else:
                    self._handle_error_response(response)
                    
            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    print(f"Error de conexión, reintentando... ({attempt + 1}/{max_retries})")
                    time.sleep(2 ** attempt)  # Backoff exponencial
                    continue
                else:
                    raise Exception(f"Error de conexión después de {max_retries} intentos: {e}")
        
        # Este punto no debería alcanzarse
        raise Exception("Error inesperado en get_recommendations")

# Ejemplo de uso
if __name__ == "__main__":
    client = RayuelaClient("your-api-key")
    
    try:
        recommendations = client.get_recommendations("user123", limit=5)
        print("Recomendaciones:", recommendations)
    except RayuelaAPIError as e:
        print(f"Error de API: {e}")
    except Exception as e:
        print(f"Error inesperado: {e}")
```

## Debugging y Soporte

### Usando Request IDs

Cada solicitud incluye un `request_id` único que permite rastrear la solicitud específica en los logs del servidor. Esto es especialmente útil para:

- Reportar errores al equipo de soporte
- Debugging de problemas específicos
- Correlacionar eventos en logs

**Ejemplo de reporte de error a soporte:**
```
Error Code: TRAINING_ERROR
Request ID: f47ac10b-58cc-4372-a567-0e02b2c3d479
Timestamp: 2024-01-15 14:30:22
Descripción: El entrenamiento falló con datos aparentemente válidos
```

### Headers de Debugging

Incluye estos headers en tus solicitudes para facilitar el debugging:

```
X-Request-ID: tu-request-id-personalizado  # Opcional
X-Client-Name: mi-aplicacion
X-Client-Version: 1.2.3
```

### Logs de Cliente Recomendados

Para facilitar el debugging, incluye en tus logs:

```javascript
console.log('API Request', {
  endpoint: '/api/v1/recommendations/user123',
  request_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  timestamp: new Date().toISOString(),
  user_agent: navigator.userAgent
});
```

## Códigos de Estado HTTP

| Status Code | Significado | Cuándo Ocurre |
|-------------|-------------|---------------|
| 200 | OK | Solicitud exitosa |
| 201 | Created | Recurso creado exitosamente |
| 400 | Bad Request | Datos de entrada inválidos |
| 401 | Unauthorized | Autenticación requerida o fallida |
| 403 | Forbidden | Acceso denegado |
| 404 | Not Found | Recurso no encontrado |
| 409 | Conflict | Conflicto con recurso existente |
| 422 | Unprocessable Entity | Error de validación |
| 429 | Too Many Requests | Rate limit excedido |
| 500 | Internal Server Error | Error interno del servidor |

## Mejores Prácticas

### Para Desarrolladores

1. **Siempre verifica el `error_code`** en lugar del mensaje de error
2. **Implementa rate limiting** en tu cliente para evitar errores 429
3. **Usa exponential backoff** para reintentos
4. **Logga el `request_id`** para debugging
5. **Maneja los errores específicos** de tu dominio

### Ejemplo de Rate Limiting en Cliente

```javascript
class RateLimiter {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }
  
  async acquire() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return this.acquire();
    }
    
    this.requests.push(now);
  }
}

const rateLimiter = new RateLimiter(10, 60000); // 10 requests per minute

async function makeAPICall(url, options) {
  await rateLimiter.acquire();
  return fetch(url, options);
}
```

## Conclusión

Esta guía proporciona toda la información necesaria para manejar errores de la API de Rayuela de forma efectiva. Recuerda siempre:

- Usar los códigos de error para lógica programática
- Implementar manejo de reintentos para errores 429
- Loggear request_ids para debugging
- Implementar fallbacks apropiados para tu aplicación

Para más información o problemas específicos, contacta al equipo de soporte incluyendo el `request_id` y el `error_code` específico.

#!/usr/bin/env python3

# Ultra-simple main.py for debugging
import os
from fastapi import FastAPI

# Create app without any complex imports
app = FastAPI(
    title="Rayuela Debug",
    version="debug",
    docs_url="/api/docs",
    openapi_url="/api/openapi.json"
)

@app.get("/")
async def root():
    return {"message": "Rayuela Debug Mode", "status": "running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "mode": "debug",
        "env": os.getenv("ENV", "unknown"),
        "skip_secrets": os.getenv("SKIP_SECRETS", "false"),
        "skip_migrations": os.getenv("SKIP_MIGRATIONS", "false")
    }

@app.get("/test")
async def test():
    return {"test": "ok", "message": "Simple endpoint working"}

# Minimal OpenAPI routes for frontend compatibility
@app.get("/api/v1/health")
async def api_health():
    return {"status": "healthy", "api_version": "v1", "mode": "simple"}

# No lifespan, no complex imports, no middleware

# Auto-start server when imported or executed
import uvicorn
import threading

def start_server():
    port = int(os.getenv("PORT", "8080"))
    uvicorn.run(app, host="0.0.0.0", port=port)

if __name__ == "__main__":
    start_server()
else:
    # If imported, start server in background thread
    port = int(os.getenv("PORT", "8080"))
    uvicorn.run(app, host="0.0.0.0", port=port) 
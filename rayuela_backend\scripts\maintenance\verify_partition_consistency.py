#!/usr/bin/env python3
"""
Script para verificar que todos los modelos tenant-scoped 
tienen configurado correctamente postgresql_partition_by.

Este script:
1. Inspecciona todos los modelos de SQLAlchemy
2. Identifica cuáles deberían tener particionamiento (heredan de TenantMixin o están en TENANT_SCOPED_TABLES)  
3. Verifica que tengan postgresql_partition_by configurado
4. Reporta inconsistencias
"""

import sys
import os
from typing import List, Tuple

# Add the project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from sqlalchemy import inspect
from sqlalchemy.exc import SQLAlchemyError
from src.db.base import Base
from src.db.models.mixins import TenantMixin, ACCOUNT_RANGE

# Lista de tablas que deben tener particionamiento según la migración RLS
TENANT_SCOPED_TABLES = [
    "products",
    "end_users", 
    "interactions",
    "searches",
    "recommendations",
    "artifact_metadata",
    "model_metrics", 
    "training_jobs",
    "batch_ingestion_jobs",
    "training_metrics",
    "system_users",
    "system_user_roles",
    "audit_logs",
    "notifications",
    "account_usage_metrics",
    "subscriptions",
    "endpoint_metrics",
    "api_keys",
    "orders",
    "order_items",
    "roles",
    "permissions",
    "role_permissions",
]

def check_model_partition_config(model_class) -> Tuple[bool, str]:
    """
    Verifica si un modelo tiene configurado correctamente postgresql_partition_by.
    
    Returns:
        Tuple[bool, str]: (tiene_configuracion_correcta, mensaje_detalle)
    """
    table_name = model_class.__tablename__
    table_args = getattr(model_class, '__table_args__', None)
    
    if table_args is None:
        return False, f"No tiene __table_args__ definido"
    
    # Buscar postgresql_partition_by en __table_args__
    partition_config = None
    
    if isinstance(table_args, tuple):
        for arg in table_args:
            if isinstance(arg, dict) and 'postgresql_partition_by' in arg:
                partition_config = arg['postgresql_partition_by']
                break
    elif isinstance(table_args, dict) and 'postgresql_partition_by' in table_args:
        partition_config = table_args['postgresql_partition_by']
    
    if partition_config is None:
        return False, f"No tiene postgresql_partition_by configurado"
    
    if partition_config != ACCOUNT_RANGE:
        return False, f"postgresql_partition_by configurado incorrectamente: '{partition_config}' (esperado: '{ACCOUNT_RANGE}')"
    
    return True, f"Configuración correcta: {partition_config}"

def inherits_from_tenant_mixin(model_class) -> bool:
    """Verifica si un modelo hereda de TenantMixin."""
    return issubclass(model_class, TenantMixin)

def has_account_id_column(model_class) -> bool:
    """Verifica si un modelo tiene una columna account_id."""
    return hasattr(model_class, 'account_id')

def main():
    """Función principal del script de verificación."""
    print("🔍 Verificando consistencia de particionamiento en modelos...")
    print("=" * 80)
    
    # Importar todos los modelos
    try:
        from src.db import models  # Esto debería importar todos los modelos
    except ImportError as e:
        print(f"❌ Error importando modelos: {e}")
        return 1
    
    inconsistencies = []
    verified_models = []
    
    # Iterar sobre todos los modelos registrados en Base
    for model_class in Base.registry._class_registry.values():
        if not hasattr(model_class, '__tablename__'):
            continue
            
        table_name = model_class.__tablename__
        
        # Determinar si debería tener particionamiento
        should_be_partitioned = (
            table_name in TENANT_SCOPED_TABLES or
            inherits_from_tenant_mixin(model_class) or
            has_account_id_column(model_class)
        )
        
        if not should_be_partitioned:
            continue
        
        # Verificar configuración de particionamiento
        has_correct_config, detail_msg = check_model_partition_config(model_class)
        
        model_info = {
            'model': model_class.__name__,
            'table': table_name,
            'inherits_tenant_mixin': inherits_from_tenant_mixin(model_class),
            'has_account_id': has_account_id_column(model_class),
            'in_tenant_scoped_list': table_name in TENANT_SCOPED_TABLES,
            'has_correct_config': has_correct_config,
            'detail': detail_msg
        }
        
        if has_correct_config:
            verified_models.append(model_info)
        else:
            inconsistencies.append(model_info)
    
    # Reportar resultados
    print(f"✅ Modelos con configuración correcta: {len(verified_models)}")
    for model in verified_models:
        print(f"   • {model['model']} ({model['table']}): {model['detail']}")
    
    print(f"\n❌ Modelos con inconsistencias: {len(inconsistencies)}")
    for model in inconsistencies:
        print(f"   • {model['model']} ({model['table']})")
        print(f"     - Hereda TenantMixin: {model['inherits_tenant_mixin']}")
        print(f"     - Tiene account_id: {model['has_account_id']}")
        print(f"     - En TENANT_SCOPED_TABLES: {model['in_tenant_scoped_list']}")
        print(f"     - Problema: {model['detail']}")
        print()
    
    print("=" * 80)
    
    if inconsistencies:
        print(f"❌ Se encontraron {len(inconsistencies)} inconsistencias en particionamiento")
        return 1
    else:
        print("✅ Todos los modelos tenant-scoped tienen configuración de particionamiento consistente")
        return 0

if __name__ == "__main__":
    sys.exit(main()) 
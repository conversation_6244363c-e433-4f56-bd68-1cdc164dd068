#!/usr/bin/env python3
"""
Script para hacer backfill de external_id en productos existentes.

Este script actualiza productos que no tienen external_id asignado,
usando una estrategia de generación de IDs únicos basada en:
1. product_id interno (por compatibilidad)
2. UUID v4 (para evitar colisiones)
3. Timestamp (para unicidad adicional)

Uso:
    python backfill_product_external_ids.py [--dry-run] [--batch-size=1000]
"""

import asyncio
import argparse
import sys
import uuid
from datetime import datetime
from typing import List, Optional

import asyncpg
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Configuración de la base de datos
DATABASE_URL = "postgresql+asyncpg://user:password@localhost/rayuela_db"  # Ajustar según configuración


class ProductExternalIdBackfiller:
    """Clase para hacer backfill de external_id en productos."""

    def __init__(self, database_url: str):
        """
        Inicializar el backfiller.

        Args:
            database_url: URL de conexión a la base de datos
        """
        self.engine = create_async_engine(database_url)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

    async def get_products_without_external_id(
        self, batch_size: int = 1000, offset: int = 0
    ) -> List[dict]:
        """
        Obtener productos que no tienen external_id.

        Args:
            batch_size: Tamaño del lote
            offset: Desplazamiento para paginación

        Returns:
            Lista de diccionarios con datos de productos
        """
        async with self.async_session() as session:
            query = text("""
                SELECT product_id, account_id, name, created_at
                FROM products 
                WHERE external_id IS NULL OR external_id = ''
                ORDER BY account_id, product_id
                LIMIT :limit OFFSET :offset
            """)
            
            result = await session.execute(
                query, {"limit": batch_size, "offset": offset}
            )
            
            return [
                {
                    "product_id": row.product_id,
                    "account_id": row.account_id,
                    "name": row.name,
                    "created_at": row.created_at,
                }
                for row in result.fetchall()
            ]

    def generate_external_id(
        self, product_id: int, account_id: int, name: str, strategy: str = "uuid"
    ) -> str:
        """
        Generar un external_id único para un producto.

        Args:
            product_id: ID interno del producto
            account_id: ID de la cuenta
            name: Nombre del producto
            strategy: Estrategia de generación ('uuid', 'prefixed', 'legacy')

        Returns:
            external_id generado
        """
        if strategy == "uuid":
            # Estrategia UUID: Más único, pero menos legible
            return f"PROD_{uuid.uuid4().hex[:12].upper()}"
        
        elif strategy == "prefixed":
            # Estrategia prefijo + product_id: Más legible, compatible hacia atrás
            return f"PROD_{account_id}_{product_id}"
        
        elif strategy == "legacy":
            # Estrategia legacy: Usar product_id directamente (solo para compatibilidad)
            return f"{product_id}"
        
        else:
            raise ValueError(f"Estrategia desconocida: {strategy}")

    async def check_external_id_conflicts(
        self, external_id: str, account_id: int
    ) -> bool:
        """
        Verificar si un external_id ya existe para una cuenta.

        Args:
            external_id: ID externo a verificar
            account_id: ID de la cuenta

        Returns:
            True si existe conflicto, False si no
        """
        async with self.async_session() as session:
            query = text("""
                SELECT COUNT(*) as count
                FROM products 
                WHERE external_id = :external_id AND account_id = :account_id
            """)
            
            result = await session.execute(
                query, {"external_id": external_id, "account_id": account_id}
            )
            
            count = result.scalar()
            return count > 0

    async def update_product_external_id(
        self, product_id: int, account_id: int, external_id: str, dry_run: bool = False
    ) -> bool:
        """
        Actualizar el external_id de un producto.

        Args:
            product_id: ID interno del producto
            account_id: ID de la cuenta
            external_id: Nuevo external_id
            dry_run: Si True, solo simula la operación

        Returns:
            True si la actualización fue exitosa
        """
        if dry_run:
            print(f"[DRY RUN] Actualizaría producto {product_id} (cuenta {account_id}) con external_id: {external_id}")
            return True

        try:
            async with self.async_session() as session:
                async with session.begin():
                    query = text("""
                        UPDATE products 
                        SET external_id = :external_id, updated_at = NOW()
                        WHERE product_id = :product_id AND account_id = :account_id
                    """)
                    
                    result = await session.execute(query, {
                        "external_id": external_id,
                        "product_id": product_id,
                        "account_id": account_id
                    })
                    
                    return result.rowcount > 0

        except Exception as e:
            print(f"Error actualizando producto {product_id}: {e}")
            return False

    async def run_backfill(
        self, batch_size: int = 1000, strategy: str = "uuid", dry_run: bool = False
    ) -> dict:
        """
        Ejecutar el backfill completo.

        Args:
            batch_size: Tamaño del lote
            strategy: Estrategia de generación
            dry_run: Si True, solo simula las operaciones

        Returns:
            Estadísticas totales de la operación
        """
        print(f"Iniciando backfill de external_id para productos...")
        print(f"Estrategia: {strategy}")
        print(f"Tamaño de lote: {batch_size}")
        print(f"Modo: {'DRY RUN' if dry_run else 'EJECUCIÓN REAL'}")
        print("-" * 50)

        total_stats = {
            "processed": 0,
            "updated": 0,
            "conflicts": 0,
            "errors": 0
        }

        offset = 0
        batch_num = 1

        while True:
            # Obtener lote de productos
            products = await self.get_products_without_external_id(batch_size, offset)
            
            if not products:
                print("No hay más productos para procesar.")
                break

            print(f"Procesando lote {batch_num} ({len(products)} productos)...")
            
            # Procesar cada producto en el lote
            for product in products:
                total_stats["processed"] += 1
                
                # Generar external_id
                external_id = self.generate_external_id(
                    product["product_id"],
                    product["account_id"],
                    product["name"],
                    strategy
                )
                
                # Verificar conflictos
                max_attempts = 3
                attempt = 0
                
                while attempt < max_attempts:
                    conflict = await self.check_external_id_conflicts(
                        external_id, product["account_id"]
                    )
                    
                    if not conflict:
                        break
                    
                    # Si hay conflicto, generar un nuevo ID
                    attempt += 1
                    if strategy == "uuid":
                        external_id = self.generate_external_id(
                            product["product_id"],
                            product["account_id"],
                            product["name"],
                            strategy
                        )
                    else:
                        # Para estrategias no-UUID, agregar timestamp
                        timestamp = int(datetime.now().timestamp())
                        external_id = f"{external_id}_{timestamp}"
                
                if attempt >= max_attempts:
                    total_stats["conflicts"] += 1
                    print(f"No se pudo resolver conflicto para producto {product['product_id']}")
                    continue
                
                # Actualizar producto
                success = await self.update_product_external_id(
                    product["product_id"],
                    product["account_id"],
                    external_id,
                    dry_run
                )
                
                if success:
                    total_stats["updated"] += 1
                else:
                    total_stats["errors"] += 1
            
            print(f"Lote {batch_num} completado.")
            
            offset += batch_size
            batch_num += 1

        print("-" * 50)
        print(f"Backfill completado. Estadísticas totales: {total_stats}")
        
        return total_stats

    async def close(self):
        """Cerrar conexiones."""
        await self.engine.dispose()


async def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Backfill external_id para productos")
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Simular operaciones sin hacer cambios reales"
    )
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=1000, 
        help="Tamaño del lote (default: 1000)"
    )
    parser.add_argument(
        "--strategy", 
        choices=["uuid", "prefixed", "legacy"], 
        default="uuid",
        help="Estrategia de generación de external_id (default: uuid)"
    )
    parser.add_argument(
        "--database-url", 
        default=DATABASE_URL,
        help="URL de conexión a la base de datos"
    )

    args = parser.parse_args()

    # Validar argumentos
    if args.batch_size <= 0:
        print("Error: batch-size debe ser mayor que 0")
        sys.exit(1)

    try:
        # Ejecutar backfill
        backfiller = ProductExternalIdBackfiller(args.database_url)
        
        stats = await backfiller.run_backfill(
            batch_size=args.batch_size,
            strategy=args.strategy,
            dry_run=args.dry_run
        )
        
        await backfiller.close()

        # Mostrar resumen
        print("\n" + "=" * 50)
        print("RESUMEN DE BACKFILL")
        print("=" * 50)
        print(f"Productos procesados: {stats['processed']}")
        print(f"Productos actualizados: {stats['updated']}")
        print(f"Conflictos encontrados: {stats['conflicts']}")
        print(f"Errores: {stats['errors']}")
        
        if stats['errors'] > 0:
            print("\n⚠️  Se encontraron errores durante el proceso.")
            sys.exit(1)
        elif stats['conflicts'] > 0:
            print("\n⚠️  Se encontraron conflictos que no se pudieron resolver.")
            sys.exit(1)
        else:
            print("\n✅ Backfill completado exitosamente.")

    except Exception as e:
        print(f"Error durante el backfill: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python3
"""
Script de emergencia para rollback de configuración de timeouts.

USAR SOLO EN EMERGENCIAS cuando la nueva configuración de timeouts
esté causando problemas en producción.

Este script:
1. Hace backup de la configuración actual
2. Restaura temporalmente la configuración anterior (timeouts=0)
3. Proporciona instrucciones para el rollback permanente

⚠️  ADVERTENCIA: Esto restaura la configuración INSEGURA original.
Solo usar como medida temporal mientras se resuelve el problema.

Uso:
    python scripts/migrations/emergency_timeout_rollback.py [--confirm]
"""

import os
import sys
import shutil
from datetime import datetime
from pathlib import Path

# Añadir src al PYTHONPATH
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent.parent


def create_backup():
    """Crea backup del archivo env.py actual."""
    env_file = project_root / "alembic" / "env.py"
    
    if not env_file.exists():
        print("❌ Error: archivo alembic/env.py no encontrado")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = project_root / "alembic" / f"env.py.backup.{timestamp}"
    
    shutil.copy2(env_file, backup_file)
    print(f"✅ Backup creado: {backup_file}")
    
    return backup_file


def generate_emergency_config():
    """Genera la configuración de emergencia (INSEGURA)."""
    return '''import asyncio
from logging.config import fileConfig
import os
import sys

from sqlalchemy import pool, engine_from_config
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context


# --- Añadir src al PYTHONPATH ---
# Esto permite a Alembic encontrar tus modelos y configuración
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # Sube un nivel desde alembic/
sys.path.insert(0, project_root)
# ---------------------------------

# --- Importar tu Base y configuración ---
from src.db.base import Base  # Asegúrate que Base esté definida aquí
from src.core.config import settings  # Importa tu configuración

# Importa tus modelos para que Base.metadata los conozca
# Es crucial importar TODOS los archivos de modelos aquí
from src.db import models  # O importa específicamente cada modelo

# ---------------------------------------

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# --- Configurar la URL de la base de datos desde settings ---
# Sobrescribe la URL del .ini con la de tu objeto settings
config.set_main_option("sqlalchemy.url", settings.database_url)
# ----------------------------------------------------------

# ⚠️  CONFIGURACIÓN DE EMERGENCIA - TIMEOUTS DESACTIVADOS ⚠️ 
# ESTA CONFIGURACIÓN ES INSEGURA Y TEMPORAL
# PUEDE CAUSAR BLOQUEOS INDEFINIDOS EN PRODUCCIÓN
print("🚨 ADVERTENCIA: Usando configuración de timeouts de emergencia")
print("🚨 Esta configuración DESACTIVA los timeouts (INSEGURO)")
print("🚨 Restaurar configuración segura lo antes posible")

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online_sync() -> None:
    """Run migrations in 'online' mode using synchronous connection.
    
    ⚠️  CONFIGURACIÓN DE EMERGENCIA: timeouts desactivados
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Override the URL to use psycopg2 instead of asyncpg for sync operations
    sync_url = settings.database_url.replace('postgresql+asyncpg://', 'postgresql://')
    configuration['sqlalchemy.url'] = sync_url
    
    # Use synchronous engine for better reliability in Cloud Build
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'options': '-c statement_timeout=0 -c lock_timeout=0 -c application_name=alembic_migration_emergency'
        }
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

async def run_migrations_online() -> None:
    """Run migrations in 'online' mode using async approach.

    ⚠️  CONFIGURACIÓN DE EMERGENCIA: timeouts desactivados
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Use simplified configuration for better reliability in Cloud Build
    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'server_settings': {
                'jit': 'off',
                'statement_timeout': '0',
                'lock_timeout': '0',
                'application_name': 'alembic_migration_emergency'
            },
            'command_timeout': 300
        }
    )

    try:
        # Create connection with extended timeout for Cloud Build environment
        connection = await asyncio.wait_for(connectable.connect(), timeout=600)
        
        try:
            await connection.run_sync(do_run_migrations)
        finally:
            await connection.close()
    except asyncio.TimeoutError:
        print("❌ Migration connection timed out after 600 seconds")
        raise
    finally:
        await connectable.dispose()


if context.is_offline_mode():
    run_migrations_offline()
else:
    # Use synchronous approach for better reliability in Cloud Build
    try:
        run_migrations_online_sync()
    except Exception as e:
        print(f"⚠️ Synchronous migration failed: {e}")
        print("🔄 Falling back to async approach...")
        asyncio.run(run_migrations_online())
'''


def apply_emergency_config():
    """Aplica la configuración de emergencia."""
    env_file = project_root / "alembic" / "env.py"
    
    emergency_config = generate_emergency_config()
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(emergency_config)
    
    print(f"✅ Configuración de emergencia aplicada a {env_file}")


def print_warning():
    """Imprime advertencias importantes."""
    print("\n" + "🚨" * 50)
    print("🚨 CONFIGURACIÓN DE EMERGENCIA APLICADA")
    print("🚨" * 50)
    print("\n⚠️  ESTA CONFIGURACIÓN ES INSEGURA:")
    print("   - Desactiva timeouts de base de datos")
    print("   - Puede causar bloqueos indefinidos")
    print("   - Solo para uso temporal de emergencia")
    
    print("\n🔧 PASOS A SEGUIR:")
    print("1. Resolver el problema que causó la necesidad del rollback")
    print("2. Restaurar la configuración segura lo antes posible")
    print("3. Ejecutar verify_timeout_configuration.py para validar")
    print("4. Monitorear cuidadosamente las migraciones")
    
    print("\n📋 PARA RESTAURAR LA CONFIGURACIÓN SEGURA:")
    print("   git checkout -- rayuela_backend/alembic/env.py")
    print("   # O restaurar desde backup:")
    
    # Listar backups disponibles
    backups = list((project_root / "alembic").glob("env.py.backup.*"))
    if backups:
        latest_backup = max(backups, key=lambda x: x.stat().st_mtime)
        print(f"   cp {latest_backup} rayuela_backend/alembic/env.py")


def main():
    """Función principal."""
    print("🚨 SCRIPT DE EMERGENCIA - ROLLBACK DE TIMEOUTS")
    print("=" * 60)
    
    if "--confirm" not in sys.argv:
        print("\n⚠️  ESTE SCRIPT RESTAURA UNA CONFIGURACIÓN INSEGURA")
        print("   Solo usar en emergencias cuando las migraciones fallen")
        print("   debido a la nueva configuración de timeouts.")
        print("\n   Para confirmar, ejecutar con --confirm")
        print("   Ejemplo: python emergency_timeout_rollback.py --confirm")
        return 1
    
    print("\n🔍 Verificando estado actual...")
    
    # Crear backup
    backup_file = create_backup()
    if not backup_file:
        return 1
    
    print("\n⚠️  Aplicando configuración de emergencia...")
    
    try:
        apply_emergency_config()
        print_warning()
        
        print(f"\n✅ Rollback de emergencia completado")
        print(f"📁 Backup guardado en: {backup_file}")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error durante el rollback: {e}")
        print("   Intentando restaurar desde backup...")
        
        try:
            env_file = project_root / "alembic" / "env.py"
            shutil.copy2(backup_file, env_file)
            print("✅ Archivo restaurado desde backup")
        except Exception as restore_error:
            print(f"❌ Error al restaurar: {restore_error}")
            print("   Restaurar manualmente desde Git o backup")
        
        return 1


if __name__ == "__main__":
    sys.exit(main()) 
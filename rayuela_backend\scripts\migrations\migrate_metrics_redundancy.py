#!/usr/bin/env python3
"""
Script para migrar y validar la eliminación de redundancia en métricas.

Este script:
1. Verifica que la migración de datos se haya completado correctamente
2. Proporciona métricas sobre el proceso de migración
3. Puede ejecutar una migración manual si es necesario
"""
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, func, text
from src.core.config import settings
from src.db.models.model_metadata import ModelMetadata
from src.db.models.model_metric import ModelMetric
from src.db.models.training_metrics import TrainingMetrics
from src.db.models.training_job import TrainingJob
from src.services.metrics_service import MetricsService
from src.utils.base_logger import log_info, log_error, log_warning


class MetricsRedundancyMigrator:
    """Clase para gestionar la migración de redundancia de métricas"""

    def __init__(self):
        self.engine = create_async_engine(
            settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
            echo=False
        )
        self.SessionLocal = sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        self.metrics_service = MetricsService()

    async def validate_migration(self) -> Dict[str, Any]:
        """
        Valida que la migración se haya completado correctamente.
        
        Returns:
            Diccionario con estadísticas de validación
        """
        async with self.SessionLocal() as db:
            try:
                # Contar registros en ModelMetadata con performance_metrics JSONB originales
                original_metrics_query = select(func.count(ModelMetadata.id)).where(
                    text("performance_metrics ? 'precision' OR performance_metrics ? 'recall' OR performance_metrics ? 'ndcg'")
                )
                result = await db.execute(original_metrics_query)
                models_with_original_metrics = result.scalar()

                # Contar registros migrados (con summary)
                migrated_metrics_query = select(func.count(ModelMetadata.id)).where(
                    text("performance_metrics ? 'detailed_metrics_in_model_metrics'")
                )
                result = await db.execute(migrated_metrics_query)
                models_with_migrated_metrics = result.scalar()

                # Contar registros en ModelMetric
                model_metrics_query = select(func.count(ModelMetric.id))
                result = await db.execute(model_metrics_query)
                total_model_metrics = result.scalar()

                # Contar registros en TrainingMetrics
                training_metrics_query = select(func.count(TrainingMetrics.id))
                result = await db.execute(training_metrics_query)
                total_training_metrics = result.scalar()

                # Contar TrainingJobs con métricas originales
                original_training_query = select(func.count(TrainingJob.id)).where(
                    text("metrics ? 'accuracy' OR metrics ? 'precision'")
                )
                result = await db.execute(original_training_query)
                training_jobs_with_original_metrics = result.scalar()

                # Contar TrainingJobs migrados
                migrated_training_query = select(func.count(TrainingJob.id)).where(
                    text("metrics ? 'detailed_metrics_in_training_metrics'")
                )
                result = await db.execute(migrated_training_query)
                training_jobs_with_migrated_metrics = result.scalar()

                validation_stats = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "model_metadata": {
                        "with_original_metrics": models_with_original_metrics,
                        "with_migrated_summary": models_with_migrated_metrics,
                        "migration_coverage": (
                            models_with_migrated_metrics / max(models_with_original_metrics + models_with_migrated_metrics, 1)
                        ) * 100
                    },
                    "model_metrics_table": {
                        "total_records": total_model_metrics
                    },
                    "training_jobs": {
                        "with_original_metrics": training_jobs_with_original_metrics,
                        "with_migrated_summary": training_jobs_with_migrated_metrics,
                        "migration_coverage": (
                            training_jobs_with_migrated_metrics / max(training_jobs_with_original_metrics + training_jobs_with_migrated_metrics, 1)
                        ) * 100
                    },
                    "training_metrics_table": {
                        "total_records": total_training_metrics
                    }
                }

                return validation_stats

            except Exception as e:
                log_error(f"Error validating migration: {str(e)}")
                return {"error": str(e)}

    async def manual_migrate_model_metrics(self, limit: int = 100) -> Dict[str, int]:
        """
        Ejecuta una migración manual de métricas de ModelMetadata a ModelMetric.
        
        Args:
            limit: Límite de registros a procesar en esta ejecución
            
        Returns:
            Estadísticas de la migración
        """
        async with self.SessionLocal() as db:
            try:
                # Buscar ModelMetadata con métricas JSONB sin migrar
                query = select(ModelMetadata).where(
                    text("""
                        performance_metrics IS NOT NULL 
                        AND performance_metrics != '{}'::jsonb
                        AND NOT (performance_metrics ? 'detailed_metrics_in_model_metrics')
                    """)
                ).limit(limit)
                
                result = await db.execute(query)
                models_to_migrate = result.scalars().all()

                migrated_count = 0
                for model in models_to_migrate:
                    if model.performance_metrics:
                        # Extraer métricas numéricas
                        numeric_metrics = {}
                        for key, value in model.performance_metrics.items():
                            try:
                                numeric_metrics[key] = float(value)
                            except (ValueError, TypeError):
                                log_warning(f"Skipping non-numeric metric {key}: {value}")

                        if numeric_metrics:
                            # Usar el servicio para guardar métricas
                            await self.metrics_service.save_model_metrics(
                                db=db,
                                account_id=model.account_id,
                                model_metadata_id=model.id,
                                metrics=numeric_metrics,
                                metric_type="offline"
                            )
                            migrated_count += 1

                log_info(f"Manual migration completed: {migrated_count} models migrated")
                return {
                    "processed": len(models_to_migrate),
                    "migrated": migrated_count,
                    "timestamp": datetime.utcnow().isoformat()
                }

            except Exception as e:
                log_error(f"Error in manual migration: {str(e)}")
                return {"error": str(e)}

    async def manual_migrate_training_metrics(self, limit: int = 100) -> Dict[str, int]:
        """
        Ejecuta una migración manual de métricas de TrainingJob a TrainingMetrics.
        
        Args:
            limit: Límite de registros a procesar en esta ejecución
            
        Returns:
            Estadísticas de la migración
        """
        async with self.SessionLocal() as db:
            try:
                # Buscar TrainingJobs con métricas JSON sin migrar
                query = select(TrainingJob).where(
                    text("""
                        metrics IS NOT NULL 
                        AND metrics != '{}'::json 
                        AND NOT (metrics ? 'detailed_metrics_in_training_metrics')
                    """)
                ).limit(limit)
                
                result = await db.execute(query)
                jobs_to_migrate = result.scalars().all()

                migrated_count = 0
                for job in jobs_to_migrate:
                    if job.metrics and job.artifact_metadata_id:
                        # Usar el servicio para guardar métricas
                        await self.metrics_service.save_training_metrics(
                            db=db,
                            account_id=job.account_id,
                            model_id=job.artifact_metadata_id,
                            metrics=job.metrics
                        )

                        # Actualizar TrainingJob con resumen
                        job.metrics = {
                            "summary_generated_at": datetime.utcnow().isoformat(),
                            "detailed_metrics_in_training_metrics": True,
                            "primary_metric": job.metrics.get("accuracy", job.metrics.get("precision", "N/A"))
                        }
                        
                        migrated_count += 1

                await db.commit()

                log_info(f"Manual training migration completed: {migrated_count} jobs migrated")
                return {
                    "processed": len(jobs_to_migrate),
                    "migrated": migrated_count,
                    "timestamp": datetime.utcnow().isoformat()
                }

            except Exception as e:
                await db.rollback()
                log_error(f"Error in manual training migration: {str(e)}")
                return {"error": str(e)}

    async def generate_migration_report(self) -> Dict[str, Any]:
        """
        Genera un reporte completo del estado de la migración.
        
        Returns:
            Reporte detallado de la migración
        """
        async with self.SessionLocal() as db:
            try:
                validation_stats = await self.validate_migration()
                
                # Obtener ejemplos de datos migrados
                model_metrics_sample_query = select(ModelMetric).limit(5)
                result = await db.execute(model_metrics_sample_query)
                model_metrics_sample = result.scalars().all()

                training_metrics_sample_query = select(TrainingMetrics).limit(5)
                result = await db.execute(training_metrics_sample_query)
                training_metrics_sample = result.scalars().all()

                # Analizar tipos de métricas más comunes
                common_metrics_query = select(
                    ModelMetric.metric_name,
                    func.count(ModelMetric.id).label("count")
                ).group_by(ModelMetric.metric_name).order_by(text("count DESC")).limit(10)
                result = await db.execute(common_metrics_query)
                common_metrics = [{"metric": row.metric_name, "count": row.count} for row in result]

                report = {
                    "migration_validation": validation_stats,
                    "sample_data": {
                        "model_metrics": [
                            {
                                "id": m.id,
                                "metric_name": m.metric_name,
                                "metric_value": m.metric_value,
                                "metric_type": m.metric_type,
                                "timestamp": m.timestamp.isoformat()
                            } for m in model_metrics_sample
                        ],
                        "training_metrics": [
                            {
                                "id": t.id,
                                "model_id": t.model_id,
                                "accuracy": t.accuracy,
                                "precision": t.precision,
                                "timestamp": t.timestamp.isoformat()
                            } for t in training_metrics_sample
                        ]
                    },
                    "analysis": {
                        "common_metrics": common_metrics
                    },
                    "recommendations": []
                }

                # Agregar recomendaciones basadas en los resultados
                if validation_stats.get("model_metadata", {}).get("migration_coverage", 0) < 90:
                    report["recommendations"].append(
                        "Consider running manual migration for remaining ModelMetadata records"
                    )

                if validation_stats.get("training_jobs", {}).get("migration_coverage", 0) < 90:
                    report["recommendations"].append(
                        "Consider running manual migration for remaining TrainingJob records"
                    )

                return report

            except Exception as e:
                log_error(f"Error generating migration report: {str(e)}")
                return {"error": str(e)}

    async def cleanup_empty_jsonb_fields(self) -> Dict[str, int]:
        """
        Limpia campos JSONB que están vacíos o solo contienen metadata de migración.
        
        Returns:
            Estadísticas de limpieza
        """
        async with self.SessionLocal() as db:
            try:
                # Limpiar performance_metrics vacíos en ModelMetadata
                empty_performance_query = text("""
                    UPDATE artifact_metadata 
                    SET performance_metrics = NULL 
                    WHERE performance_metrics = '{}'::jsonb 
                    OR (performance_metrics ? 'detailed_metrics_in_model_metrics' 
                        AND jsonb_array_length(jsonb_object_keys(performance_metrics)) <= 3)
                """)
                result = await db.execute(empty_performance_query)
                cleaned_model_metadata = result.rowcount

                # Limpiar metrics vacíos en TrainingJob
                empty_training_query = text("""
                    UPDATE training_jobs 
                    SET metrics = NULL 
                    WHERE metrics = '{}'::json 
                    OR (metrics ? 'detailed_metrics_in_training_metrics' 
                        AND json_array_length(json_object_keys(metrics)) <= 3)
                """)
                result = await db.execute(empty_training_query)
                cleaned_training_jobs = result.rowcount

                await db.commit()

                cleanup_stats = {
                    "cleaned_model_metadata": cleaned_model_metadata,
                    "cleaned_training_jobs": cleaned_training_jobs,
                    "timestamp": datetime.utcnow().isoformat()
                }

                log_info(f"Cleanup completed: {cleanup_stats}")
                return cleanup_stats

            except Exception as e:
                await db.rollback()
                log_error(f"Error during cleanup: {str(e)}")
                return {"error": str(e)}

    async def close(self):
        """Cierra las conexiones de base de datos"""
        await self.engine.dispose()


async def main():
    """Función principal del script"""
    if len(sys.argv) < 2:
        print("Usage: python migrate_metrics_redundancy.py [validate|migrate-models|migrate-training|report|cleanup]")
        sys.exit(1)

    command = sys.argv[1]
    migrator = MetricsRedundancyMigrator()

    try:
        if command == "validate":
            stats = await migrator.validate_migration()
            print("Migration Validation Results:")
            print("=" * 50)
            for key, value in stats.items():
                print(f"{key}: {value}")

        elif command == "migrate-models":
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            stats = await migrator.manual_migrate_model_metrics(limit)
            print(f"Model Metrics Migration Results: {stats}")

        elif command == "migrate-training":
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            stats = await migrator.manual_migrate_training_metrics(limit)
            print(f"Training Metrics Migration Results: {stats}")

        elif command == "report":
            report = await migrator.generate_migration_report()
            print("Migration Report:")
            print("=" * 50)
            print(report)

        elif command == "cleanup":
            stats = await migrator.cleanup_empty_jsonb_fields()
            print(f"Cleanup Results: {stats}")

        else:
            print(f"Unknown command: {command}")
            sys.exit(1)

    except Exception as e:
        log_error(f"Error executing command {command}: {str(e)}")
        sys.exit(1)

    finally:
        await migrator.close()


if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python3
"""
Script para popular la columna features de productos con datos estructurados.

Este script ayuda a migrar datos existentes de productos al nuevo formato
con características enriquecidas en la columna features JSONB.

Uso:
    python populate_product_features.py --account-id 1 --dry-run
    python populate_product_features.py --account-id 1 --apply-changes
"""

import asyncio
import json
import argparse
import sys
import os
from typing import Dict, Any, List, Optional

# Añadir el directorio raíz al path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from src.db.database import get_async_session
from src.db.models.product import Product
from src.utils.base_logger import log_info, log_error, log_warning


class ProductFeaturesPopulator:
    """Clase para popular la columna features de productos con datos estructurados."""
    
    def __init__(self, account_id: int, dry_run: bool = True):
        self.account_id = account_id
        self.dry_run = dry_run
        
    async def populate_features(self):
        """Procesa todos los productos de la cuenta y popula el campo features."""
        
        try:
            async for session in get_async_session():
                # Obtener productos sin features o con features vacías
                query = select(Product).where(
                    Product.account_id == self.account_id,
                    Product.is_active == True,
                    Product.deleted_at.is_(None)
                ).where(
                    # Productos sin features o con features vacías
                    (Product.features.is_(None)) | 
                    (Product.features == {})
                )
                
                result = await session.execute(query)
                products = result.scalars().all()
                
                log_info(f"Encontrados {len(products)} productos para procesar en cuenta {self.account_id}")
                
                if not products:
                    log_info("No hay productos para procesar")
                    return
                
                processed_count = 0
                for product in products:
                    features = await self._generate_features_for_product(product)
                    
                    if features:
                        if self.dry_run:
                            log_info(f"[DRY RUN] Producto {product.product_id}: {json.dumps(features, indent=2)}")
                        else:
                            # Actualizar el producto
                            await session.execute(
                                update(Product)
                                .where(
                                    Product.account_id == self.account_id,
                                    Product.product_id == product.product_id
                                )
                                .values(features=features)
                            )
                            log_info(f"Actualizado producto {product.product_id} con {len(features)} características")
                        
                        processed_count += 1
                
                if not self.dry_run:
                    await session.commit()
                    log_info(f"Actualizados {processed_count} productos exitosamente")
                else:
                    log_info(f"[DRY RUN] Se procesarían {processed_count} productos")
                    
        except Exception as e:
            log_error(f"Error procesando productos: {str(e)}")
            raise
    
    async def _generate_features_for_product(self, product: Product) -> Dict[str, Any]:
        """
        Genera características estructuradas para un producto basado en datos existentes.
        
        Args:
            product: Instancia del producto
            
        Returns:
            Diccionario con características estructuradas
        """
        features = {}
        
        try:
            # Características básicas derivadas de campos existentes
            if product.category:
                features["master_category"] = product.category
                features["sub_category"] = product.category  # Podría ser más específico
            
            # Generar tags basados en nombre y descripción
            features["tags"] = self._generate_tags_from_text(
                product.name, product.description
            )
            
            # Métricas de reviews si están disponibles
            if product.average_rating and product.num_ratings:
                features["review_summary"] = {
                    "adjusted_score": float(product.average_rating),
                    "final_score": float(product.average_rating),
                    "reviews_count": int(product.num_ratings)
                }
            
            # Información de inventario
            if product.inventory_count is not None:
                features["availability"] = "IN_STOCK" if product.inventory_count > 0 else "OUT_OF_STOCK"
                features["inventory_count"] = int(product.inventory_count)
            
            # Información de precio
            if product.price:
                features["price"] = float(product.price)
            
            # Generar un ejemplo de colors y brand (esto podría venir de datos reales)
            features.update(self._generate_example_attributes(product))
            
            log_info(f"Generadas {len(features)} características para producto {product.product_id}")
            return features
            
        except Exception as e:
            log_error(f"Error generando características para producto {product.product_id}: {str(e)}")
            return {}
    
    def _generate_tags_from_text(self, name: str, description: str) -> List[str]:
        """
        Genera tags a partir del nombre y descripción del producto.
        
        Args:
            name: Nombre del producto
            description: Descripción del producto
            
        Returns:
            Lista de tags generados
        """
        tags = []
        
        # Texto combinado para análisis
        text = f"{name or ''} {description or ''}".lower()
        
        # Tags comunes basados en palabras clave
        keywords_to_tags = {
            "shirt": "apparel",
            "sweatshirt": "apparel",
            "google": "google",
            "cotton": "cotton",
            "french terry": "french_terry",
            "crewneck": "crewneck",
            "hoodie": "hoodie",
            "organic": "organic",
            "unisex": "unisex",
            "men": "menswear",
            "women": "womenswear",
            "kids": "kidswear",
            "bag": "accessories",
            "bottle": "accessories",
            "tech": "technology",
            "android": "android",
            "chrome": "chrome",
        }
        
        for keyword, tag in keywords_to_tags.items():
            if keyword in text:
                tags.append(tag)
        
        # Limitar a 5 tags para evitar ruido
        return tags[:5]
    
    def _generate_example_attributes(self, product: Product) -> Dict[str, Any]:
        """
        Genera atributos de ejemplo para demostrar la funcionalidad.
        En un caso real, estos datos vendrían de fuentes externas.
        
        Args:
            product: Instancia del producto
            
        Returns:
            Diccionario con atributos de ejemplo
        """
        attributes = {}
        
        # Ejemplo de marca basado en el nombre del producto
        name_lower = (product.name or "").lower()
        if "google" in name_lower:
            attributes["brand"] = "Google"
        elif "android" in name_lower:
            attributes["brand"] = "Android"
        else:
            attributes["brand"] = "Generic"
        
        # Ejemplo de colores basado en palabras clave
        colors = []
        color_keywords = {
            "black": "black",
            "white": "white",
            "blue": "blue",
            "red": "red",
            "green": "green",
            "grey": "grey",
            "gray": "grey",
            "navy": "navy",
            "beige": "beige",
            "orange": "orange",
            "teal": "teal"
        }
        
        for keyword, color in color_keywords.items():
            if keyword in name_lower:
                colors.append(color)
        
        if not colors:
            colors = ["default"]  # Color por defecto
        
        attributes["colors"] = colors[:3]  # Máximo 3 colores
        
        # Ejemplo de questions_and_answers
        attributes["questions_and_answers"] = [
            {
                "question": f"¿Cuáles son las dimensiones de {product.name}?",
                "answer": "Las dimensiones específicas se encuentran en la descripción del producto.",
                "user_id": "system_generated"
            }
        ]
        
        return attributes


async def main():
    """Función principal del script."""
    parser = argparse.ArgumentParser(
        description="Popular columna features de productos con datos estructurados"
    )
    parser.add_argument(
        "--account-id",
        type=int,
        required=True,
        help="ID de la cuenta a procesar"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        default=True,
        help="Ejecutar en modo dry-run (no aplicar cambios)"
    )
    parser.add_argument(
        "--apply-changes",
        action="store_true",
        help="Aplicar cambios reales (deshabilita dry-run)"
    )
    
    args = parser.parse_args()
    
    # Determinar si es dry-run
    dry_run = not args.apply_changes
    
    if dry_run:
        log_info("Ejecutando en modo DRY RUN - no se aplicarán cambios")
    else:
        log_warning("Ejecutando en modo APLICAR CAMBIOS - se modificará la base de datos")
    
    # Crear populator y ejecutar
    populator = ProductFeaturesPopulator(
        account_id=args.account_id,
        dry_run=dry_run
    )
    
    await populator.populate_features()
    
    log_info("Proceso completado")


if __name__ == "__main__":
    asyncio.run(main()) 
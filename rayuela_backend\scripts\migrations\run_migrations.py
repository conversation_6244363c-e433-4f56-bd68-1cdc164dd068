#!/usr/bin/env python3
"""
Script para ejecutar migraciones de base de datos de forma segura.

Este script implementa las mejores prácticas para sistemas escalables:
- Separación de migraciones del startup de la aplicación
- Verificación de conectividad antes de ejecutar migraciones
- Logging detallado para auditoria
- Manejo robusto de errores
- Soporte para rollbacks controlados

Uso:
    python run_migrations.py [--dry-run] [--target REVISION] [--rollback STEPS]
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import Optional

# Agregar el directorio raíz al path para importar módulos
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def setup_logging():
    """Configurar logging para el script de migraciones."""
    import logging
    
    # Configurar logger
    logger = logging.getLogger("migrations")
    logger.setLevel(logging.INFO)
    
    # Handler para consola
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

def check_database_connectivity(logger) -> bool:
    """
    Verificar conectividad a la base de datos antes de ejecutar migraciones.
    
    Returns:
        bool: True si la conexión es exitosa, False en caso contrario
    """
    try:
        import psycopg2
        
        # Obtener credenciales de variables de entorno
        db_config = {
            'host': os.environ.get('POSTGRES_SERVER'),
            'port': os.environ.get('POSTGRES_PORT', '5432'),
            'database': os.environ.get('POSTGRES_DB'),
            'user': os.environ.get('POSTGRES_USER'),
            'password': os.environ.get('POSTGRES_PASSWORD')
        }
        
        # Verificar que todas las credenciales estén disponibles
        missing_vars = [k for k, v in db_config.items() if not v]
        if missing_vars:
            logger.error(f"❌ Variables de entorno faltantes: {missing_vars}")
            return False
        
        # Intentar conexión
        logger.info("🔍 Verificando conectividad a la base de datos...")
        logger.info(f"   Host: {db_config['host']}:{db_config['port']}")
        logger.info(f"   Database: {db_config['database']}")
        logger.info(f"   User: {db_config['user']}")
        
        conn = psycopg2.connect(**db_config)
        
        # Verificar que la conexión funcione
        with conn.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            logger.info(f"✅ Conexión exitosa - PostgreSQL: {version}")
        
        conn.close()
        return True
        
    except ImportError:
        logger.error("❌ psycopg2 no está disponible. Instalar con: pip install psycopg2-binary")
        return False
    except Exception as e:
        logger.error(f"❌ Error de conectividad a la base de datos: {e}")
        logger.error("   Verificar:")
        logger.error("   1. Que la base de datos esté ejecutándose")
        logger.error("   2. Que las credenciales sean correctas")
        logger.error("   3. Que no haya restricciones de red/firewall")
        return False

def get_current_revision(logger) -> Optional[str]:
    """
    Obtener la revisión actual de la base de datos.
    
    Returns:
        str: Revisión actual o None si hay error
    """
    try:
        result = subprocess.run(
            ["python", "-m", "alembic", "current"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            # Parsear output para obtener la revisión
            output = result.stdout.strip()
            if output and "(head)" not in output:
                revision = output.split()[0] if output.split() else "None"
                logger.info(f"📋 Revisión actual: {revision}")
                return revision
            else:
                logger.info("📋 Base de datos en HEAD (última revisión)")
                return "head"
        else:
            logger.error(f"❌ Error obteniendo revisión actual: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error ejecutando alembic current: {e}")
        return None

def run_migrations(target: Optional[str] = None, dry_run: bool = False, logger=None) -> bool:
    """
    Ejecutar migraciones de Alembic.
    
    Args:
        target: Revisión objetivo (default: head)
        dry_run: Si True, solo mostrar las migraciones sin ejecutar
        logger: Logger para output
        
    Returns:
        bool: True si las migraciones fueron exitosas
    """
    if not logger:
        logger = setup_logging()
    
    target = target or "head"
    
    try:
        # Comando base
        cmd = ["python", "-m", "alembic"]
        
        if dry_run:
            logger.info("🔍 MODO DRY-RUN - Solo mostrando migraciones pendientes")
            # Mostrar historial de migraciones
            cmd.extend(["history", "--verbose"])
        else:
            logger.info(f"🚀 Ejecutando migraciones hacia: {target}")
            cmd.extend(["upgrade", target])
        
        # Ejecutar comando
        logger.info(f"💻 Comando: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=False if not dry_run else True,
            text=True,
            timeout=600  # 10 minutos timeout
        )
        
        if result.returncode == 0:
            if dry_run:
                logger.info("📊 Historial de migraciones:")
                logger.info(result.stdout)
            else:
                logger.info("✅ MIGRACIONES COMPLETADAS EXITOSAMENTE")
                
                # Verificar estado final
                final_revision = get_current_revision(logger)
                if final_revision:
                    logger.info(f"📊 Estado final: {final_revision}")
            
            return True
        else:
            logger.error(f"❌ ERROR EN MIGRACIONES:")
            logger.error(result.stderr if hasattr(result, 'stderr') and result.stderr else "Error desconocido")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ TIMEOUT: Las migraciones tardaron más de 10 minutos")
        return False
    except Exception as e:
        logger.error(f"❌ Error ejecutando migraciones: {e}")
        return False

def rollback_migrations(steps: int, logger=None) -> bool:
    """
    Ejecutar rollback de migraciones.
    
    Args:
        steps: Número de pasos a retroceder
        logger: Logger para output
        
    Returns:
        bool: True si el rollback fue exitoso
    """
    if not logger:
        logger = setup_logging()
    
    try:
        logger.warning(f"⚠️  EJECUTANDO ROLLBACK de {steps} revisiones")
        logger.warning("🚨 Esta es una operación potencialmente destructiva")
        
        # Obtener revisión actual
        current = get_current_revision(logger)
        if not current:
            logger.error("❌ No se pudo determinar la revisión actual")
            return False
        
        # Ejecutar rollback
        cmd = ["python", "-m", "alembic", "downgrade", f"-{steps}"]
        
        logger.info(f"💻 Comando: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=False,
            text=True,
            timeout=300  # 5 minutos timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ ROLLBACK COMPLETADO EXITOSAMENTE")
            
            # Verificar estado final
            final_revision = get_current_revision(logger)
            if final_revision:
                logger.info(f"📊 Estado después del rollback: {final_revision}")
            
            return True
        else:
            logger.error("❌ ERROR EN ROLLBACK")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error ejecutando rollback: {e}")
        return False

def main():
    """Función principal del script."""
    parser = argparse.ArgumentParser(
        description="Ejecutar migraciones de base de datos de forma segura",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python run_migrations.py                    # Ejecutar todas las migraciones pendientes
  python run_migrations.py --dry-run          # Mostrar migraciones sin ejecutar
  python run_migrations.py --target abc123    # Migrar a revisión específica
  python run_migrations.py --rollback 2       # Retroceder 2 revisiones
        """
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Solo mostrar las migraciones sin ejecutar"
    )
    
    parser.add_argument(
        "--target",
        type=str,
        help="Revisión objetivo (default: head)"
    )
    
    parser.add_argument(
        "--rollback",
        type=int,
        help="Número de revisiones a retroceder"
    )
    
    parser.add_argument(
        "--force",
        action="store_true",
        help="Forzar ejecución sin verificaciones adicionales"
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    logger = setup_logging()
    
    logger.info("🔄 INICIANDO SCRIPT DE MIGRACIONES DE BASE DE DATOS")
    logger.info("🏗️ Implementación de mejores prácticas para sistemas escalables")
    logger.info("=" * 70)
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("alembic"):
        logger.error("❌ Directorio 'alembic' no encontrado")
        logger.error("   Ejecutar este script desde el directorio rayuela_backend/")
        sys.exit(1)
    
    # Verificar conectividad a la base de datos (a menos que se fuerce)
    if not args.force:
        if not check_database_connectivity(logger):
            logger.error("❌ Fallo en verificación de conectividad")
            logger.error("   Usar --force para omitir esta verificación")
            sys.exit(1)
    
    # Mostrar estado inicial
    logger.info("📊 ESTADO INICIAL:")
    initial_revision = get_current_revision(logger)
    if not initial_revision and not args.force:
        logger.error("❌ No se pudo determinar el estado inicial de la base de datos")
        sys.exit(1)
    
    # Ejecutar operación solicitada
    success = False
    
    if args.rollback:
        # Ejecutar rollback
        if not args.force:
            confirm = input(f"⚠️  ¿Confirmar rollback de {args.rollback} revisiones? (y/N): ")
            if confirm.lower() != 'y':
                logger.info("❌ Rollback cancelado por el usuario")
                sys.exit(0)
        
        success = rollback_migrations(args.rollback, logger)
        
    else:
        # Ejecutar migraciones normales
        success = run_migrations(args.target, args.dry_run, logger)
    
    # Resultado final
    logger.info("=" * 70)
    if success:
        logger.info("✅ OPERACIÓN COMPLETADA EXITOSAMENTE")
        if not args.dry_run:
            logger.info("🎯 Base de datos actualizada y lista para uso")
        
        logger.info("🔒 BENEFICIOS DE ESTA IMPLEMENTACIÓN:")
        logger.info("   ✓ Migraciones separadas del startup de la aplicación")
        logger.info("   ✓ Mayor control y robustez en el proceso")
        logger.info("   ✓ Verificaciones de seguridad antes de ejecutar")
        logger.info("   ✓ Logging detallado para auditoria")
        logger.info("   ✓ Soporte para rollbacks controlados")
        
        sys.exit(0)
    else:
        logger.error("❌ OPERACIÓN FALLÓ")
        logger.error("🚨 Revisar los logs anteriores para detalles del error")
        sys.exit(1)

if __name__ == "__main__":
    main() 
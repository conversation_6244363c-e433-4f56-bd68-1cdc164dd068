#!/usr/bin/env python3
"""
Script de verificación para confirmar que la separación de migraciones está configurada correctamente.

Este script verifica:
1. Que las migraciones estén deshabilitadas por defecto en producción
2. Que el script de migraciones funcione correctamente
3. Que el pipeline de CI/CD esté configurado para ejecutar migraciones
4. Que los logs muestren el comportamiento correcto

Uso:
    python verify_migration_setup.py [--env production|development]
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple

def setup_logging():
    """Configurar logging simple para el script."""
    import logging
    
    logger = logging.getLogger("migration_setup_verification")
    logger.setLevel(logging.INFO)
    
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

def check_main_py_configuration(logger) -> <PERSON>ple[bool, List[str]]:
    """
    Verificar que main.py esté configurado correctamente para separar migraciones.
    
    Returns:
        Tuple[bool, List[str]]: (éxito, lista de issues encontrados)
    """
    issues = []
    main_py_path = Path(__file__).parent.parent.parent / "main.py"
    
    if not main_py_path.exists():
        issues.append("❌ Archivo main.py no encontrado")
        return False, issues
    
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar que exista la lógica de skip_migrations_default
        if 'skip_migrations_default = "true" if settings.ENV == "production"' in content:
            logger.info("✅ main.py configurado para deshabilitar migraciones en producción por defecto")
        else:
            issues.append("❌ main.py no está configurado para deshabilitar migraciones en producción")
        
        # Verificar comentarios de mejores prácticas
        if "IMPROVED: Database migrations now handled by CI/CD pipeline" in content:
            logger.info("✅ Comentarios de mejores prácticas presentes en main.py")
        else:
            issues.append("⚠️  main.py podría beneficiarse de comentarios sobre mejores prácticas")
        
        # Verificar logs informativos
        if "PRODUCCIÓN: Migraciones manejadas por CI/CD pipeline" in content:
            logger.info("✅ Logging informativo configurado para producción")
        else:
            issues.append("❌ Faltan logs informativos sobre migraciones en CI/CD")
        
    except Exception as e:
        issues.append(f"❌ Error leyendo main.py: {e}")
        return False, issues
    
    return len(issues) == 0, issues

def check_migration_script_exists(logger) -> Tuple[bool, List[str]]:
    """
    Verificar que el script dedicado de migraciones exista y sea funcional.
    
    Returns:
        Tuple[bool, List[str]]: (éxito, lista de issues encontrados)
    """
    issues = []
    script_path = Path(__file__).parent / "run_migrations.py"
    
    if not script_path.exists():
        issues.append("❌ Script run_migrations.py no encontrado")
        return False, issues
    
    logger.info("✅ Script de migraciones dedicado encontrado")
    
    try:
        # Verificar que el script tenga funcionalidades clave
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            "check_database_connectivity",
            "run_migrations",
            "rollback_migrations",
            "get_current_revision"
        ]
        
        for func in required_functions:
            if f"def {func}" in content:
                logger.info(f"✅ Función {func} presente")
            else:
                issues.append(f"❌ Función {func} no encontrada en el script")
        
        # Verificar argumentos de línea de comandos
        if "--dry-run" in content and "--rollback" in content:
            logger.info("✅ Soporte para dry-run y rollback configurado")
        else:
            issues.append("❌ Faltan opciones de línea de comandos (dry-run, rollback)")
        
    except Exception as e:
        issues.append(f"❌ Error verificando script de migraciones: {e}")
        return False, issues
    
    return len(issues) == 0, issues

def check_cloudbuild_configuration(logger) -> Tuple[bool, List[str]]:
    """
    Verificar que Cloud Build esté configurado para ejecutar migraciones.
    
    Returns:
        Tuple[bool, List[str]]: (éxito, lista de issues encontrados)
    """
    issues = []
    cloudbuild_path = Path(__file__).parent.parent.parent.parent / "cloudbuild-deploy-production.yaml"
    
    if not cloudbuild_path.exists():
        issues.append("❌ cloudbuild-deploy-production.yaml no encontrado")
        return False, issues
    
    try:
        with open(cloudbuild_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar que exista el paso de migraciones
        if "id: 'run-migrations'" in content:
            logger.info("✅ Paso de migraciones encontrado en Cloud Build")
        else:
            issues.append("❌ Paso 'run-migrations' no encontrado en Cloud Build")
        
        # Verificar que use la imagen correcta para migraciones
        if "rayuela-backend:$BUILD_ID" in content and "python -m alembic upgrade head" in content:
            logger.info("✅ Cloud Build configurado para usar imagen del backend para migraciones")
        else:
            issues.append("❌ Cloud Build no está configurado correctamente para migraciones")
        
        # Verificar que las migraciones se ejecuten antes del despliegue
        migration_step_found = False
        deploy_step_found = False
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if "id: 'run-migrations'" in line:
                migration_step_found = True
                migration_line = i
            elif "id: 'deploy-backend'" in line:
                deploy_step_found = True
                deploy_line = i
                break
        
        if migration_step_found and deploy_step_found and migration_line < deploy_line:
            logger.info("✅ Migraciones se ejecutan antes del despliegue del backend")
        else:
            issues.append("❌ Orden incorrecto: migraciones deben ejecutarse antes del despliegue")
        
        # Verificar que deploy-backend dependa de run-migrations
        if "waitFor: ['run-migrations']" in content:
            logger.info("✅ Despliegue del backend depende de migraciones exitosas")
        else:
            issues.append("❌ Despliegue del backend no depende de migraciones")
        
    except Exception as e:
        issues.append(f"❌ Error verificando Cloud Build: {e}")
        return False, issues
    
    return len(issues) == 0, issues

def check_environment_behavior(env: str, logger) -> Tuple[bool, List[str]]:
    """
    Verificar el comportamiento según el entorno.
    
    Args:
        env: Entorno a verificar ('production' o 'development')
        
    Returns:
        Tuple[bool, List[str]]: (éxito, lista de issues encontrados)
    """
    issues = []
    
    logger.info(f"🔍 Verificando comportamiento para entorno: {env}")
    
    # Simular el comportamiento de skip_migrations_default
    if env == "production":
        expected_skip_default = "true"
        logger.info("✅ En producción, migraciones deshabilitadas por defecto")
    else:
        expected_skip_default = "false"
        logger.info("✅ En desarrollo, migraciones habilitadas por defecto")
    
    # Verificar que el comportamiento esté documentado
    docs_path = Path(__file__).parent.parent.parent.parent / "docs" / "PRODUCTION_MIGRATIONS_SEPARATION.md"
    
    if docs_path.exists():
        logger.info("✅ Documentación de separación de migraciones encontrada")
        
        try:
            with open(docs_path, 'r', encoding='utf-8') as f:
                docs_content = f.read()
            
            if env in docs_content and "SKIP_MIGRATIONS" in docs_content:
                logger.info(f"✅ Comportamiento para {env} documentado")
            else:
                issues.append(f"⚠️  Comportamiento para {env} no claramente documentado")
        
        except Exception as e:
            issues.append(f"⚠️  Error leyendo documentación: {e}")
    else:
        issues.append("⚠️  Documentación no encontrada - considerar crear docs/PRODUCTION_MIGRATIONS_SEPARATION.md")
    
    return len(issues) == 0, issues

def run_verification(env: str = "production") -> bool:
    """
    Ejecutar todas las verificaciones.
    
    Args:
        env: Entorno a verificar
        
    Returns:
        bool: True si todas las verificaciones pasan
    """
    logger = setup_logging()
    
    logger.info("🔄 INICIANDO VERIFICACIÓN DE SEPARACIÓN DE MIGRACIONES")
    logger.info("=" * 70)
    
    all_passed = True
    total_issues = []
    
    # Verificar main.py
    logger.info("\n📋 Verificando configuración de main.py...")
    main_passed, main_issues = check_main_py_configuration(logger)
    all_passed = all_passed and main_passed
    total_issues.extend(main_issues)
    
    # Verificar script de migraciones
    logger.info("\n🔧 Verificando script de migraciones...")
    script_passed, script_issues = check_migration_script_exists(logger)
    all_passed = all_passed and script_passed
    total_issues.extend(script_issues)
    
    # Verificar Cloud Build
    logger.info("\n☁️  Verificando configuración de Cloud Build...")
    cloudbuild_passed, cloudbuild_issues = check_cloudbuild_configuration(logger)
    all_passed = all_passed and cloudbuild_passed
    total_issues.extend(cloudbuild_issues)
    
    # Verificar comportamiento por entorno
    logger.info(f"\n🌍 Verificando comportamiento para entorno {env}...")
    env_passed, env_issues = check_environment_behavior(env, logger)
    all_passed = all_passed and env_passed
    total_issues.extend(env_issues)
    
    # Resumen final
    logger.info("\n" + "=" * 70)
    
    if all_passed:
        logger.info("✅ TODAS LAS VERIFICACIONES PASARON")
        logger.info("🏆 La separación de migraciones está configurada correctamente")
        logger.info("\n🔒 BENEFICIOS CONFIRMADOS:")
        logger.info("   ✓ Migraciones separadas del startup en producción")
        logger.info("   ✓ Pipeline de CI/CD configurado para ejecutar migraciones")
        logger.info("   ✓ Script dedicado disponible para operaciones manuales")
        logger.info("   ✓ Comportamiento diferenciado por entorno")
        logger.info("   ✓ Documentación disponible")
    else:
        logger.error("❌ ALGUNAS VERIFICACIONES FALLARON")
        logger.error("🚨 Issues encontrados:")
        for issue in total_issues:
            logger.error(f"   {issue}")
        
        logger.error("\n🔧 ACCIONES RECOMENDADAS:")
        logger.error("   1. Revisar y corregir los issues listados arriba")
        logger.error("   2. Ejecutar nuevamente la verificación")
        logger.error("   3. Consultar docs/PRODUCTION_MIGRATIONS_SEPARATION.md")
    
    return all_passed

def main():
    """Función principal del script."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Verificar que la separación de migraciones esté configurada correctamente"
    )
    
    parser.add_argument(
        "--env",
        choices=["production", "development"],
        default="production",
        help="Entorno a verificar (default: production)"
    )
    
    args = parser.parse_args()
    
    success = run_verification(args.env)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 
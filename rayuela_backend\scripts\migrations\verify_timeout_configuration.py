#!/usr/bin/env python3
"""
Script para verificar la configuración de timeouts en migraciones de Alembic.

Este script valida que:
1. Los timeouts estén configurados correctamente según el entorno
2. No haya configuraciones peligrosas (timeout=0)
3. Los valores sean razonables para cada entorno

Uso:
    python scripts/migrations/verify_timeout_configuration.py
"""

import os
import sys
import re
from pathlib import Path

# Detectar proyecto root
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent.parent


def check_env_py_file():
    """Verifica el archivo alembic/env.py para configuraciones peligrosas."""
    env_file = project_root / "alembic" / "env.py"
    
    if not env_file.exists():
        print("❌ Error: archivo alembic/env.py no encontrado")
        return False
    
    print("🔍 Verificando configuración en alembic/env.py...")
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar configuraciones peligrosas
    dangerous_patterns = [
        r'statement_timeout\s*[=:]\s*[\'"]?0[\'"]?',
        r'lock_timeout\s*[=:]\s*[\'"]?0[\'"]?',
        r'statement_timeout=0',
        r'lock_timeout=0'
    ]
    
    issues_found = []
    
    for pattern in dangerous_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            issues_found.extend(matches)
    
    if issues_found:
        print("⚠️  Configuraciones peligrosas encontradas:")
        for issue in issues_found:
            print(f"   - {issue}")
        print("\n💡 Estas configuraciones pueden causar bloqueos indefinidos en producción")
        return False
    
    # Verificar que existe la función get_migration_timeouts
    if 'get_migration_timeouts' not in content:
        print("⚠️  Función get_migration_timeouts no encontrada")
        print("   Esta función debería manejar timeouts por entorno")
        return False
    
    # Verificar que existen timeouts configurados
    timeout_keywords = ['statement_timeout', 'lock_timeout', 'idle_in_transaction_session_timeout']
    found_keywords = [kw for kw in timeout_keywords if kw in content]
    
    if len(found_keywords) < 2:
        print("⚠️  No se encontraron suficientes configuraciones de timeout")
        print(f"   Encontrados: {found_keywords}")
        return False
    
    print("✅ Archivo alembic/env.py parece tener configuración segura")
    return True


def simulate_timeout_detection():
    """Simula la detección de entorno que usa get_migration_timeouts."""
    print("\n🔍 Simulando detección de entorno...")
    
    # Replicar la lógica de get_migration_timeouts
    # Como no podemos importar settings, usar detección básica
    database_url = os.getenv('DATABASE_URL', 'postgresql://localhost/test')
    
    is_development = (
        os.getenv('ENVIRONMENT', '').lower() in ['development', 'dev', 'local'] or
        os.getenv('DEBUG', '').lower() == 'true' or
        'localhost' in database_url or
        '127.0.0.1' in database_url
    )
    
    print(f"Variables de entorno:")
    print(f"   ENVIRONMENT: {os.getenv('ENVIRONMENT', 'No definida')}")
    print(f"   DEBUG: {os.getenv('DEBUG', 'No definida')}")
    print(f"   DATABASE_URL: {database_url[:50]}...")
    
    if is_development:
        timeouts = {
            'statement_timeout': '30min',
            'lock_timeout': '10min',
            'idle_in_transaction_session_timeout': '20min'
        }
        print(f"\n🔧 Entorno detectado: DESARROLLO")
    else:
        timeouts = {
            'statement_timeout': '10min',
            'lock_timeout': '2min',
            'idle_in_transaction_session_timeout': '5min'
        }
        print(f"\n🔧 Entorno detectado: PRODUCCIÓN")
    
    print("Timeouts configurados:")
    for key, value in timeouts.items():
        print(f"   {key}: {value}")
    
    return timeouts, is_development


def validate_timeout_values(timeouts, is_development):
    """Valida que los valores de timeout sean razonables."""
    print("\n🔍 Validando valores de timeout...")
    
    issues = []
    
    # Convertir timeouts a minutos para validación
    def parse_timeout(value):
        if value.endswith('min'):
            return int(value[:-3])
        elif value.endswith('hour'):
            return int(value[:-4]) * 60
        else:
            return 0
    
    statement_timeout = parse_timeout(timeouts.get('statement_timeout', '0'))
    lock_timeout = parse_timeout(timeouts.get('lock_timeout', '0'))
    
    # Validaciones
    if statement_timeout == 0:
        issues.append("statement_timeout no debería ser 0 (bloqueos indefinidos)")
    
    if lock_timeout == 0:
        issues.append("lock_timeout no debería ser 0 (locks indefinidos)")
    
    if is_development:
        # En desarrollo, permitir timeouts más largos
        if statement_timeout > 60:  # Más de 1 hora
            issues.append(f"statement_timeout muy largo para desarrollo: {statement_timeout}min")
    else:
        # En producción, ser más estricto
        if statement_timeout > 30:  # Más de 30 minutos
            issues.append(f"statement_timeout muy largo para producción: {statement_timeout}min")
        
        if lock_timeout > 10:  # Más de 10 minutos
            issues.append(f"lock_timeout muy largo para producción: {lock_timeout}min")
    
    if lock_timeout > statement_timeout:
        issues.append("lock_timeout no debería ser mayor que statement_timeout")
    
    if issues:
        print("⚠️  Problemas encontrados en valores de timeout:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("✅ Valores de timeout son razonables")
    return True


def check_migration_files():
    """Verifica archivos de migración por configuraciones peligrosas."""
    print("\n🔍 Verificando archivos de migración...")
    
    migrations_dir = project_root / "alembic" / "versions"
    
    if not migrations_dir.exists():
        print("⚠️  Directorio de migraciones no encontrado")
        return True
    
    migration_files = list(migrations_dir.glob("*.py"))
    
    if not migration_files:
        print("ℹ️  No se encontraron archivos de migración")
        return True
    
    dangerous_patterns = [
        r'statement_timeout\s*=\s*0',
        r'lock_timeout\s*=\s*0',
        r'SET\s+statement_timeout\s*=\s*0',
        r'SET\s+lock_timeout\s*=\s*0'
    ]
    
    issues_found = []
    
    for migration_file in migration_files:
        with open(migration_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        for pattern in dangerous_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                issues_found.append(f"{migration_file.name}: {matches}")
    
    if issues_found:
        print("⚠️  Configuraciones peligrosas encontradas en migraciones:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    
    print(f"✅ {len(migration_files)} archivos de migración verificados - sin problemas")
    return True


def check_env_file_structure():
    """Verifica la estructura del archivo env.py."""
    print("\n🔍 Verificando estructura del archivo env.py...")
    
    env_file = project_root / "alembic" / "env.py"
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_elements = [
        'get_migration_timeouts',
        'MIGRATION_TIMEOUTS',
        'run_migrations_online_sync',
        'run_migrations_online'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in content:
            missing_elements.append(element)
    
    if missing_elements:
        print("⚠️  Elementos faltantes en env.py:")
        for element in missing_elements:
            print(f"   - {element}")
        return False
    
    # Verificar que no haya timeouts hardcodeados peligrosos
    if 'timeout=0' in content or 'timeout = 0' in content:
        print("⚠️  Se encontraron timeouts hardcodeados en 0")
        return False
    
    print("✅ Estructura del archivo env.py es correcta")
    return True


def generate_recommendations():
    """Genera recomendaciones basadas en la configuración actual."""
    print("\n📝 Recomendaciones:")
    
    print("1. 🔒 Seguridad:")
    print("   - Nunca usar timeout=0 en producción")
    print("   - Monitorear duración de migraciones")
    print("   - Configurar alertas para migraciones largas")
    
    print("\n2. 🚀 Performance:")
    print("   - Dividir migraciones grandes en pasos pequeños")
    print("   - Usar índices CONCURRENTLY para tablas grandes")
    print("   - Considerar migraciones zero-downtime")
    
    print("\n3. 📊 Monitoreo:")
    print("   - Logs de duración de migraciones")
    print("   - Métricas de locks activos")
    print("   - Alertas de PostgreSQL")
    
    print("\n4. 🛠️  Herramientas:")
    print("   - Ejecutar este script regularmente")
    print("   - Usar pg_stat_activity para monitorear")
    print("   - Considerar herramientas como pg_repack")


def main():
    """Función principal."""
    print("🔍 Verificador de Configuración de Timeouts para Migraciones")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 1. Verificar archivo env.py
    if not check_env_py_file():
        all_checks_passed = False
    
    # 2. Verificar estructura del archivo
    if not check_env_file_structure():
        all_checks_passed = False
    
    # 3. Simular detección de entorno
    try:
        timeouts, is_development = simulate_timeout_detection()
        
        # 4. Validar valores de timeout
        if not validate_timeout_values(timeouts, is_development):
            all_checks_passed = False
            
    except Exception as e:
        print(f"❌ Error al simular configuración: {e}")
        all_checks_passed = False
    
    # 5. Verificar archivos de migración
    if not check_migration_files():
        all_checks_passed = False
    
    # 6. Generar recomendaciones
    generate_recommendations()
    
    # Resultado final
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("✅ TODAS LAS VERIFICACIONES PASARON")
        print("   La configuración de timeouts es segura")
        return 0
    else:
        print("❌ ALGUNAS VERIFICACIONES FALLARON")
        print("   Revisa los problemas identificados arriba")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 
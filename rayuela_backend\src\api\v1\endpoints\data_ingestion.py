from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from src.db import schemas
from src.db.session import get_db
from src.core.deps import get_current_account, get_limit_service, get_batch_data_storage_service
from src.core.redis_utils import get_redis
from src.services import LimitService, DataIngestionService
from src.utils.base_logger import log_error, log_info
from typing import List, Optional

router = APIRouter()


@router.post("/batch", response_model=schemas.BatchIngestionResponse, status_code=status.HTTP_202_ACCEPTED)
async def batch_data_ingestion(
    data: schemas.BatchIngestionRequest,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
    redis: Redis = Depends(get_redis),
    storage_service = Depends(get_batch_data_storage_service),
):
    """
    Carga masiva de datos de usuarios, productos e interacciones.

    Este endpoint permite cargar datos en lote para inicializar o actualizar el sistema de recomendación.
    Los datos se procesan de forma asíncrona utilizando Celery para mayor robustez.
    Los datos sensibles se almacenan de forma segura en GCS o en el sistema de archivos local.

    Para una guía detallada sobre cómo formatear y enviar datos, consulte la
    [Guía de Ingesta de Datos Masiva](/docs/guides/data_ingestion_guide).
    """
    try:
        # Crear servicio de ingesta de datos con Redis para medición de almacenamiento
        # y servicio de almacenamiento para datos sensibles
        ingestion_service = DataIngestionService(
            db=db,
            account_id=account.account_id,
            limit_service=limit_service,
            redis=redis,
            storage_service=storage_service
        )

        log_info(f"Processing batch data ingestion for account {account.account_id} with secure storage")

        # Usar el servicio para crear el trabajo de ingesta
        return await ingestion_service.create_batch_ingestion_job(data)

    except HTTPException:
        # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
        raise
    except Exception as e:
        log_error(f"Error in batch data ingestion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing batch data: {str(e)}"
        )


@router.get("/batch/{job_id}", response_model=schemas.BatchIngestionJobStatus)
async def get_batch_job_status(
    job_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Consulta el estado de un trabajo de ingesta masiva.

    Este endpoint permite verificar el progreso de un trabajo de ingesta masiva,
    incluyendo la cantidad de registros procesados, errores encontrados y tiempo estimado
    de finalización.

    Para más detalles sobre cómo monitorear el proceso de ingesta y manejar errores,
    consulte la sección [Monitoreo del Proceso](/docs/guides/data_ingestion_guide#monitoreo-del-proceso)
    en la Guía de Ingesta de Datos.

    Args:
        job_id: ID del trabajo de ingesta masiva
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Estado del trabajo de ingesta masiva
    """
    try:
        # Crear servicio de ingesta de datos
        ingestion_service = DataIngestionService(
            db=db,
            account_id=account.account_id,
            limit_service=limit_service
        )

        # Usar el servicio para obtener el estado del trabajo
        return await ingestion_service.get_batch_job_status(job_id)

    except HTTPException:
        # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
        raise
    except Exception as e:
        log_error(f"Error getting batch job status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting batch job status: {str(e)}"
        )


@router.get("/batch", response_model=List[schemas.BatchIngestionJobStatus])
async def list_batch_jobs(
    limit: int = Query(20, ge=1, le=100, description="Maximum number of jobs to return"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    """
    Lista los trabajos de ingesta masiva recientes para la cuenta actual.

    Este endpoint permite obtener una lista de trabajos de ingesta masiva,
    ordenados por fecha de creación (más recientes primero).

    Args:
        limit: Número máximo de trabajos a devolver (máximo 100)
        status: Filtrar por estado del trabajo (opcional)
        account: Cuenta actual
        db: Sesión de base de datos

    Returns:
        Lista de trabajos de ingesta masiva
    """
    try:
        # Crear servicio de ingesta de datos
        ingestion_service = DataIngestionService(
            db=db,
            account_id=account.account_id,
            limit_service=limit_service
        )

        # Obtener trabajos recientes usando el repositorio
        from src.db.repositories.batch_ingestion import BatchIngestionJobRepository
        job_repo = BatchIngestionJobRepository(db, account.account_id)
        
        jobs = await job_repo.get_recent_jobs(limit=limit, status=status)
        
        # Transformar a la respuesta esperada
        job_statuses = []
        for job in jobs:
            job_status = {
                "job_id": job.id,
                "status": job.status,
                "created_at": job.created_at,
                "started_at": job.started_at,
                "completed_at": job.completed_at,
                "error_message": job.error_message,
                "task_id": job.task_id,
                "processed_count": job.processed_count or {},
                "parameters": job.parameters or {},
            }
            
            # Agregar campos calculados si están disponibles
            if job.processed_count:
                total = job.processed_count.get("total", 0)
                errors = job.processed_count.get("errors", 0)
                
                if total > 0:
                    job_status["progress_percentage"] = min(100.0, (total / max(1, job.parameters.get("estimated_total", total))) * 100)
                    if errors > 0:
                        job_status["success_rate"] = ((total - errors) / total) * 100
                        job_status["error_count"] = errors
            
            job_statuses.append(job_status)
        
        return job_statuses

    except HTTPException:
        # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
        raise
    except Exception as e:
        log_error(f"Error listing batch jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing batch jobs: {str(e)}"
        )

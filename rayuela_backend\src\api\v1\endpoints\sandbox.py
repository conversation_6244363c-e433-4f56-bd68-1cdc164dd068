"""
Endpoints específicos para funcionalidades de Developer Sandbox.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_current_user_and_account
from src.db.session import get_db
from src.db.models import SystemUser, Account
from src.db.enums import SubscriptionPlan
from src.services.sandbox_service import SandboxService

router = APIRouter()

@router.post("/reset")
async def reset_sandbox_data(
    current_user_account: tuple[SystemUser, Account] = Depends(get_current_user_and_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Reset all sandbox data for FREE plan users.
    
    This endpoint allows FREE plan users to clean their experimental data
    to start fresh with new experiments.
    
    Only available for FREE plan users.
    """
    current_user, account = current_user_account
    
    # Check if account is on FREE plan
    if account.subscription and account.subscription.plan != SubscriptionPlan.FREE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Sandbox reset is only available for Developer Sandbox (FREE) plan users"
        )
    
    # Check if account has FREE plan or no subscription (defaults to FREE)
    sandbox_service = SandboxService(db)
    
    try:
        reset_summary = await sandbox_service.reset_account_data(account.account_id)
        
        return {
            "success": True,
            "message": "Sandbox data reset successfully",
            "reset_summary": reset_summary
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting sandbox data: {str(e)}"
        )

@router.get("/status")
async def get_sandbox_status(
    current_user_account: tuple[SystemUser, Account] = Depends(get_current_user_and_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Get current sandbox status and data counts.
    
    Returns information about the current state of sandbox data.
    """
    current_user, account = current_user_account
    
    # Only show sandbox status for FREE plan users
    if account.subscription and account.subscription.plan != SubscriptionPlan.FREE:
        return {
            "is_sandbox": False,
            "message": "Not a sandbox account"
        }
    
    sandbox_service = SandboxService(db)
    
    try:
        status_info = await sandbox_service.get_sandbox_status(account.account_id)
        
        return {
            "is_sandbox": True,
            "status": status_info
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting sandbox status: {str(e)}"
        ) 
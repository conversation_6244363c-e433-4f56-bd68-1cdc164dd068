"""
Consolidated endpoint for usage summary information.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from typing import Dict, Any, cast, Union, TypeVar, Optional
from datetime import datetime, timezone, timedelta

from src.core.deps import (
    get_current_active_user,
    get_db,
    get_subscription_service,
    get_storage_tracker_service,
    get_limit_service,
)
from src.core.redis_utils import get_redis
from src.db.models import SystemUser
from src.services import SubscriptionService
from src.services.storage_tracker_service import StorageTrackerService
from src.services.redis_storage_meter_service import RedisStorageMeterService
from src.services.limit_service import LimitService
from src.utils.base_logger import log_info, log_error
from src.db.repositories.model import TrainingJobRepository
from src.db.enums import PLAN_LIMITS, SubscriptionPlan
from src.db.schemas.usage_summary import UsageSummaryResponse, SubscriptionInfo, ApiCallsUsage, StorageUsage, TrainingUsage, PlanLimits, PlanInfo, PlanFeatures

router = APIRouter()

# Helper function to safely convert database values to Python types
def safe_value(value: Any, default: Any = 0) -> Any:
    """Safely convert a database value to a Python type."""
    if value is None:
        return default
    try:
        # For numeric values, convert to int or float
        if isinstance(value, (int, float)):
            return value
        return int(value) if isinstance(value, str) and value.isdigit() else float(value)
    except (ValueError, TypeError):
        return default


@router.get("/summary", response_model=UsageSummaryResponse)
async def get_usage_summary(
    current_user: SystemUser = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    storage_tracker: StorageTrackerService = Depends(get_storage_tracker_service),
    limit_service: LimitService = Depends(get_limit_service),
    redis: Redis = Depends(get_redis),
    db: AsyncSession = Depends(get_db),
):
    """
    Get a consolidated summary of usage information for the current account.

    Returns:
        Dict with comprehensive usage information including:
        - Current plan details
        - API calls usage with reset date
        - Storage usage with last measurement time
        - Training usage (last training date and frequency limit)
        - Link to Stripe Billing Portal
    """
    try:
        account_id = current_user.account_id

        # Get subscription
        subscription = await subscription_service._subscription_repo.get_by_account(account_id)

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found",
            )

        # Get plan limits
        plan_type = subscription.plan_type
        # Convert string to SubscriptionPlan enum
        plan_enum = cast(SubscriptionPlan, plan_type)
        plan_limits = PLAN_LIMITS.get(plan_enum, {})

        # Get storage usage from Redis
        redis_meter = RedisStorageMeterService(redis, account_id)
        redis_storage_bytes = await redis_meter.get_storage_usage_bytes()
        storage_details = await redis_meter.get_storage_details()

        # If not in Redis, calculate using storage tracker
        if redis_storage_bytes == 0 and storage_details is None:
            log_info(f"Storage usage for account {account_id} not found in Redis, calculating directly")
            db_storage_bytes = await storage_tracker.get_current_storage_usage()
            storage_used = db_storage_bytes
            storage_last_measured = "Just now"
            storage_source = "database_calculation"
        else:
            storage_used = redis_storage_bytes
            storage_last_measured = storage_details.get("measured_at", "Unknown") if storage_details else "Unknown"
            storage_source = "redis_cache"

        # Calculate time since last measurement
        try:
            if storage_last_measured != "Just now" and storage_last_measured != "Unknown":
                last_measured_dt = datetime.fromisoformat(storage_last_measured)
                now = datetime.now(timezone.utc)
                time_diff = now - last_measured_dt
                hours_ago = time_diff.total_seconds() / 3600
                storage_last_measured = f"{storage_last_measured} (about {int(hours_ago)} hours ago)"
        except Exception as e:
            log_error(f"Error calculating time since last storage measurement: {str(e)}")

        # Get training information
        training_repo = TrainingJobRepository(db, account_id=account_id)

        # Get last successful training job
        last_training_job = await training_repo.get_last_successful()

        # Get training frequency limit
        training_frequency_str = plan_limits.get("training_frequency", "manual")

        # Calculate next available training time
        next_training_available = None
        if subscription.last_successful_training_at is not None and training_frequency_str != "manual":
            # Parse training frequency string
            frequency_parts = training_frequency_str.split()
            if len(frequency_parts) == 2:
                try:
                    value = int(frequency_parts[0])
                    unit = frequency_parts[1]

                    if unit in ["day", "days"]:
                        delta = timedelta(days=value)
                    elif unit in ["hour", "hours"]:
                        delta = timedelta(hours=value)
                    else:
                        delta = timedelta(days=7)  # Default to 7 days

                    next_training_available = subscription.last_successful_training_at + delta
                except (ValueError, IndexError):
                    next_training_available = None

        # Get all available plans with their limits
        all_plans = {}
        for plan_enum, limits in PLAN_LIMITS.items():
            plan_type = plan_enum.value  # Convert enum to string
            # Format storage limits for display
            storage_mb = limits.get("storage_limit", 0) / (1024 * 1024)
            storage_gb = limits.get("storage_limit", 0) / (1024 * 1024 * 1024)

            # Format training data limit for display
            training_data_limit = limits.get("training_data_limit", 0)
            training_data_limit_formatted = (
                f"{training_data_limit:,} interactions"
                if training_data_limit > 0
                else "Unlimited"
            )

            # Format max items and users for display
            max_items = limits.get("max_items", 0)
            max_items_formatted = f"{max_items:,}" if max_items > 0 else "Unlimited"

            max_users = limits.get("max_users", 0)
            max_users_formatted = f"{max_users:,}" if max_users > 0 else "Unlimited"

            # Create plan info object
            all_plans[plan_type] = {
                "id": plan_type,
                "limits": {
                    "api_calls": limits.get("api_calls_limit", 0),
                    "storage_bytes": limits.get("storage_limit", 0),
                    "storage_mb": round(storage_mb, 2),
                    "storage_gb": round(storage_gb, 4),
                    "max_requests_per_minute": limits.get("max_requests_per_minute", 0),
                    "max_concurrent_requests": limits.get("max_concurrent_requests", 0),
                    "training_frequency": limits.get("training_frequency", "manual"),
                    "training_data_limit": training_data_limit,
                    "training_data_limit_formatted": training_data_limit_formatted,
                    "max_items": max_items,
                    "max_items_formatted": max_items_formatted,
                    "max_users": max_users,
                    "max_users_formatted": max_users_formatted,
                    "recommendation_cache_ttl": limits.get("recommendation_cache_ttl", 3600),
                },
                "features": {
                    "available_models": limits.get("available_models", []),
                }
            }

        # Format the response
        usage_info = UsageSummaryResponse(
            subscription=SubscriptionInfo(
                plan=subscription.plan_type,
                is_active=subscription.is_active,
                expires_at=subscription.expires_at,
            ),
            api_calls=ApiCallsUsage(
                used=subscription.monthly_api_calls_used,
                limit=subscription.api_calls_limit,
                percentage=round(
                    (safe_value(subscription.monthly_api_calls_used) / safe_value(subscription.api_calls_limit, 1)) * 100,
                    2,
                ),
                reset_date=subscription.last_reset_date,
            ),
            storage=StorageUsage(
                used_bytes=storage_used,
                limit_bytes=subscription.storage_limit,
                percentage=round(
                    (safe_value(storage_used) / safe_value(subscription.storage_limit, 1)) * 100,
                    2,
                ),
                last_measured=storage_last_measured,
                source=storage_source,
            ),
            training=TrainingUsage(
                last_training_date=subscription.last_successful_training_at,
                frequency_limit=training_frequency_str,
                next_available=next_training_available,
                can_train_now=next_training_available is None or datetime.now(timezone.utc) >= next_training_available,
            ),
            plan_limits=PlanLimits(
                api_calls=plan_limits.get("api_calls_limit", 0),
                storage_bytes=plan_limits.get("storage_limit", 0),
                storage_mb=round(plan_limits.get("storage_limit", 0) / (1024 * 1024), 2),
                storage_gb=round(plan_limits.get("storage_limit", 0) / (1024 * 1024 * 1024), 4),
                max_requests_per_minute=plan_limits.get("max_requests_per_minute", 0),
                max_concurrent_requests=plan_limits.get("max_concurrent_requests", 0),
                training_frequency=plan_limits.get("training_frequency", "manual"),
                training_data_limit=plan_limits.get("training_data_limit", 0),
                training_data_limit_formatted=(
                    f"{plan_limits.get('training_data_limit', 0):,} interactions"
                    if plan_limits.get('training_data_limit', 0) > 0
                    else "Unlimited"
                ),
                max_items=plan_limits.get("max_items", 0),
                max_items_formatted=f"{plan_limits.get('max_items', 0):,}" if plan_limits.get('max_items', 0) > 0 else "Unlimited",
                max_users=plan_limits.get("max_users", 0),
                max_users_formatted=f"{plan_limits.get('max_users', 0):,}" if plan_limits.get('max_users', 0) > 0 else "Unlimited",
                recommendation_cache_ttl=plan_limits.get("recommendation_cache_ttl", 3600),
            ),
            all_plans={
                plan_type: PlanInfo(
                    id=plan_type,
                    limits=PlanLimits(
                        api_calls=limits.get("api_calls_limit", 0),
                        storage_bytes=limits.get("storage_limit", 0),
                        storage_mb=round(limits.get("storage_limit", 0) / (1024 * 1024), 2),
                        storage_gb=round(limits.get("storage_limit", 0) / (1024 * 1024 * 1024), 4),
                        max_requests_per_minute=limits.get("max_requests_per_minute", 0),
                        max_concurrent_requests=limits.get("max_concurrent_requests", 0),
                        training_frequency=limits.get("training_frequency", "manual"),
                        training_data_limit=limits.get("training_data_limit", 0),
                        training_data_limit_formatted=(
                            f"{limits.get('training_data_limit', 0):,} interactions"
                            if limits.get('training_data_limit', 0) > 0
                            else "Unlimited"
                        ),
                        max_items=limits.get("max_items", 0),
                        max_items_formatted=f"{limits.get('max_items', 0):,}" if limits.get('max_items', 0) > 0 else "Unlimited",
                        max_users=limits.get("max_users", 0),
                        max_users_formatted=f"{limits.get('max_users', 0):,}" if limits.get('max_users', 0) > 0 else "Unlimited",
                        recommendation_cache_ttl=limits.get("recommendation_cache_ttl", 3600),
                    ),
                    features=PlanFeatures(
                        available_models=limits.get("available_models", [])
                    )
                )
                for plan_enum, limits in PLAN_LIMITS.items()
                for plan_type in [plan_enum.value]  # Convert enum to string
            },
            billing_portal_url="/api/v1/billing/create-portal-session"
        )

        log_info(f"Retrieved usage summary for account {account_id}")
        return usage_info

    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error retrieving usage summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving usage summary: {str(e)}",
        )

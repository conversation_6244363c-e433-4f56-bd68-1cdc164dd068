from fastapi import Request
from fastapi.responses import JSONResponse
from src.core.exceptions import (
    NotFoundError,
    ValidationError,
    PermissionDeniedError,
    ConflictError,
    RateLimitExceededError,
    InternalServerError,
    CustomHTTPException
)
from src.utils.base_logger import log_error
from src.middleware.request_id_middleware import get_request_id
from typing import Dict, Any, Optional
import time


def create_error_response(
    request: Request,
    status_code: int,
    message: str,
    error_code: str,
    additional_data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None
) -> JSONResponse:
    """
    Crea una respuesta de error estandarizada con estructura JSON consistente.
    
    Args:
        request: Objeto Request de FastAPI
        status_code: Código de estado HTTP
        message: Mensaje de error legible
        error_code: Código de error estandarizado
        additional_data: Datos adicionales para incluir en la respuesta
        headers: Headers HTTP adicionales
        
    Returns:
        JSONResponse con estructura de error consistente
    """
    request_id = get_request_id(request)
    
    # Estructura de error consistente
    error_response = {
        "message": message,
        "error_code": error_code,
        "request_id": request_id,
        "timestamp": int(time.time()),
        "path": str(request.url.path)
    }
    
    # Añadir datos adicionales si se proporcionan
    if additional_data:
        error_response.update(additional_data)
    
    # Crear respuesta JSON
    response = JSONResponse(
        status_code=status_code,
        content=error_response
    )
    
    # Añadir headers si se proporcionan
    if headers:
        for key, value in headers.items():
            response.headers[key] = value
    
    # Siempre incluir el request_id en headers
    response.headers["X-Request-ID"] = request_id
    
    return response


async def handle_not_found(request: Request, exc: NotFoundError) -> JSONResponse:
    """Manejador para errores de tipo 'recurso no encontrado'."""
    log_error(f"Not Found Error [{get_request_id(request)}]: {exc.detail}")
    
    return create_error_response(
        request=request,
        status_code=404,
        message=str(exc.detail),
        error_code="RESOURCE_NOT_FOUND"
    )


async def handle_validation_error(request: Request, exc: ValidationError) -> JSONResponse:
    """Manejador para errores de validación."""
    log_error(f"Validation Error [{get_request_id(request)}]: {exc.detail}")
    
    return create_error_response(
        request=request,
        status_code=422,
        message=str(exc.detail),
        error_code="VALIDATION_ERROR",
        additional_data={"validation_error": True}
    )


async def handle_permission_denied(request: Request, exc: PermissionDeniedError) -> JSONResponse:
    """Manejador para errores de permisos denegados."""
    log_error(f"Permission Denied [{get_request_id(request)}]: {exc.detail}")
    
    return create_error_response(
        request=request,
        status_code=403,
        message=str(exc.detail),
        error_code="INSUFFICIENT_PERMISSIONS"
    )


async def handle_conflict(request: Request, exc: ConflictError) -> JSONResponse:
    """Manejador para errores de conflicto."""
    log_error(f"Conflict Error [{get_request_id(request)}]: {exc.detail}")
    
    return create_error_response(
        request=request,
        status_code=409,
        message=str(exc.detail),
        error_code="CONFLICT"
    )


async def handle_rate_limit(request: Request, exc: RateLimitExceededError) -> JSONResponse:
    """Manejador para errores de límite de tasa excedido."""
    log_error(f"Rate Limit Exceeded [{get_request_id(request)}]: {exc.detail}")
    
    # Calcular tiempo de reintento recomendado (60 segundos por defecto)
    retry_after = getattr(exc, 'retry_after', 60)
    
    return create_error_response(
        request=request,
        status_code=429,
        message=str(exc.detail),
        error_code="RATE_LIMIT_EXCEEDED",
        additional_data={
            "retry_after": retry_after,
            "retry_after_description": f"You can retry after {retry_after} seconds"
        },
        headers={"Retry-After": str(retry_after)}
    )


async def handle_internal_error(request: Request, exc: InternalServerError) -> JSONResponse:
    """Manejador para errores internos del servidor."""
    request_id = get_request_id(request)
    log_error(f"Internal Server Error [{request_id}]: {exc.detail}")
    
    return create_error_response(
        request=request,
        status_code=500,
        message="An internal server error occurred. Please contact support with the request ID.",
        error_code="INTERNAL_ERROR",
        additional_data={
            "internal_error": True,
            "support_message": f"Please contact support with request ID: {request_id}"
        }
    )


async def handle_custom_http_exception(request: Request, exc: CustomHTTPException) -> JSONResponse:
    """Manejador genérico para todas las CustomHTTPException."""
    request_id = get_request_id(request)
    log_error(f"Custom HTTP Exception [{request_id}]: {exc.detail}")
    
    # Extraer información de la excepción
    if isinstance(exc.detail, dict):
        message = exc.detail.get("message", str(exc.detail))
        error_code = exc.detail.get("error_code", "INTERNAL_ERROR")
        additional_data = {k: v for k, v in exc.detail.items() if k not in ["message", "error_code"]}
    else:
        message = str(exc.detail)
        error_code = getattr(exc, 'error_code', 'INTERNAL_ERROR')
        additional_data = None
    
    # Manejar headers especiales para rate limiting
    headers = None
    if exc.status_code == 429:
        retry_after = getattr(exc, 'retry_after', 60)
        headers = {"Retry-After": str(retry_after)}
        if additional_data is None:
            additional_data = {}
        additional_data.update({
            "retry_after": retry_after,
            "retry_after_description": f"You can retry after {retry_after} seconds"
        })
    
    return create_error_response(
        request=request,
        status_code=exc.status_code,
        message=message,
        error_code=error_code,
        additional_data=additional_data,
        headers=headers
    )


def register_exception_handlers(app):
    """Registra los manejadores de excepciones en la aplicación."""
    app.add_exception_handler(NotFoundError, handle_not_found)
    app.add_exception_handler(ValidationError, handle_validation_error)
    app.add_exception_handler(PermissionDeniedError, handle_permission_denied)
    app.add_exception_handler(ConflictError, handle_conflict)
    app.add_exception_handler(RateLimitExceededError, handle_rate_limit)
    app.add_exception_handler(InternalServerError, handle_internal_error)
    app.add_exception_handler(CustomHTTPException, handle_custom_http_exception) 
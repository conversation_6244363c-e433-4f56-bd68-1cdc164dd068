from fastapi import HTTPException, status
from functools import wraps
from src.utils.base_logger import log_error
from typing import Any, Optional, Dict
from enum import Enum


class ErrorCode(str, Enum):
    """Códigos de error estandarizados para la API.

    Estos códigos permiten a los clientes manejar errores específicos de forma programática,
    sin depender del mensaje de error que podría cambiar.
    """
    # Errores de autenticación y autorización (401, 403)
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"  # Credenciales inválidas
    INVALID_TOKEN = "INVALID_TOKEN"  # Token JWT inválido o expirado
    INVALID_API_KEY = "INVALID_API_KEY"  # API Key inválida
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"  # Permisos insuficientes
    EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED"  # Email no verificado

    # Errores de recursos no encontrados (404)
    ACCOUNT_NOT_FOUND = "ACCOUNT_NOT_FOUND"  # Cuenta no encontrada
    USER_NOT_FOUND = "USER_NOT_FOUND"  # Usuario no encontrado
    PRODUCT_NOT_FOUND = "PRODUCT_NOT_FOUND"  # Producto no encontrado
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"  # Recurso genérico no encontrado
    MODEL_NOT_FOUND = "MODEL_NOT_FOUND"  # Modelo no encontrado

    # Errores de validación y conflictos (400, 409, 422)
    VALIDATION_ERROR = "VALIDATION_ERROR"  # Error de validación de datos
    DUPLICATE_ENTRY = "DUPLICATE_ENTRY"  # Entrada duplicada
    INVALID_DATA = "INVALID_DATA"  # Datos inválidos
    CONFLICT = "CONFLICT"  # Conflicto con el estado actual

    # Errores de límites (429)
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"  # Límite de tasa excedido
    RESOURCE_LIMIT_EXCEEDED = "RESOURCE_LIMIT_EXCEEDED"  # Límite de recursos excedido
    SUBSCRIPTION_LIMIT = "SUBSCRIPTION_LIMIT"  # Límite de suscripción
    TRAINING_FREQUENCY_LIMIT = "TRAINING_FREQUENCY_LIMIT"  # Límite de frecuencia de entrenamiento

    # Errores de recomendación (500, pero específicos del dominio)
    RECOMMENDATION_ERROR = "RECOMMENDATION_ERROR"  # Error general de recomendación
    MODEL_NOT_TRAINED = "MODEL_NOT_TRAINED"  # Modelo no entrenado
    TRAINING_ERROR = "TRAINING_ERROR"  # Error en el entrenamiento

    # Errores de base de datos y sistema (500)
    DATABASE_ERROR = "DATABASE_ERROR"  # Error de base de datos
    PARTITION_ERROR = "PARTITION_ERROR"  # Error de partición
    INTERNAL_ERROR = "INTERNAL_ERROR"  # Error interno del servidor
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"  # Error de servicio externo


class CustomHTTPException(HTTPException):
    """Excepción HTTP personalizada con código de error estandarizado."""

    def __init__(self, status_code: int, detail: Any, error_code: ErrorCode = ErrorCode.INTERNAL_ERROR):
        # Si detail es un diccionario, asegurarse de que tenga error_code
        if isinstance(detail, dict) and "error_code" not in detail:
            detail["error_code"] = error_code
        # Si detail es una cadena, crear un diccionario con el mensaje y el código
        elif isinstance(detail, str):
            detail = {
                "message": detail,
                "error_code": error_code
            }

        super().__init__(status_code=status_code, detail=detail)


class AccountNotFoundError(CustomHTTPException):
    """Excepción lanzada cuando no se encuentra una cuenta."""

    def __init__(self, account_id: Optional[int] = None):
        detail = (
            f"Account with ID {account_id} not found"
            if account_id
            else "Account not found"
        )
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code=ErrorCode.ACCOUNT_NOT_FOUND
        )


class InvalidCredentialsError(CustomHTTPException):
    """Excepción lanzada cuando las credenciales son inválidas."""

    def __init__(self, message: str = "Invalid credentials provided"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=message,
            error_code=ErrorCode.INVALID_CREDENTIALS
        )


class RateLimitExceededError(CustomHTTPException):
    """Excepción lanzada cuando se excede el límite de peticiones."""

    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = 60):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=message,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED
        )
        self.retry_after = retry_after


class ResourceNotFoundError(CustomHTTPException):
    """Excepción base para recursos no encontrados."""

    def __init__(self, resource_type: str, resource_id: Any):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{resource_type} with ID {resource_id} not found",
            error_code=ErrorCode.RESOURCE_NOT_FOUND
        )


class ProductNotFoundException(CustomHTTPException):
    """Excepción lanzada cuando no se encuentra un producto en la base de datos."""

    def __init__(self, product_id: int):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with ID {product_id} not found",
            error_code=ErrorCode.PRODUCT_NOT_FOUND
        )


class RecommendationException(CustomHTTPException):
    """Excepción lanzada cuando ocurre un error al generar recomendaciones."""

    def __init__(
        self, message: str = "An error occurred while generating recommendations"
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=message,
            error_code=ErrorCode.RECOMMENDATION_ERROR
        )


class DuplicateEntryError(CustomHTTPException):
    """Excepción para entradas duplicadas en la base de datos"""

    def __init__(
        self,
        detail: str = "Ya existe un registro con estos datos",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code=ErrorCode.DUPLICATE_ENTRY
        )
        self.headers = headers


class ValidationError(CustomHTTPException):
    """Excepción para errores de validación"""

    def __init__(
        self,
        detail: str = "Error de validación",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code=ErrorCode.VALIDATION_ERROR
        )
        self.headers = headers


class NotFoundError(CustomHTTPException):
    """Excepción para recursos no encontrados"""

    def __init__(
        self,
        detail: str = "Recurso no encontrado",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code=ErrorCode.RESOURCE_NOT_FOUND
        )
        self.headers = headers


class AuthenticationError(CustomHTTPException):
    """Excepción para errores de autenticación"""

    def __init__(
        self,
        detail: str = "Error de autenticación",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code=ErrorCode.INVALID_CREDENTIALS
        )
        self.headers = headers


class AuthorizationError(CustomHTTPException):
    """Excepción para errores de autorización"""

    def __init__(
        self,
        detail: str = "No tiene permisos para realizar esta acción",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code=ErrorCode.INSUFFICIENT_PERMISSIONS
        )
        self.headers = headers


class PermissionDeniedError(CustomHTTPException):
    """Excepción para permisos denegados específicos"""

    def __init__(
        self,
        detail: str = "Permiso denegado para realizar esta acción",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code=ErrorCode.INSUFFICIENT_PERMISSIONS
        )
        self.headers = headers


class ConflictError(CustomHTTPException):
    """Excepción para conflictos de recursos"""

    def __init__(
        self,
        detail: str = "Conflicto con el recurso existente",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code=ErrorCode.CONFLICT
        )
        self.headers = headers


class InternalServerError(CustomHTTPException):
    """Excepción para errores internos del servidor"""

    def __init__(
        self,
        detail: str = "Error interno del servidor",
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code=ErrorCode.INTERNAL_ERROR
        )
        self.headers = headers


def handle_exceptions(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            log_error(f"Error in {func.__name__}: {str(e)}")
            raise CustomHTTPException(
                status_code=500,
                detail=str(e),
                error_code=ErrorCode.INTERNAL_ERROR
            )

    return wrapper


class ModelNotTrainedError(Exception):
    """Excepción para indicar que el modelo aún no ha sido entrenado."""

    def __init__(self, message="Model is not trained yet."):
        self.error_code = ErrorCode.MODEL_NOT_TRAINED
        super().__init__(message)


class ModelNotFoundError(CustomHTTPException):
    """Excepción lanzada cuando no se encuentra un modelo en la base de datos."""

    def __init__(self, model_id: int = None):
        message = f"Model with ID {model_id} not found" if model_id else "Model not found"
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=message,
            error_code=ErrorCode.MODEL_NOT_FOUND
        )


class TrainingError(CustomHTTPException):
    """Excepción lanzada cuando ocurre un error durante el entrenamiento del modelo."""

    def __init__(self, message: str = "An error occurred during model training"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=message,
            error_code=ErrorCode.TRAINING_ERROR
        )


class PartitionError(CustomHTTPException):
    """Excepción base para errores relacionados con particiones."""

    def __init__(self, message: str = "Error managing database partitions"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=message,
            error_code=ErrorCode.PARTITION_ERROR
        )


class PartitionCreationError(PartitionError):
    """Excepción para errores en la creación de particiones."""

    def __init__(self, partition_name: str, error: str):
        super().__init__(message=f"Error creating partition {partition_name}: {error}")


class TenantResourceLimitError(CustomHTTPException):
    """Excepción para límites de recursos de inquilinos."""

    def __init__(self, account_id: int, detail: str):
        super().__init__(
            status_code=400,
            detail={
                "account_id": account_id,
                "message": detail,
                "error_code": ErrorCode.RESOURCE_LIMIT_EXCEEDED
            }
        )


class TenantSubscriptionError(CustomHTTPException):
    """Excepción para errores de suscripción de inquilinos."""

    def __init__(self, account_id: int, detail: str):
        super().__init__(
            status_code=400,
            detail={
                "account_id": account_id,
                "message": detail,
                "error_code": ErrorCode.SUBSCRIPTION_LIMIT
            }
        )


class LimitExceededError(CustomHTTPException):
    """Excepción lanzada cuando se excede un límite de recursos."""

    def __init__(self, message: str = "Resource limit exceeded", retry_after: int = 60):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=message,
            error_code=ErrorCode.RESOURCE_LIMIT_EXCEEDED
        )
        self.retry_after = retry_after


class EmailNotVerifiedError(CustomHTTPException):
    """Excepción lanzada cuando se intenta acceder a un recurso con un email no verificado."""

    def __init__(self, message: str = "Email not verified. Please verify your email address."):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=message,
            error_code=ErrorCode.EMAIL_NOT_VERIFIED
        )


def handle_partition_errors(func):
    """Decorador para manejar errores de particionamiento."""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except PartitionError as e:
            log_error(f"Partition error in {func.__name__}: {str(e)}")
            raise
        except Exception as e:
            log_error(f"Unexpected error in {func.__name__}: {str(e)}")
            raise PartitionError(
                f"Unexpected error during partition operation: {str(e)}"
            )

    return wrapper


class InvalidAPIKeyError(Exception):
    """Excepción cuando una API Key es inválida o no está autorizada."""
    def __init__(self, message: str = "API Key inválida o no autorizada."):
        self.message = message
        super().__init__(self.message)

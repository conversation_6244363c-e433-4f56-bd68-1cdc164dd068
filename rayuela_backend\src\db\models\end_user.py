from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    DateTime,
    Foreign<PERSON>ey,
    Boolean,
    UniqueConstraint,
    Index,
    func,
    PrimaryKeyConstraint,
    Identity,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import get_tenant_table_args


class EndUser(Base):
    __tablename__ = "end_users"

    user_id = Column(Integer, Identity(), primary_key=True)
    account_id = Column(Integer, ForeignKey("accounts.account_id"), primary_key=True)
    external_id = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True)
    last_activity_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Timestamp of last activity by this end user",
    )

    # Cold start preference fields
    preferred_categories = Column(
        JSONB,
        nullable=True,
        comment="List of preferred product categories for cold start recommendations"
    )
    disliked_categories = Column(
        JSONB,
        nullable=True,
        comment="List of disliked product categories to avoid in recommendations"
    )
    preferred_brands = Column(
        JSONB,
        nullable=True,
        comment="List of preferred brands for cold start recommendations"
    )
    disliked_brands = Column(
        JSONB,
        nullable=True,
        comment="List of disliked brands to avoid in recommendations"
    )
    price_range_min = Column(
        Integer,
        nullable=True,
        comment="Minimum price preference for recommendations"
    )
    price_range_max = Column(
        Integer,
        nullable=True,
        comment="Maximum price preference for recommendations"
    )
    demographic_info = Column(
        JSONB,
        nullable=True,
        comment="Demographic information (age_group, gender, location, etc.) for cold start"
    )
    onboarding_preferences = Column(
        JSONB,
        nullable=True,
        comment="Additional preferences collected during onboarding process"
    )
    is_active = Column(Boolean, default=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("user_id", "account_id"),
        UniqueConstraint("account_id", "external_id", name="uq_end_user_external_id"),
        Index("idx_end_user_preferred_categories", "preferred_categories", postgresql_using="gin"),
        Index("idx_end_user_price_range", "account_id", "price_range_min", "price_range_max"),
    )

    # Relationships
    account = relationship("Account", back_populates="end_users")
    interactions = relationship(
        "Interaction",
        back_populates="end_user",
    )
    recommendations = relationship(
        "Recommendation",
        back_populates="end_user",
    )
    searches = relationship("Search", back_populates="end_user")
    orders = relationship("Order", back_populates="end_user")

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, ForeignKey
from sqlalchemy.orm import relationship, declared_attr

ACCOUNT_ID_FK = "accounts.account_id"

ACCOUNT_RANGE = "RANGE (account_id)"


def get_tenant_table_args(*additional_args):
    """
    Helper function para crear __table_args__ consistentes para modelos tenant-scoped.
    
    Args:
        *additional_args: Constraints, índices, y diccionarios de configuración adicionales
        
    Returns:
        tuple: __table_args__ que incluye particionamiento y elementos adicionales
    """
    # Separar argumentos que son objetos SQLAlchemy de los diccionarios de configuración
    schema_items = []
    config_dicts = {}
    
    for arg in additional_args:
        if isinstance(arg, dict):
            # Combinar diccionarios de configuración
            config_dicts.update(arg)
        else:
            # Objetos SQLAlchemy (constraints, índices, etc.)
            schema_items.append(arg)
    
    # Agregar la configuración de particionamiento
    config_dicts["postgresql_partition_by"] = ACCOUNT_RANGE
    
    # Retornar la tupla con todos los elementos + el diccionario combinado
    return tuple(schema_items) + (config_dicts,)


class TenantMixin:
    """Mixin para modelos con soporte multi-tenant"""

    @declared_attr
    def account_id(cls):
        """
        Columna account_id que es parte de la PK y clave de particionamiento.
        También es una Foreign Key a la tabla accounts.
        """
        return Column(
            Integer, ForeignKey(ACCOUNT_ID_FK, ondelete="CASCADE"), nullable=False
        )

    @declared_attr
    def account(cls):
        """
        Relación con el modelo Account.
        """
        return relationship(
            "Account",
            back_populates=cls.__tablename__,
            foreign_keys=[cls.account_id],
        )

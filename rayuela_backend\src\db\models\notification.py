from sqlalchemy import (
    Column,
    Integer,
    Text,
    Boolean,
    DateTime,
    Enum,
    Index,
    PrimaryKeyConstraint,
    func,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import NotificationType
from .mixins import TenantMixin, get_tenant_table_args


class Notification(Base, TenantMixin):
    __tablename__ = "notifications"

    # La otra parte de la PK compuesta
    id = Column(Integer, Identity(), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    message = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())

    # Definición explícita de la PK compuesta e índices
    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_notification_account", "account_id"),
    )

    # Relaciones
    account = relationship("Account", back_populates="notifications")

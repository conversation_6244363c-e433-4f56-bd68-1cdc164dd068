from sqlalchemy import (
    Column,
    Integer,
    String,
    ForeignKey,
    DECIMAL,
    Index,
    func,
    DateTime,
    PrimaryKeyConstraint,
    ForeignKeyConstraint,
)
from sqlalchemy.orm import relationship

from src.db.base import Base
from .mixins import get_tenant_table_args


class OrderItem(Base):
    """
    Modelo para representar elementos individuales de una orden.
    """

    __tablename__ = "order_items"

    account_id = Column(Integer, ForeignKey("accounts.account_id"), primary_key=True)
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, nullable=False, index=True)
    product_id = Column(Integer, nullable=False, index=True)

    # Información del item
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(DECIMAL(10, 2), nullable=False)
    total_price = Column(DECIMAL(10, 2), nullable=False)

    # Timestamps estándar
    created_at = Column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=func.now(),
        server_default=func.now(),
        onupdate=func.now(),
    )

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FK for order_id to ensure tenant isolation
        ForeignKeyConstraint(
            ["account_id", "order_id"],
            ["orders.account_id", "orders.id"],
            ondelete="CASCADE",
            name="fk_order_item_order",
        ),
        # Composite FK for product_id to ensure tenant isolation
        ForeignKeyConstraint(
            ["product_id", "account_id"],
            ["products.product_id", "products.account_id"],
            ondelete="CASCADE",
            name="fk_order_item_product",
        ),
        Index("idx_order_item_account_order", "account_id", "order_id"),
        Index("idx_order_item_account_product", "account_id", "product_id"),
    )

    # Relaciones
    account = relationship("Account")
    order = relationship(
        "Order",
        foreign_keys=[account_id, order_id],
        primaryjoin="and_(OrderItem.account_id==Order.account_id, OrderItem.order_id==Order.id)",
        back_populates="order_items",
    )
    product = relationship(
        "Product",
        foreign_keys=[product_id, account_id],
        primaryjoin="and_(OrderItem.product_id==Product.product_id, OrderItem.account_id==Product.account_id)",
        back_populates="order_items",
    )

    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id={self.order_id}, product_id={self.product_id}, quantity={self.quantity}, total_price={self.total_price})>"

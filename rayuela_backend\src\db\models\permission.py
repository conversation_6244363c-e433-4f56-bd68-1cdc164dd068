from sqlalchemy import Column, Integer, String, Index, PrimaryKeyConstraint, Identity
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, get_tenant_table_args


class Permission(Base, TenantMixin):
    """Modelo para permisos con soporte multi-tenant"""

    __tablename__ = "permissions"

    # La otra parte de la PK compuesta
    id = Column(Integer, Identity(), nullable=False)

    # Columnas adicionales
    name = Column(String(255), nullable=False)
    description = Column(String(255))

    # Relaciones
    roles = relationship("Role", secondary="role_permissions", back_populates="permissions")

    # Definición explícita de la PK compuesta e índices
    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_permission_name", "name"),
        Index("idx_permission_account", "account_id"),
    )

from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Text,
    Index,
    func,
    PrimaryKeyConstraint,
    Identity,
    Boolean,
    Float,
    DECIMAL,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import get_tenant_table_args


class Product(Base):
    __tablename__ = "products"

    product_id = Column(Integer, Identity(), primary_key=True)
    account_id = Column(Integer, ForeignKey("accounts.account_id"), primary_key=True)
    external_id = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    category = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    price = Column(DECIMAL(10, 2), nullable=False)
    average_rating = Column(Float, nullable=True, default=0.0)
    num_ratings = Column(Integer, nullable=True, default=0)
    inventory_count = Column(Integer, nullable=True, default=0)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True)
    search_vector = Column(Text, nullable=True)
    name_trgm = Column(Text, nullable=True)
    description_trgm = Column(Text, nullable=True)
    last_interaction_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Timestamp of last interaction with this product",
    )
    is_active = Column(Boolean, default=True, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    # Enhanced product features for content-based recommendations
    features = Column(
        JSONB,
        nullable=True,
        comment="Structured product attributes (tags, colors, brand, review_summary, questions_and_answers, etc.) for enhanced content-based recommendations"
    )

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("product_id", "account_id"),
        UniqueConstraint("account_id", "external_id", name="uq_product_external_id"),
        Index("idx_product_account_category", "account_id", "category"),
        Index("idx_product_account_name", "account_id", "name"),
        Index("idx_product_external_id", "account_id", "external_id"),
        Index("idx_product_search_vector", "search_vector", postgresql_using="gin"),
        Index("idx_product_name_trgm", "name_trgm", postgresql_using="gin"),
        Index(
            "idx_product_description_trgm", "description_trgm", postgresql_using="gin"
        ),
        Index(
            "idx_products_account_last_interaction", "account_id", "last_interaction_at"
        ),
        # GIN index for JSONB features to enable efficient querying
        Index("idx_product_features", "features", postgresql_using="gin"),
    )

    # Relationships
    account = relationship("Account", back_populates="products")
    interactions = relationship(
        "Interaction",
        back_populates="product",
    )
    recommendations = relationship(
        "Recommendation",
        back_populates="product",
    )
    order_items = relationship("OrderItem", back_populates="product")

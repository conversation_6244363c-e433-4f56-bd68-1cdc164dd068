from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Boolean,
    Index,
    PrimaryKeyConstraint,
    func,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, get_tenant_table_args


class SystemUser(Base, TenantMixin):
    __tablename__ = "system_users"

    # La otra parte de la PK compuesta
    id = Column(Integer, Identity(), nullable=False)

    # Columnas adicionales
    email = Column(String(255), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    created_at = Column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=func.now(),
        server_default=func.now(),
        onupdate=func.now(),
    )
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    last_login_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Timestamp of last login by this user",
    )
    email_verified = Column(Boolean, default=False)
    verification_token = Column(String(255), nullable=True)
    verification_token_expires_at = Column(DateTime(timezone=True), nullable=True)

    # Definición explícita de la PK compuesta e índices
    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        # UNIQUE constraint global en email para prevenir condiciones de carrera
        Index("idx_system_user_email_global", "email", unique=True),
        # Índice adicional para consultas por account_id
        Index("idx_system_user_account", "account_id"),
        # Índice para operaciones de limpieza
        Index("idx_system_users_account_last_login", "account_id", "last_login_at"),
        {
            "comment": "Emails are globally unique across all accounts. This is enforced at the database level to prevent race conditions."
        },
    )

    # Relaciones
    account = relationship("Account", back_populates="system_users")
    system_user_roles = relationship(
        "SystemUserRole",
        back_populates="system_user",
        foreign_keys="[SystemUserRole.system_user_id]",
        primaryjoin="and_(SystemUser.account_id==SystemUserRole.account_id, SystemUser.id==SystemUserRole.system_user_id)",
        passive_deletes=True,
    )
    roles = relationship(
        "Role",
        secondary="system_user_roles",
        back_populates="users",
        viewonly=True,  # Avoids SQLAlchemy managing the association table from this side.
        # Use SystemUserRole relationship to manage the association.
    )

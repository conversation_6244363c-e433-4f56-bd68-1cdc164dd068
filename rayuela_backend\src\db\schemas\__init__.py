# src/db/schemas/__init__.py
from .base import CamelCaseModel
from .account import AccountBase, AccountCreate, AccountUpdate, AccountResponse
from .subscription import SubscriptionBase, SubscriptionCreate, SubscriptionUpdate, Subscription
from .end_user import <PERSON><PERSON><PERSON>B<PERSON>, EndUserCreate, EndUserUpdate, EndUser
from .system_user import SystemUserBase, SystemUserCreate, SystemUserUpdate, SystemUser, SystemUserResponse
from .role_permission import RoleBase, RoleCreate, Role, PermissionCreate, Permission
from .product import ProductBase, ProductCreate, ProductUpdate, Product, InventoryUpdate
from .category import Category
from .interaction import InteractionBase, InteractionCreate, Interaction
from .search import SearchBase, SearchCreate, Search
from .audit import AuditLogBase, AuditLogCreate, AuditLog, UsageStats
from .notification import NotificationBase, NotificationCreate, Notification
from .artifact import (
    ModelMetadataBase,
    ModelMetadataCreate,
    ModelMetadata,
    ModelMetadataResponse,
    TrainingJobBase,
    TrainingJobCreate,
    TrainingJob,
)
from .pipeline import TrainingResponse, TrainingStatus, TrainingJobStatus
from .auth import Token, TokenPayload, TokenData, RegisterRequest, LoginRequest
from .common import PaginatedResponse
from .data_ingestion import BatchIngestionRequest, BatchIngestionResponse
from .batch_ingestion_job import (
    BatchIngestionJobBase,
    BatchIngestionJobCreate,
    BatchIngestionJob,
    BatchIngestionJobResponse,
    BatchIngestionJobStatus
)
from .billing import (
    CreateCheckoutSessionRequest,
    CreateCheckoutSessionResponse,
    CreatePortalSessionRequest,
    CreatePortalSessionResponse,
    StripeWebhookEvent
)
from .recommendation import (
    RecommendationStrategy, RecommendationStrategyParams, STRATEGY_CONFIGS,
    PageType, DeviceType, RecommendationContext,
    FilterOperator, Filter, LogicalOperator, FilterGroup,
    ExplanationLevel, ExplanationReason, ExplanationEvidence, DetailedExplanation,
    StandardQueryParams, SortConfig, SortDirection
)
from .recommendation_query import RecommendationQueryRequest
from .usage_summary import (
    UsageSummaryResponse,
    ApiCallsUsage,
    StorageUsage,
    TrainingUsage,
    PlanLimits,
    PlanFeatures,
    PlanInfo,
    SubscriptionInfo
)
from .analytics import (
    RecommendationPerformanceResponse,
    ConfidenceMetricsResponse,
    ModelComparisonResponse,
    MetricsHistoryResponse,
    OfflineMetrics,
    OnlineMetrics,
    ModelMetrics,
    ConfidenceDistribution
)

__all__ = [
    "CamelCaseModel",
    "AccountBase",
    "AccountCreate",
    "AccountUpdate",
    "AccountResponse",
    "SubscriptionBase",
    "SubscriptionCreate",
    "SubscriptionUpdate",
    "Subscription",
    "SystemUserBase",
    "SystemUserCreate",
    "SystemUserUpdate",
    "SystemUser",
    "SystemUserResponse",
    "EndUserBase",
    "EndUserCreate",
    "EndUserUpdate",
    "EndUser",
    "RoleBase",
    "RoleCreate",
    "Role",
    "PermissionCreate",
    "Permission",
    "ProductBase",
    "ProductCreate",
    "ProductUpdate",
    "Product",
    "InventoryUpdate",
    "Category",
    "InteractionBase",
    "InteractionCreate",
    "Interaction",
    "SearchBase",
    "SearchCreate",
    "Search",
    "AuditLogBase",
    "AuditLogCreate",
    "AuditLog",
    "UsageStats",
    "NotificationBase",
    "NotificationCreate",
    "Notification",
    "ModelMetadataBase",
    "ModelMetadataCreate",
    "ModelMetadata",
    "ModelMetadataResponse",
    "TrainingJobBase",
    "TrainingJobCreate",
    "TrainingJob",
    "TrainingResponse",
    "TrainingStatus",
    "TrainingJobStatus",
    "Token",
    "TokenPayload",
    "TokenData",
    "RegisterRequest",
    "LoginRequest",
    "PaginatedResponse",
    "BatchIngestionRequest",
    "BatchIngestionResponse",
    "BatchIngestionJobBase",
    "BatchIngestionJobCreate",
    "BatchIngestionJob",
    "BatchIngestionJobResponse",
    "BatchIngestionJobStatus",
    "CreateCheckoutSessionRequest",
    "CreateCheckoutSessionResponse",
    "CreatePortalSessionRequest",
    "CreatePortalSessionResponse",
    "StripeWebhookEvent",
    "RecommendationStrategy",
    "RecommendationStrategyParams",
    "STRATEGY_CONFIGS",
    "PageType",
    "DeviceType",
    "RecommendationContext",
    "FilterOperator",
    "Filter",
    "LogicalOperator",
    "FilterGroup",
    "ExplanationLevel",
    "ExplanationReason",
    "ExplanationEvidence",
    "DetailedExplanation",
    "RecommendationQueryRequest",
    "StandardQueryParams",
    "SortConfig",
    "SortDirection",
    # Usage Summary
    "UsageSummaryResponse",
    "ApiCallsUsage",
    "StorageUsage",
    "TrainingUsage",
    "PlanLimits",
    "PlanFeatures",
    "PlanInfo",
    "SubscriptionInfo",
    # Analytics
    "RecommendationPerformanceResponse",
    "ConfidenceMetricsResponse",
    "ModelComparisonResponse",
    "MetricsHistoryResponse",
    "OfflineMetrics",
    "OnlineMetrics",
    "ModelMetrics",
    "ConfidenceDistribution",
]

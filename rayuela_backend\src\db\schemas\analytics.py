"""
Schemas for analytics and recommendation metrics data.
"""
from pydantic import Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from .base import CamelCaseModel


class ModelMetrics(CamelCaseModel):
    """Individual model metrics"""
    model_id: int = Field(..., description="Model ID")
    model_type: str = Field(..., description="Model type (collaborative, content, hybrid)")
    version: str = Field("v1", description="Model version")
    precision: float = Field(0.0, description="Precision metric")
    recall: float = Field(0.0, description="Recall metric")
    ndcg: float = Field(0.0, description="NDCG metric")
    map_score: float = Field(0.0, description="MAP score", alias="map")
    catalog_coverage: float = Field(0.0, description="Catalog coverage")
    diversity: float = Field(0.0, description="Diversity metric")
    novelty: float = Field(0.0, description="Novelty metric")
    serendipity: float = Field(0.0, description="Serendipity metric")

    model_config = {"protected_namespaces": ()}


class OfflineMetrics(CamelCaseModel):
    """Offline metrics data"""
    precision: float = Field(0.0, description="Overall precision")
    recall: float = Field(0.0, description="Overall recall")
    ndcg: float = Field(0.0, description="Overall NDCG")
    map_score: float = Field(0.0, description="Overall MAP", alias="map")
    catalog_coverage: float = Field(0.0, description="Overall catalog coverage")
    diversity: float = Field(0.0, description="Overall diversity")
    novelty: float = Field(0.0, description="Overall novelty")
    serendipity: float = Field(0.0, description="Overall serendipity")
    models: List[ModelMetrics] = Field(default_factory=list, description="Individual model metrics")


class OnlineMetrics(CamelCaseModel):
    """Online metrics data"""
    ctr: float = Field(0.0, description="Click-through rate")
    cvr: float = Field(0.0, description="Conversion rate")
    user_engagement: float = Field(0.0, description="User engagement score")
    session_duration: float = Field(0.0, description="Average session duration")
    bounce_rate: float = Field(0.0, description="Bounce rate")


class ConfidenceDistribution(CamelCaseModel):
    """Confidence distribution for a model type"""
    avg: float = Field(0.0, description="Average confidence")
    low_confidence_count: int = Field(0, description="Number of low confidence predictions")
    medium_confidence_count: int = Field(0, description="Number of medium confidence predictions")
    high_confidence_count: int = Field(0, description="Number of high confidence predictions")


class ConfidenceTrendPoint(CamelCaseModel):
    """Single point in confidence trend"""
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    confidence: float = Field(0.0, description="Confidence value")


class ConfidenceTrends(CamelCaseModel):
    """Confidence trends over time"""
    last_7_days: List[ConfidenceTrendPoint] = Field(default_factory=list, description="Last 7 days trend")


class RecommendationPerformanceResponse(CamelCaseModel):
    """Complete recommendation performance response"""
    offline_metrics: OfflineMetrics = Field(..., description="Offline metrics")
    online_metrics: OnlineMetrics = Field(..., description="Online metrics")
    model_id: Optional[int] = Field(None, description="Specific model ID if filtered")

    model_config = {"protected_namespaces": ()}


class ConfidenceMetricsResponse(CamelCaseModel):
    """Complete confidence metrics response"""
    summary: Dict[str, float] = Field(default_factory=dict, description="Summary metrics")
    confidence_distribution: Dict[str, ConfidenceDistribution] = Field(
        default_factory=dict, 
        description="Confidence distribution by model type"
    )
    confidence_trends: ConfidenceTrends = Field(..., description="Confidence trends")
    category_confidence: Dict[str, float] = Field(
        default_factory=dict, 
        description="Confidence by category"
    )
    confidence_factors: Dict[str, float] = Field(
        default_factory=dict, 
        description="Factors affecting confidence"
    )


class ModelComparisonMetrics(CamelCaseModel):
    """Metrics for model comparison"""
    model_id: int = Field(..., description="Model ID")
    model_name: str = Field(..., description="Model name")
    created_at: datetime = Field(..., description="Creation date")
    metrics: Dict[str, float] = Field(default_factory=dict, description="All metrics")
    performance_score: float = Field(0.0, description="Overall performance score")

    model_config = {"protected_namespaces": ()}


class ModelComparisonResponse(CamelCaseModel):
    """Model comparison response"""
    models: List[ModelComparisonMetrics] = Field(default_factory=list, description="Models being compared")
    comparison_matrix: Dict[str, Dict[str, float]] = Field(
        default_factory=dict, 
        description="Comparison matrix between models"
    )
    best_model: Optional[ModelComparisonMetrics] = Field(None, description="Best performing model")
    improvements: Dict[str, str] = Field(
        default_factory=dict, 
        description="Improvement suggestions"
    )


class MetricsHistoryPoint(CamelCaseModel):
    """Single point in metrics history"""
    timestamp: datetime = Field(..., description="Timestamp")
    value: float = Field(..., description="Metric value")
    model_id: Optional[int] = Field(None, description="Associated model ID")

    model_config = {"protected_namespaces": ()}


class MetricsHistoryResponse(CamelCaseModel):
    """Metrics history response"""
    metric_name: str = Field(..., description="Name of the metric")
    data_points: List[MetricsHistoryPoint] = Field(default_factory=list, description="Historical data points")
    trend: str = Field("stable", description="Trend direction (improving, declining, stable)")
    average_value: float = Field(0.0, description="Average value over the period")

    class ConfigDict:
        from_attributes = True 
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Any

from .recommendation import FilterGroup, RecommendationContext, SortConfig


class RecommendationQueryRequest(BaseModel):
    """
    Esquema para solicitudes de recomendaciones con filtros complejos.
    Permite especificar filtros estructurados y otros parámetros en el cuerpo de la solicitud.
    """
    user_id: int = Field(..., gt=0, description="ID del usuario para el que se generan recomendaciones")
    filters: Optional[FilterGroup] = Field(None, description="Filtros estructurados para aplicar a las recomendaciones")
    context: Optional[RecommendationContext] = Field(None, description="Contexto explícito para filtrar o re-rankear recomendaciones")
    strategy: Optional[str] = Field(None, description="Estrategia de recomendación a utilizar")
    model_type: str = Field("standard", description="Tipo de modelo a utilizar")
    include_explanation: bool = Field(False, description="Incluir explicación de la recomendación")
    sort_by: Optional[SortConfig] = Field(None, description="Configuración de ordenamiento")
    skip: int = Field(0, ge=0, description="Número de elementos a saltar")
    limit: int = Field(10, gt=0, le=100, description="Número máximo de elementos a devolver")

    model_config = ConfigDict(
        protected_namespaces=(),  # Permite el uso de model_type sin conflicto
        json_schema_extra={
            "example": {
                "user_id": 123,
                "filters": {
                    "logic": "and",
                    "filters": [
                        {
                            "field": "price",
                            "op": "lt",
                            "value": 50
                        },
                        {
                            "field": "category",
                            "op": "eq",
                            "value": "electronics"
                        }
                    ]
                },
                "context": {
                    "page_type": "product_detail",
                    "device": "mobile",
                    "source_item_id": 456
                },
                "model_type": "standard",
                "strategy": "balanced",
                "include_explanation": True,
                "skip": 0,
                "limit": 10
            }
        }
    )


class RecommendationQueryExternalRequest(BaseModel):
    """
    Esquema para solicitudes de recomendaciones usando external_user_id.
    Permite a los clientes usar sus propios identificadores de usuario.
    """
    external_user_id: str = Field(..., description="ID externo del usuario proporcionado por el cliente")
    filters: Optional[FilterGroup] = Field(None, description="Filtros estructurados para aplicar a las recomendaciones")
    context: Optional[RecommendationContext] = Field(None, description="Contexto explícito para filtrar o re-rankear recomendaciones")
    strategy: Optional[str] = Field(None, description="Estrategia de recomendación a utilizar")
    model_type: str = Field("standard", description="Tipo de modelo a utilizar")
    include_explanation: bool = Field(False, description="Incluir explicación de la recomendación")
    sort_by: Optional[SortConfig] = Field(None, description="Configuración de ordenamiento")
    skip: int = Field(0, ge=0, description="Número de elementos a saltar")
    limit: int = Field(10, gt=0, le=100, description="Número máximo de elementos a devolver")

    model_config = ConfigDict(
        protected_namespaces=(),
        json_schema_extra={
            "example": {
                "external_user_id": "user_abc123",
                "filters": {
                    "logic": "and",
                    "filters": [
                        {
                            "field": "price",
                            "op": "lt",
                            "value": 50
                        },
                        {
                            "field": "category",
                            "op": "eq",
                            "value": "electronics"
                        }
                    ]
                },
                "context": {
                    "page_type": "product_detail",
                    "device": "mobile",
                    "source_external_product_id": "prod_xyz789"
                },
                "model_type": "standard",
                "strategy": "balanced",
                "include_explanation": True,
                "skip": 0,
                "limit": 10
            }
        }
    )

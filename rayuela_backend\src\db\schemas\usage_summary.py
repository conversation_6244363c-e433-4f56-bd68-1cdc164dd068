"""
Schemas for usage summary data.
"""
from pydantic import Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from .base import CamelCaseModel


class ApiCallsUsage(CamelCaseModel):
    """API calls usage information"""
    used: int = Field(0, description="Number of API calls used")
    limit: int = Field(10000, description="API calls limit")
    percentage: float = Field(0.0, description="Percentage of limit used")
    reset_date: Optional[datetime] = Field(None, description="Date when usage resets")


class StorageUsage(CamelCaseModel):
    """Storage usage information"""
    used_bytes: int = Field(0, description="Storage used in bytes")
    limit_bytes: int = Field(1000000, description="Storage limit in bytes")
    percentage: float = Field(0.0, description="Percentage of limit used")
    last_measured: Optional[str] = Field(None, description="Last measurement timestamp")
    source: Optional[str] = Field(None, description="Source of measurement (redis_cache, database_calculation)")


class TrainingUsage(CamelCaseModel):
    """Training usage information"""
    last_training_date: Optional[datetime] = Field(None, description="Date of last training")
    frequency_limit: str = Field("manual", description="Training frequency limit")
    next_available: Optional[datetime] = Field(None, description="Next available training time")
    can_train_now: bool = Field(True, description="Whether training is available now")


class PlanLimits(CamelCaseModel):
    """Plan limits information"""
    api_calls: int = Field(10000, description="API calls limit")
    storage_bytes: int = Field(1000000, description="Storage limit in bytes")
    storage_mb: float = Field(1.0, description="Storage limit in MB")
    storage_gb: float = Field(0.001, description="Storage limit in GB")
    max_requests_per_minute: int = Field(60, description="Rate limit per minute")
    max_concurrent_requests: int = Field(10, description="Concurrent requests limit")
    training_frequency: str = Field("manual", description="Training frequency")
    training_data_limit: int = Field(0, description="Training data limit")
    training_data_limit_formatted: str = Field("Unlimited", description="Formatted training data limit")
    max_items: int = Field(0, description="Maximum items")
    max_items_formatted: str = Field("Unlimited", description="Formatted max items")
    max_users: int = Field(0, description="Maximum users")
    max_users_formatted: str = Field("Unlimited", description="Formatted max users")
    recommendation_cache_ttl: int = Field(3600, description="Cache TTL in seconds")


class PlanFeatures(CamelCaseModel):
    """Plan features information"""
    available_models: List[str] = Field(default_factory=list, description="Available models")


class PlanInfo(CamelCaseModel):
    """Plan information"""
    id: str = Field(..., description="Plan ID")
    limits: PlanLimits = Field(..., description="Plan limits")
    features: PlanFeatures = Field(..., description="Plan features")


class SubscriptionInfo(CamelCaseModel):
    """Subscription information"""
    plan: str = Field(..., description="Plan type")
    is_active: bool = Field(True, description="Whether subscription is active")
    expires_at: Optional[datetime] = Field(None, description="Expiration date")


class UsageSummaryResponse(CamelCaseModel):
    """Complete usage summary response"""
    subscription: SubscriptionInfo = Field(..., description="Subscription information")
    api_calls: ApiCallsUsage = Field(..., description="API calls usage")
    storage: StorageUsage = Field(..., description="Storage usage")
    training: TrainingUsage = Field(..., description="Training usage")
    plan_limits: PlanLimits = Field(..., description="Current plan limits")
    all_plans: Dict[str, PlanInfo] = Field(default_factory=dict, description="All available plans")
    billing_portal_url: Optional[str] = Field(None, description="Billing portal URL")

    class ConfigDict:
        from_attributes = True 
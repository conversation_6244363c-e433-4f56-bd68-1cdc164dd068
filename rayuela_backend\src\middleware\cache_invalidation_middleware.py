from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from src.utils.base_logger import log_info, log_error, log_warning
from src.db.session import get_db
from src.services.cache_service import get_cache_service
from src.core.auth_utils import get_user_and_account_ids
from typing import Optional, Callable
import asyncio


class CacheInvalidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware para invalidación de caché como último recurso.
    
    Este middleware solo debe usarse cuando los servicios de negocio no pueden
    invalidar la caché explícitamente. Se recomienda que los servicios llamen
    directamente al CacheService centralizado.
    """

    def __init__(self, app):
        super().__init__(app)
        self.interaction_endpoints = [
            "/api/v1/interactions",  # Endpoint para registrar interacciones
            "/api/v1/ratings",  # Endpoint para registrar ratings
            "/api/v1/purchases",  # Endpoint para registrar compras
        ]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Procesar la solicitud
        response = await call_next(request)

        # Solo procesar POST/PUT/PATCH a endpoints de interacción
        if request.method in ["POST", "PUT", "PATCH"] and any(
            request.url.path.startswith(endpoint)
            for endpoint in self.interaction_endpoints
        ):
            # Solo invalidar si la respuesta fue exitosa
            if response.status_code < 400:
                # Procesar en segundo plano para no bloquear la respuesta
                asyncio.create_task(self._process_cache_invalidation(request))

        return response

    async def _process_cache_invalidation(self, request: Request) -> None:
        """
        Procesa la invalidación de caché en segundo plano como último recurso.
        
        Este método solo debe ejecutarse cuando los servicios de negocio no han
        invalidado la caché explícitamente.
        """
        try:
            async with get_db() as db:
                # Obtener información de autenticación
                user_id, account_id = await get_user_and_account_ids(request, db)
                
                if not account_id:
                    log_warning(f"No se pudo determinar account_id para invalidar caché en {request.url.path}")
                    return
                
                # Usar el servicio centralizado de caché
                cache_service = await get_cache_service()
                
                if user_id:
                    # Si tenemos user_id específico, invalidar solo sus recomendaciones
                    success = await cache_service.invalidate_user_recommendations(account_id, user_id)
                    if success:
                        log_info(f"Caché de usuario invalidada por middleware para user_id={user_id}, account_id={account_id}")
                    else:
                        log_error(f"Error invalidando caché de usuario por middleware para user_id={user_id}, account_id={account_id}")
                else:
                    # Si no tenemos user_id específico, intentar extraerlo del cuerpo
                    extracted_user_id = await self._extract_user_id_from_request(request)
                    
                    if extracted_user_id:
                        success = await cache_service.invalidate_user_recommendations(account_id, extracted_user_id)
                        if success:
                            log_info(f"Caché de usuario invalidada por middleware (extraído) para user_id={extracted_user_id}, account_id={account_id}")
                    else:
                        # Como último recurso, invalidar toda la caché de la cuenta
                        success = await cache_service.invalidate_account_recommendations(account_id)
                        if success:
                            log_warning(f"Caché de cuenta invalidada por middleware como último recurso para account_id={account_id}")

        except Exception as e:
            # No interrumpir el flujo principal si hay un error
            log_error(f"Error invalidando caché en middleware para {request.url.path}: {str(e)}")

    async def _extract_user_id_from_request(self, request: Request) -> Optional[int]:
        """
        Intenta extraer el ID de usuario del cuerpo de la solicitud.
        
        Este método es un último recurso y solo debe usarse cuando los servicios
        de negocio no pueden proporcionar el user_id explícitamente.
        
        Args:
            request: Solicitud HTTP
            
        Returns:
            ID de usuario o None si no se pudo extraer
        """
        try:
            # Leer el cuerpo de la solicitud
            body = await request.json()
            
            # Buscar user_id en diferentes formatos posibles
            user_id = (
                body.get("user_id") or 
                body.get("end_user_id") or 
                body.get("userId") or
                body.get("endUserId")
            )
            
            if isinstance(user_id, int) and user_id > 0:
                return user_id
            elif isinstance(user_id, str) and user_id.isdigit():
                parsed_id = int(user_id)
                return parsed_id if parsed_id > 0 else None
                
        except Exception as e:
            log_warning(f"No se pudo extraer user_id del cuerpo de la solicitud: {str(e)}")
        
        return None

import uuid
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSO<PERSON>esponse
from typing import Callable
from src.utils.base_logger import log_info, log_error


class RequestIDMiddleware(BaseHTTPMiddleware):
    """
    Middleware que genera un request_id único para cada solicitud HTTP.
    
    Este middleware:
    1. Genera un UUID único para cada request
    2. Lo incluye en el contexto de la solicitud
    3. Lo propaga a los logs
    4. Lo incluye en las respuestas de error
    """
    
    def __init__(self, app, header_name: str = "X-Request-ID"):
        super().__init__(app)
        self.header_name = header_name
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generar request_id único o usar el existente si viene en headers
        request_id = request.headers.get(self.header_name.lower()) or str(uuid.uuid4())
        
        # Almacenar el request_id en el estado de la request
        request.state.request_id = request_id
        
        try:
            # Procesar la solicitud
            response = await call_next(request)
            
            # Añadir el request_id a la respuesta
            response.headers[self.header_name] = request_id
            
            return response
            
        except Exception as e:
            # Si hay una excepción no manejada, asegurar que el request_id se incluya
            log_error(f"Unhandled exception in request {request_id}: {str(e)}")
            
            # Crear respuesta de error con request_id
            error_response = JSONResponse(
                status_code=500,
                content={
                    "message": "Internal server error",
                    "error_code": "INTERNAL_ERROR",
                    "request_id": request_id
                }
            )
            error_response.headers[self.header_name] = request_id
            
            return error_response


def get_request_id(request: Request) -> str:
    """
    Obtiene el request_id de la solicitud actual.
    
    Args:
        request: Objeto Request de FastAPI
        
    Returns:
        request_id único de la solicitud
    """
    return getattr(request.state, 'request_id', 'unknown') 
from typing import Dict, Any, Tu<PERSON>, Union, List, Optional
import pandas as pd
import numpy as np
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import TruncatedSVD
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import Normalizer, StandardScaler
from sklearn.metrics.pairwise import cosine_similarity

from src.ml_pipeline.base_trainer import BaseTrainer
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.config import settings

import os
os.environ['TF_USE_LEGACY_KERAS'] = '1'
os.environ["TRANSFORMERS_NO_TF"] = "1"

from sentence_transformers import SentenceTransformer

class ContentTrainer(BaseTrainer):
    """Trainer para modelos basados en contenido con embeddings semánticos y atributos estructurados"""

    def __init__(self, account_id: int):
        super().__init__(account_id)
        self.default_params = {
            "vector_size": settings.CONTENT_VECTOR_SIZE,  # Dimensión final de los vectores combinados
            "max_features": 1000,  # Número máximo de características para TF-IDF (como respaldo)
            "random_state": 42,  # Para reproducibilidad
            "embedding_model": "paraphrase-multilingual-MiniLM-L12-v2",  # Modelo SBERT predeterminado
            "use_semantic_embeddings": True,  # Usar embeddings semánticos (SBERT)
            "use_structured_attributes": True,  # Usar atributos estructurados
            "attribute_weight": 0.3,  # Peso de los atributos estructurados en la combinación
            "text_weight": 0.7,  # Peso del texto en la combinación
        }
        self.tfidf_vectorizer = None
        self.sentence_transformer = None
        self.attribute_encoder = None
        self.scaler = None

    def get_required_columns(self) -> list:
        """Retorna las columnas requeridas para el entrenamiento"""
        return ["id", "name", "description", "category", "features"]

    def prepare_data(self, data: pd.DataFrame) -> Tuple:
        """
        Prepara los datos para el entrenamiento, generando embeddings semánticos
        y procesando atributos estructurados.

        Args:
            data: DataFrame con datos de productos

        Returns:
            Tupla con (texto_procesado, matriz_embeddings, ids_productos)
        """
        if not self.validate_data(data):
            raise ValueError("Datos inválidos para entrenamiento")

        # Asegurar que las columnas existan, incluso si están vacías
        if "features" not in data.columns:
            data["features"] = None

        # Enriquecer el texto con características adicionales del JSON features
        data["enriched_text"] = data.apply(self._build_enriched_text, axis=1)

        # Extraer IDs de productos
        product_ids = data["id"].tolist()

        # Retornar los datos procesados
        return (data, None, product_ids)

    def _build_enriched_text(self, row: pd.Series) -> str:
        """
        Construye texto enriquecido para embeddings semánticos incorporando 
        review_summary, questions_and_answers, tags, colors, brand, etc.

        Args:
            row: Fila del DataFrame con datos del producto

        Returns:
            Texto enriquecido combinando todas las características disponibles
        """
        try:
            # Texto base
            text_parts = []
            
            # Campos básicos
            if pd.notna(row.get("name")):
                text_parts.append(str(row["name"]))
            if pd.notna(row.get("description")):
                text_parts.append(str(row["description"]))
            if pd.notna(row.get("category")):
                text_parts.append(str(row["category"]))

            # Procesar features JSON
            features = row.get("features")
            if features is not None and not pd.isna(features):
                # Convertir string JSON a diccionario si es necesario
                if isinstance(features, str):
                    try:
                        features = json.loads(features)
                    except:
                        features = {}
                
                if isinstance(features, dict):
                    # Extraer brand
                    if "brand" in features and features["brand"]:
                        text_parts.append(f"Brand: {features['brand']}")
                    
                    # Extraer tags
                    if "tags" in features and features["tags"]:
                        if isinstance(features["tags"], list):
                            tags_text = " ".join(features["tags"])
                            text_parts.append(f"Tags: {tags_text}")
                    
                    # Extraer colors
                    if "colors" in features and features["colors"]:
                        if isinstance(features["colors"], list):
                            colors_text = " ".join(features["colors"])
                            text_parts.append(f"Colors: {colors_text}")
                    
                    # Extraer review_summary
                    if "review_summary" in features and features["review_summary"]:
                        review_summary = features["review_summary"]
                        if isinstance(review_summary, dict):
                            # Incluir detalles y opiniones adicionales
                            if "details" in review_summary and review_summary["details"]:
                                text_parts.append(f"Customer insights: {review_summary['details']}")
                            if "additional_opinions" in review_summary and review_summary["additional_opinions"]:
                                text_parts.append(f"Additional feedback: {review_summary['additional_opinions']}")
                    
                    # Extraer questions_and_answers
                    if "questions_and_answers" in features and features["questions_and_answers"]:
                        qa_data = features["questions_and_answers"]
                        if isinstance(qa_data, list):
                            qa_texts = []
                            for qa in qa_data[:5]:  # Limitar a las primeras 5 Q&A para evitar texto muy largo
                                if isinstance(qa, dict) and "question" in qa and "answer" in qa:
                                    qa_texts.append(f"Q: {qa['question']} A: {qa['answer']}")
                            if qa_texts:
                                text_parts.append("Q&A: " + " ".join(qa_texts))
                    
                    # Extraer sub_category y master_category si están disponibles
                    if "sub_category" in features and features["sub_category"]:
                        text_parts.append(f"Subcategory: {features['sub_category']}")
                    if "master_category" in features and features["master_category"]:
                        text_parts.append(f"Master category: {features['master_category']}")

            # Combinar todo el texto
            enriched_text = " ".join(text_parts)
            
            # Limpiar y normalizar
            enriched_text = " ".join(enriched_text.split())  # Eliminar espacios extras
            
            log_info(f"Texto enriquecido generado para producto {row.get('id', 'unknown')}: {len(enriched_text)} caracteres")
            
            return enriched_text

        except Exception as e:
            log_error(f"Error generando texto enriquecido para producto {row.get('id', 'unknown')}: {str(e)}")
            # Fallback al texto básico
            basic_text = ""
            if pd.notna(row.get("name")):
                basic_text += str(row["name"]) + " "
            if pd.notna(row.get("description")):
                basic_text += str(row["description"]) + " "
            if pd.notna(row.get("category")):
                basic_text += str(row["category"])
            return basic_text.strip()

    def _extract_structured_features(self, features: dict) -> dict:
        """
        Extrae características estructuradas específicas para el modelo de contenido
        desde el campo features JSONB.

        Args:
            features: Diccionario con características del producto

        Returns:
            Diccionario con características procesadas para embeddings
        """
        structured_features = {}
        
        try:
            # Atributos categóricos directos
            if "brand" in features and features["brand"]:
                structured_features["brand"] = str(features["brand"]).lower()
            
            if "sub_category" in features and features["sub_category"]:
                structured_features["sub_category"] = str(features["sub_category"]).lower()
                
            if "master_category" in features and features["master_category"]:
                structured_features["master_category"] = str(features["master_category"]).lower()
            
            # Procesar tags como atributos múltiples
            if "tags" in features and features["tags"]:
                if isinstance(features["tags"], list):
                    # Crear indicadores binarios para tags principales
                    for tag in features["tags"][:10]:  # Limitar a 10 tags principales
                        structured_features[f"tag_{str(tag).lower()}"] = 1
            
            # Procesar colors como atributos múltiples
            if "colors" in features and features["colors"]:
                if isinstance(features["colors"], list):
                    # Crear indicadores binarios para colores
                    for color in features["colors"][:5]:  # Limitar a 5 colores principales
                        structured_features[f"color_{str(color).lower()}"] = 1
            
            # Procesar métricas de review_summary
            if "review_summary" in features and features["review_summary"]:
                review_summary = features["review_summary"]
                if isinstance(review_summary, dict):
                    # Extraer métricas numéricas
                    if "adjusted_score" in review_summary:
                        try:
                            structured_features["review_adjusted_score"] = float(review_summary["adjusted_score"])
                        except (ValueError, TypeError):
                            pass
                    
                    if "final_score" in review_summary:
                        try:
                            structured_features["review_final_score"] = float(review_summary["final_score"])
                        except (ValueError, TypeError):
                            pass
                    
                    # Contar unfair_reviews (indicador de calidad)
                    if "unfair_reviews" in review_summary and isinstance(review_summary["unfair_reviews"], list):
                        structured_features["unfair_reviews_count"] = len(review_summary["unfair_reviews"])
            
            # Procesar métricas derivadas de questions_and_answers
            if "questions_and_answers" in features and features["questions_and_answers"]:
                qa_data = features["questions_and_answers"]
                if isinstance(qa_data, list):
                    structured_features["qa_count"] = len(qa_data)
                    
                    # Calcular métricas de engagement
                    total_question_length = sum(len(str(qa.get("question", ""))) for qa in qa_data if isinstance(qa, dict))
                    total_answer_length = sum(len(str(qa.get("answer", ""))) for qa in qa_data if isinstance(qa, dict))
                    
                    structured_features["avg_question_length"] = total_question_length / len(qa_data) if qa_data else 0.0
                    structured_features["avg_answer_length"] = total_answer_length / len(qa_data) if qa_data else 0.0
            
            # Procesar métricas de reviews si están disponibles
            if "reviews" in features and features["reviews"]:
                reviews = features["reviews"]
                if isinstance(reviews, list):
                    structured_features["reviews_count"] = len(reviews)
                    
                    # Calcular distribución de ratings
                    scores = []
                    for review in reviews:
                        if isinstance(review, dict) and "score" in review:
                            try:
                                scores.append(float(review["score"]))
                            except (ValueError, TypeError):
                                pass
                    
                    if scores:
                        structured_features["avg_review_score"] = sum(scores) / len(scores)
                        structured_features["review_score_std"] = np.std(scores) if len(scores) > 1 else 0.0
                        structured_features["positive_reviews_ratio"] = sum(1 for s in scores if s >= 4) / len(scores)
                        structured_features["negative_reviews_ratio"] = sum(1 for s in scores if s <= 2) / len(scores)
            
            # Atributos de disponibilidad y precio (si están en features)
            if "availability" in features and features["availability"]:
                availability = str(features["availability"]).lower()
                structured_features["is_in_stock"] = 1 if availability == "in_stock" else 0
                structured_features["is_out_of_stock"] = 1 if availability == "out_of_stock" else 0
            
            if "price" in features and features["price"]:
                try:
                    structured_features["price"] = float(features["price"])
                except (ValueError, TypeError):
                    pass
                    
            if "originalPrice" in features and features["originalPrice"]:
                try:
                    original_price = float(features["originalPrice"])
                    current_price = structured_features.get("price", original_price)
                    if original_price > 0:
                        structured_features["discount_ratio"] = (original_price - current_price) / original_price
                        structured_features["has_discount"] = 1 if original_price > current_price else 0
                except (ValueError, TypeError):
                    pass

            log_info(f"Extraídas {len(structured_features)} características estructuradas del producto")
            return structured_features

        except Exception as e:
            log_error(f"Error extrayendo características estructuradas: {str(e)}")
            return {}

    def train_model(
        self, data: pd.DataFrame, params: Union[Dict[str, Any], None] = None
    ) -> Dict[str, Any]:
        """
        Entrena el modelo de contenido utilizando embeddings semánticos y atributos estructurados.

        Args:
            data: DataFrame con datos de productos
            params: Parámetros opcionales para personalizar el entrenamiento

        Returns:
            Diccionario con los artefactos del modelo entrenado
        """
        try:
            # Combinar parámetros predeterminados con los proporcionados
            model_params = {**self.default_params, **(params or {})}

            # Preparar datos
            processed_data, _, product_ids = self.prepare_data(data)

            # Inicializar vectores de características
            text_embeddings = None
            attribute_embeddings = None

            # 1. Generar embeddings semánticos con Sentence-BERT (opción prioritaria)
            # Siempre intentar usar embeddings semánticos primero, incluso si use_semantic_embeddings=False
            # Solo usar TF-IDF como último recurso si SBERT falla completamente

            # Obtener el modelo SBERT a utilizar (con valor predeterminado)
            embedding_model = model_params.get("embedding_model", "paraphrase-multilingual-MiniLM-L12-v2")

            # Si se especifica explícitamente no usar embeddings semánticos, registrar advertencia
            if not model_params.get("use_semantic_embeddings", True):
                log_warning("Se especificó no usar embeddings semánticos, pero se intentará primero con SBERT")
                log_warning("TF-IDF solo se usará como último recurso si SBERT falla completamente")

            # Intentar generar embeddings semánticos usando texto enriquecido
            try:
                text_embeddings = self._generate_semantic_embeddings(
                    processed_data["enriched_text"].tolist(),
                    model_name=embedding_model
                )
                log_info(f"Generados embeddings semánticos de dimensión {text_embeddings.shape} usando texto enriquecido")

                # Registrar el uso exitoso de SBERT
                self.using_semantic_embeddings = True

            except Exception as e:
                log_error(f"Error fatal generando embeddings semánticos: {str(e)}")
                log_warning("Usando TF-IDF + SVD como último recurso")

                # Usar TF-IDF + SVD como último recurso
                text_embeddings = self._generate_tfidf_embeddings(
                    processed_data["enriched_text"].tolist(),
                    max_features=model_params.get("max_features", 1000),
                    n_components=model_params.get("vector_size", 100)
                )
                log_info(f"Generados embeddings TF-IDF de dimensión {text_embeddings.shape}")

                # Registrar que estamos usando TF-IDF
                self.using_semantic_embeddings = False

            # 2. Procesar atributos estructurados
            if model_params.get("use_structured_attributes", True):
                attribute_embeddings = self._process_structured_attributes(processed_data)
                log_info(f"Procesados atributos estructurados de dimensión {attribute_embeddings.shape if attribute_embeddings is not None else 0}")

            # 3. Combinar embeddings semánticos con atributos estructurados
            if attribute_embeddings is not None and text_embeddings is not None:
                # Combinar con pesos configurables
                text_weight = model_params.get("text_weight", 0.7)
                attribute_weight = model_params.get("attribute_weight", 0.3)

                # Normalizar los pesos para que sumen 1
                total_weight = text_weight + attribute_weight
                text_weight = text_weight / total_weight
                attribute_weight = attribute_weight / total_weight

                # Combinar los embeddings
                content_vectors = self._combine_embeddings(
                    text_embeddings,
                    attribute_embeddings,
                    text_weight=text_weight,
                    attribute_weight=attribute_weight
                )
                log_info(f"Combinados embeddings con pesos: texto={text_weight:.2f}, atributos={attribute_weight:.2f}")
            else:
                # Si no hay atributos, usar solo los embeddings de texto
                content_vectors = text_embeddings

            # Normalizar los vectores finales
            content_vectors = self._normalize_vectors(content_vectors)

            # Crear pipeline para futuras predicciones (para compatibilidad)
            # Nota: Para embeddings SBERT, este pipeline no se usa directamente
            # pero se mantiene para compatibilidad con el código existente
            if self.tfidf_vectorizer is not None:
                content_pipeline = Pipeline([
                    ("tfidf", self.tfidf_vectorizer),
                    ("svd", self.svd_model if hasattr(self, "svd_model") else None),
                    ("normalizer", Normalizer(copy=False)),
                ])
                self.model = content_pipeline
            else:
                # Crear un pipeline vacío para mantener compatibilidad
                content_pipeline = None
                self.model = None

            # Calcular métricas
            metrics = self._calculate_metrics(processed_data, embeddings=content_vectors)

            # Extraer categorías de productos para diversificación
            item_categories = {}
            if "category" in processed_data.columns:
                item_categories = dict(zip(processed_data["id"], processed_data["category"]))
                log_info(f"Extraídas {len(item_categories)} categorías de productos para diversificación")

            # Crear diccionario de artefactos
            artifacts = {
                "content_pipeline": content_pipeline,  # Para compatibilidad
                "sentence_transformer": self.sentence_transformer,
                "attribute_encoder": self.attribute_encoder,
                "scaler": self.scaler,
                "product_features": content_vectors,
                "product_ids": product_ids,
                "item_categories": item_categories,  # Añadir categorías para diversificación
                "metrics": metrics,
                "parameters": model_params,
                "using_semantic_embeddings": getattr(self, "using_semantic_embeddings", True),  # Indicar si se usaron embeddings semánticos
            }

            # Añadir artefactos específicos según el método utilizado
            if hasattr(self, "tfidf_vectorizer") and self.tfidf_vectorizer is not None:
                artifacts["tfidf_model"] = self.tfidf_vectorizer
            if hasattr(self, "svd_model") and self.svd_model is not None:
                artifacts["svd_model"] = self.svd_model

            return artifacts

        except Exception as e:
            log_error(f"Error en entrenamiento para cuenta {self.account_id}: {str(e)}")
            raise

    def _generate_semantic_embeddings(
        self, texts: List[str], model_name: str = "paraphrase-multilingual-MiniLM-L12-v2"
    ) -> np.ndarray:
        """
        Genera embeddings semánticos utilizando Sentence-BERT.

        Prioriza SBERT sobre TF-IDF+SVD, intentando múltiples modelos si es necesario.

        Modelos alternativos en caso de fallo:
        1. all-MiniLM-L6-v2 (más rápido, menor calidad)
        2. all-mpnet-base-v2 (más lento, mayor calidad)
        3. distiluse-base-multilingual-cased-v1 (buen balance multilingüe)

        Args:
            texts: Lista de textos a procesar
            model_name: Nombre del modelo SBERT a utilizar

        Returns:
            Array NumPy con los embeddings generados
        """
        # Lista de modelos alternativos a intentar en caso de fallo
        fallback_models = [
            "all-MiniLM-L6-v2",  # Más rápido, menor calidad
            "all-mpnet-base-v2",  # Más lento, mayor calidad
            "distiluse-base-multilingual-cased-v1"  # Buen balance multilingüe
        ]

        # Asegurar que el modelo principal no esté en la lista de fallbacks
        if model_name in fallback_models:
            fallback_models.remove(model_name)

        # Intentar con el modelo principal
        try:
            # Cargar el modelo Sentence-BERT
            log_info(f"Cargando modelo Sentence-BERT principal: {model_name}")
            self.sentence_transformer = SentenceTransformer(model_name)

            # Generar embeddings
            log_info(f"Generando embeddings para {len(texts)} textos")
            embeddings = self.sentence_transformer.encode(
                texts,
                show_progress_bar=True,
                batch_size=32,
                convert_to_numpy=True
            )

            log_info(f"Embeddings generados exitosamente con modelo {model_name}")
            return embeddings

        except Exception as e:
            log_error(f"Error generando embeddings con modelo principal {model_name}: {str(e)}")

            # Intentar con modelos alternativos
            for fallback_model in fallback_models:
                try:
                    log_info(f"Intentando con modelo alternativo: {fallback_model}")
                    self.sentence_transformer = SentenceTransformer(fallback_model)

                    embeddings = self.sentence_transformer.encode(
                        texts,
                        show_progress_bar=True,
                        batch_size=32,
                        convert_to_numpy=True
                    )

                    log_info(f"Embeddings generados exitosamente con modelo alternativo {fallback_model}")
                    return embeddings

                except Exception as fallback_error:
                    log_error(f"Error con modelo alternativo {fallback_model}: {str(fallback_error)}")
                    continue

            # Si todos los modelos SBERT fallan, usar TF-IDF como último recurso
            log_warning("Todos los modelos SBERT fallaron. Usando TF-IDF como último recurso.")
            return self._generate_tfidf_embeddings(texts)

    def _generate_tfidf_embeddings(
        self, texts: List[str], max_features: int = 1000, n_components: int = 100
    ) -> np.ndarray:
        """
        Genera embeddings utilizando TF-IDF + SVD como respaldo.

        Args:
            texts: Lista de textos a procesar
            max_features: Número máximo de características para TF-IDF
            n_components: Número de componentes para SVD

        Returns:
            Array NumPy con los embeddings generados
        """
        # Crear y ajustar TF-IDF
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words="english",
            use_idf=True,
            smooth_idf=True,
        )
        tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)

        # Reducir dimensionalidad con SVD
        self.svd_model = TruncatedSVD(n_components=n_components, random_state=42)
        normalized_matrix = Normalizer(copy=False).fit_transform(tfidf_matrix)
        embeddings = self.svd_model.fit_transform(normalized_matrix)

        return embeddings

    def _process_structured_attributes(self, data: pd.DataFrame) -> Optional[np.ndarray]:
        """
        Procesa atributos estructurados de los productos usando codificación robusta.

        Implementa:
        - OneHotEncoder para atributos categóricos
        - StandardScaler para atributos numéricos
        - Manejo avanzado de valores faltantes
        - Detección automática de tipos de atributos

        Args:
            data: DataFrame con datos de productos

        Returns:
            Array NumPy con los embeddings de atributos o None si no hay atributos
        """
        try:
            # Verificar si hay atributos estructurados
            if "features" not in data.columns or data["features"].isna().all():
                log_warning("No se encontraron atributos estructurados en los datos")
                return None

            # Importar encoders
            from sklearn.preprocessing import OneHotEncoder, StandardScaler
            from sklearn.impute import SimpleImputer

            # Extraer y procesar atributos
            attributes_list = []

            for features in data["features"]:
                # Procesar atributos JSON
                if features is None or pd.isna(features):
                    attributes_list.append({})
                    continue

                # Convertir string JSON a diccionario si es necesario
                if isinstance(features, str):
                    try:
                        features = json.loads(features)
                    except:
                        features = {}

                # Enriquecer atributos con campos específicos para recomendaciones
                enriched_features = self._extract_structured_features(features)
                attributes_list.append(enriched_features)

            # Extraer atributos comunes
            all_keys = set()
            for attrs in attributes_list:
                all_keys.update(attrs.keys())

            if not all_keys:
                log_warning("No se encontraron atributos estructurados válidos")
                return None

            # Clasificar atributos por tipo
            numeric_keys = []
            categorical_keys = []
            boolean_keys = []

            # Analizar los primeros 100 elementos (o todos si hay menos) para determinar tipos
            sample_size = min(len(attributes_list), 100)
            for key in all_keys:
                # Contar tipos de valores para este atributo
                numeric_count = 0
                categorical_count = 0
                boolean_count = 0

                for i in range(sample_size):
                    if i >= len(attributes_list):
                        break

                    value = attributes_list[i].get(key)

                    if value is None or pd.isna(value):
                        continue
                    elif isinstance(value, bool):
                        boolean_count += 1
                    elif isinstance(value, (int, float)) or (isinstance(value, str) and value.replace('.', '', 1).isdigit()):
                        numeric_count += 1
                    else:
                        categorical_count += 1

                # Determinar el tipo predominante
                max_count = max(numeric_count, categorical_count, boolean_count)
                if max_count == 0:
                    continue  # No hay valores válidos para este atributo

                if numeric_count == max_count:
                    numeric_keys.append(key)
                elif boolean_count == max_count:
                    boolean_keys.append(key)
                else:
                    categorical_keys.append(key)

            log_info(f"Atributos clasificados: {len(numeric_keys)} numéricos, {len(categorical_keys)} categóricos, {len(boolean_keys)} booleanos")

            # Crear DataFrames separados para cada tipo de atributo
            numeric_data = []
            categorical_data = {key: [] for key in categorical_keys}
            boolean_data = []

            # Extraer valores
            for attrs in attributes_list:
                # Procesar atributos numéricos
                numeric_row = []
                for key in numeric_keys:
                    value = attrs.get(key)
                    try:
                        if isinstance(value, str):
                            value = float(value)
                        numeric_row.append(float(value) if value is not None and not pd.isna(value) else np.nan)
                    except (ValueError, TypeError):
                        numeric_row.append(np.nan)

                numeric_data.append(numeric_row)

                # Procesar atributos categóricos
                for key in categorical_keys:
                    value = attrs.get(key)
                    categorical_data[key].append(str(value) if value is not None and not pd.isna(value) else None)

                # Procesar atributos booleanos
                boolean_row = []
                for key in boolean_keys:
                    value = attrs.get(key)
                    if isinstance(value, bool):
                        boolean_row.append(1.0 if value else 0.0)
                    elif isinstance(value, (int, float)):
                        boolean_row.append(1.0 if value else 0.0)
                    elif isinstance(value, str):
                        boolean_row.append(1.0 if value.lower() in ('true', 'yes', '1', 't', 'y') else 0.0)
                    else:
                        boolean_row.append(0.0)

                boolean_data.append(boolean_row)

            # Convertir a arrays NumPy
            numeric_array = np.array(numeric_data, dtype=float) if numeric_data and numeric_keys else None
            boolean_array = np.array(boolean_data, dtype=float) if boolean_data and boolean_keys else None

            # Procesar datos numéricos
            if numeric_array is not None and numeric_array.shape[1] > 0:
                # Imputar valores faltantes con la media
                self.numeric_imputer = SimpleImputer(strategy='mean')
                numeric_array = self.numeric_imputer.fit_transform(numeric_array)

                # Normalizar
                self.numeric_scaler = StandardScaler()
                numeric_array = self.numeric_scaler.fit_transform(numeric_array)

                log_info(f"Procesados {numeric_array.shape[1]} atributos numéricos")

            # Procesar datos categóricos
            categorical_arrays = []
            if categorical_keys:
                self.categorical_encoders = {}

                for key in categorical_keys:
                    if not categorical_data[key]:
                        continue

                    # Crear DataFrame para este atributo
                    cat_df = pd.DataFrame({key: categorical_data[key]})

                    # Imputar valores faltantes con 'missing'
                    cat_df[key].fillna('missing', inplace=True)

                    # Codificar con OneHotEncoder
                    encoder = OneHotEncoder(sparse=False, handle_unknown='ignore')
                    encoded = encoder.fit_transform(cat_df[[key]])

                    # Guardar encoder
                    self.categorical_encoders[key] = encoder

                    # Añadir a la lista de arrays
                    if encoded.shape[1] > 0:
                        categorical_arrays.append(encoded)

                log_info(f"Procesados {len(categorical_keys)} atributos categóricos")

            # Combinar todos los arrays
            all_arrays = []
            if numeric_array is not None:
                all_arrays.append(numeric_array)
            if categorical_arrays:
                all_arrays.extend(categorical_arrays)
            if boolean_array is not None:
                all_arrays.append(boolean_array)

            if not all_arrays:
                log_warning("No se pudieron procesar atributos estructurados")
                return None

            # Concatenar horizontalmente
            combined_array = np.hstack(all_arrays)

            # Guardar metadatos para futuras predicciones
            self.attribute_encoder = {
                'numeric_keys': numeric_keys,
                'categorical_keys': categorical_keys,
                'boolean_keys': boolean_keys
            }

            log_info(f"Generado array de atributos estructurados de dimensión {combined_array.shape}")
            return combined_array

        except Exception as e:
            log_error(f"Error procesando atributos estructurados: {str(e)}")
            return None

    def _combine_embeddings(
        self,
        text_embeddings: np.ndarray,
        attribute_embeddings: np.ndarray,
        text_weight: float = 0.7,
        attribute_weight: float = 0.3
    ) -> np.ndarray:
        """
        Combina embeddings de texto con embeddings de atributos usando técnicas avanzadas.

        Implementa tres estrategias de combinación:
        1. Concatenación seguida de una proyección lineal (PCA)
        2. Proyección no aleatoria usando SVD para preservar la estructura
        3. Promedio ponderado con pesos aprendidos (si hay suficientes datos)

        Args:
            text_embeddings: Embeddings de texto
            attribute_embeddings: Embeddings de atributos
            text_weight: Peso para los embeddings de texto
            attribute_weight: Peso para los embeddings de atributos

        Returns:
            Array NumPy con los embeddings combinados
        """
        # Verificar dimensiones
        if text_embeddings.shape[0] != attribute_embeddings.shape[0]:
            raise ValueError(f"Número de embeddings no coincide: texto={text_embeddings.shape[0]}, atributos={attribute_embeddings.shape[0]}")

        # Normalizar embeddings individualmente
        text_norm = self._normalize_vectors(text_embeddings)
        attr_norm = self._normalize_vectors(attribute_embeddings)

        # Determinar la estrategia de combinación basada en el tamaño de los datos
        n_samples = text_embeddings.shape[0]

        # ESTRATEGIA 1: CONCATENACIÓN + PROYECCIÓN (para conjuntos de datos grandes)
        if n_samples >= 100:
            try:
                from sklearn.decomposition import PCA

                # Determinar la dimensión objetivo (promedio de ambas dimensiones)
                target_dim = min(
                    max(text_norm.shape[1], attr_norm.shape[1]),
                    int((text_norm.shape[1] + attr_norm.shape[1]) / 2)
                )

                # Concatenar embeddings
                concatenated = np.hstack([
                    text_norm * text_weight,
                    attr_norm * attribute_weight
                ])

                # Reducir dimensionalidad con PCA
                pca = PCA(n_components=target_dim)
                combined = pca.fit_transform(concatenated)

                log_info(f"Combinación de embeddings usando concatenación + PCA: {combined.shape}")
                return self._normalize_vectors(combined)

            except Exception as e:
                log_error(f"Error en combinación con PCA: {str(e)}. Usando estrategia alternativa.")
                # Continuar con la siguiente estrategia si falla

        # ESTRATEGIA 2: PROYECCIÓN NO ALEATORIA (para conjuntos de datos medianos)
        if n_samples >= 20:
            try:
                from sklearn.decomposition import TruncatedSVD

                # Si las dimensiones son diferentes, proyectar atributos a la dimensión de texto
                if attr_norm.shape[1] != text_norm.shape[1]:
                    # Usar SVD para crear una proyección que preserve la estructura
                    svd = TruncatedSVD(n_components=min(attr_norm.shape[1], text_norm.shape[1]))
                    svd.fit(attr_norm)

                    # Crear matriz de proyección
                    if text_norm.shape[1] > attr_norm.shape[1]:
                        # Proyectar atributos a una dimensión mayor
                        projection = np.zeros((attr_norm.shape[1], text_norm.shape[1]))
                        projection[:, :svd.components_.shape[0]] = svd.components_.T
                    else:
                        # Proyectar atributos a una dimensión menor
                        projection = svd.components_[:text_norm.shape[1], :].T

                    # Aplicar proyección
                    attr_projected = np.dot(attr_norm, projection)
                    attr_projected = self._normalize_vectors(attr_projected)
                else:
                    attr_projected = attr_norm

                # Combinar con pesos
                combined = text_weight * text_norm + attribute_weight * attr_projected

                log_info(f"Combinación de embeddings usando proyección SVD: {combined.shape}")
                return self._normalize_vectors(combined)

            except Exception as e:
                log_error(f"Error en combinación con SVD: {str(e)}. Usando estrategia alternativa.")
                # Continuar con la siguiente estrategia si falla

        # ESTRATEGIA 3: PROMEDIO PONDERADO CON PROYECCIÓN SIMPLE (fallback)
        # Proyectar atributos a la misma dimensión que los embeddings de texto
        if attr_norm.shape[1] != text_norm.shape[1]:
            # Crear una matriz de proyección más estable que la aleatoria
            # Usar una matriz de proyección ortogonal para preservar mejor las distancias
            projection = np.random.randn(attr_norm.shape[1], text_norm.shape[1])
            # Ortogonalizar la matriz usando descomposición QR
            # Nota: Solo necesitamos la matriz Q (ortogonal), ignoramos R
            q, _ = np.linalg.qr(projection)
            # Usar solo la parte ortogonal (Q)
            projection = q[:, :text_norm.shape[1]] if q.shape[1] > text_norm.shape[1] else q

            # Aplicar proyección
            attr_projected = np.dot(attr_norm, projection)
            attr_projected = self._normalize_vectors(attr_projected)
        else:
            attr_projected = attr_norm

        # Combinar con pesos
        combined = text_weight * text_norm + attribute_weight * attr_projected

        log_info(f"Combinación de embeddings usando promedio ponderado: {combined.shape}")

        # Normalizar el resultado final
        return self._normalize_vectors(combined)

    def _normalize_vectors(self, vectors: np.ndarray) -> np.ndarray:
        """
        Normaliza vectores a norma unitaria.

        Args:
            vectors: Matriz de vectores a normalizar

        Returns:
            Matriz normalizada
        """
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        # Evitar división por cero
        norms[norms == 0] = 1.0
        return vectors / norms

    def _calculate_metrics(self, test_data: pd.DataFrame, **kwargs) -> Dict[str, float]:
        """
        Calcula métricas del modelo de contenido.

        Args:
            test_data: DataFrame con datos de prueba
            kwargs: Argumentos adicionales (embeddings, etc.)

        Returns:
            Diccionario con métricas calculadas
        """
        try:
            # Obtener embeddings
            embeddings = kwargs.get("embeddings")
            if embeddings is None:
                return {"category_similarity": 0.0, "embedding_quality": 0.0}

            # 1. Calcular similitud promedio entre productos de la misma categoría
            category_similarities = []

            for category in test_data["category"].unique():
                category_mask = test_data["category"] == category
                if category_mask.sum() > 1:
                    # Obtener índices de productos en esta categoría
                    category_indices = np.where(category_mask)[0]

                    # Calcular matriz de similitud para esta categoría
                    category_embeddings = embeddings[category_indices]
                    similarities = cosine_similarity(category_embeddings)

                    # Eliminar la diagonal (similitud consigo mismo)
                    np.fill_diagonal(similarities, 0)

                    # Calcular similitud promedio
                    category_similarities.append(float(similarities.mean()))

            # Calcular similitud promedio entre categorías
            avg_category_similarity = (
                float(np.mean(category_similarities)) if category_similarities else 0.0
            )

            # 2. Calcular calidad general de los embeddings
            # Medimos qué tan bien los embeddings separan productos de diferentes categorías

            # Calcular similitud entre todas las categorías
            cross_category_similarities = []
            categories = test_data["category"].unique()

            if len(categories) > 1:
                for i, cat1 in enumerate(categories[:-1]):
                    cat1_mask = test_data["category"] == cat1
                    cat1_indices = np.where(cat1_mask)[0]

                    for cat2 in categories[i+1:]:
                        cat2_mask = test_data["category"] == cat2
                        cat2_indices = np.where(cat2_mask)[0]

                        # Calcular similitud entre categorías
                        if len(cat1_indices) > 0 and len(cat2_indices) > 0:
                            cat1_embeddings = embeddings[cat1_indices]
                            cat2_embeddings = embeddings[cat2_indices]

                            # Calcular similitud promedio entre categorías
                            cross_similarities = cosine_similarity(cat1_embeddings, cat2_embeddings)
                            cross_category_similarities.append(float(cross_similarities.mean()))

            # Calcular similitud promedio entre categorías diferentes
            avg_cross_similarity = (
                float(np.mean(cross_category_similarities)) if cross_category_similarities else 0.0
            )

            # Calcular calidad de embeddings (mayor diferencia entre similitud intra-categoría e inter-categoría)
            embedding_quality = max(0.0, avg_category_similarity - avg_cross_similarity)

            # Normalizar a escala 0-1
            embedding_quality = min(1.0, embedding_quality)

            return {
                "category_similarity": avg_category_similarity,
                "cross_category_similarity": avg_cross_similarity,
                "embedding_quality": embedding_quality
            }

        except Exception as e:
            log_error(f"Error calculando métricas: {str(e)}")
            return {"category_similarity": 0.0, "embedding_quality": 0.0}

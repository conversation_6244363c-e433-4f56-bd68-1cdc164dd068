"""
Módulo para implementar Learning-to-Rank (LTR) para mejorar la hibridación de recomendaciones.

Este módulo implementa algoritmos como LambdaMART, XGBoost Ranker y CatBoost para optimizar
directamente métricas como NDCG, utilizando los scores de los modelos colaborativo y basado
en contenido, junto con características de usuario, producto y contexto.

IMPORTANTE: El modelo LTR debe entrenarse para predecir la relevancia real del ítem para el usuario,
no una combinación de los scores de los modelos base. La relevancia real se determina por si el
usuario interactuó con el ítem en el conjunto de prueba (1.0) o no (0.0). Esto alinea el modelo
con el objetivo de maximizar interacciones/conversiones reales, mejorando significativamente
la relevancia percibida por el usuario.
"""
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GroupKFold
from sklearn.metrics import mean_squared_error, ndcg_score
import joblib
import os
import time
from datetime import datetime

# Importaciones condicionales para modelos avanzados
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

from src.utils.base_logger import log_info, log_error, log_warning


class LearningToRankModel:
    """
    Implementa un modelo de Learning-to-Rank para mejorar la hibridación de recomendaciones.

    Este modelo combina características de diferentes fuentes (colaborativa, contenido, contexto)
    para aprender a rankear items de manera óptima para cada usuario, optimizando directamente
    métricas como NDCG.

    Soporta múltiples algoritmos:
    - LambdaMART (LightGBM)
    - XGBoost Ranker
    - CatBoost Ranker
    - GBDT (Gradient Boosting Decision Trees)
    - Random Forest
    """

    def __init__(self, account_id: int, model_type: str = "lambdamart"):
        """
        Inicializa el modelo LTR.

        Args:
            account_id: ID de la cuenta
            model_type: Tipo de modelo a utilizar ('lambdamart', 'xgboost', 'catboost', 'gbdt', 'random_forest')
        """
        self.account_id = account_id
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = None
        self.trained = False
        self.model_params = {}

        # Directorio para guardar modelos
        self.models_dir = os.path.join("models", "ltr", str(account_id))
        os.makedirs(self.models_dir, exist_ok=True)

        # Cargar parámetros por defecto para el modelo
        self._load_default_params()

    def _load_default_params(self):
        """Carga parámetros por defecto para el modelo seleccionado."""
        # Parámetros por defecto para cada tipo de modelo
        default_params = {
            "lambdamart": {
                "objective": "lambdarank",
                "metric": "ndcg",
                "boosting_type": "gbdt",
                "n_estimators": 100,
                "learning_rate": 0.05,
                "max_depth": 5,
                "num_leaves": 31,
                "min_data_in_leaf": 20,
                "min_sum_hessian_in_leaf": 1e-3,
                "random_state": 42
            },
            "xgboost": {
                "objective": "rank:ndcg",
                "eval_metric": "ndcg@10",
                "n_estimators": 100,
                "learning_rate": 0.1,
                "max_depth": 6,
                "min_child_weight": 1,
                "gamma": 0.1,
                "random_state": 42
            },
            "catboost": {
                "loss_function": "YetiRankPairwise",
                "eval_metric": "NDCG",
                "iterations": 100,
                "learning_rate": 0.1,
                "depth": 6,
                "random_seed": 42
            },
            "gbdt": {
                "n_estimators": 100,
                "learning_rate": 0.1,
                "max_depth": 3,
                "random_state": 42
            },
            "random_forest": {
                "n_estimators": 100,
                "max_depth": 5,
                "random_state": 42
            }
        }

        # Asignar parámetros por defecto según el tipo de modelo
        if self.model_type in default_params:
            self.model_params = default_params[self.model_type]
        else:
            # Si el tipo de modelo no está soportado, usar GBDT por defecto
            log_warning(f"Tipo de modelo '{self.model_type}' no soportado. Usando 'gbdt' por defecto.")
            self.model_type = "gbdt"
            self.model_params = default_params["gbdt"]

    def _create_model(self) -> Any:
        """Crea el modelo según el tipo especificado."""
        try:
            if self.model_type == "lambdamart":
                # LambdaMART implementado con LightGBM
                if LIGHTGBM_AVAILABLE:
                    return lgb.LGBMRanker(**self.model_params)
                else:
                    log_warning("LightGBM no está instalado. Usando GBDT como alternativa.")
                    self.model_type = "gbdt"

            elif self.model_type == "xgboost":
                # XGBoost Ranker
                if XGBOOST_AVAILABLE:
                    return xgb.XGBRanker(**self.model_params)
                else:
                    log_warning("XGBoost no está instalado. Usando GBDT como alternativa.")
                    self.model_type = "gbdt"

            elif self.model_type == "catboost":
                # CatBoost Ranker
                if CATBOOST_AVAILABLE:
                    return cb.CatBoostRanker(**self.model_params)
                else:
                    log_warning("CatBoost no está instalado. Usando GBDT como alternativa.")
                    self.model_type = "gbdt"

            # Modelos siempre disponibles (scikit-learn)
            if self.model_type == "gbdt":
                return GradientBoostingRegressor(**self.model_params)

            elif self.model_type == "random_forest":
                return RandomForestRegressor(**self.model_params)

            else:
                # Tipo de modelo no soportado, usar GBDT por defecto
                log_warning(f"Tipo de modelo '{self.model_type}' no soportado. Usando 'gbdt' por defecto.")
                self.model_type = "gbdt"
                return GradientBoostingRegressor(**self.model_params)

        except Exception as e:
            log_error(f"Error creando modelo LTR: {str(e)}")
            # En caso de error, usar GBDT como fallback
            self.model_type = "gbdt"
            return GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=3,
                random_state=42
            )

    def _extract_features(self, recommendations: List[Dict[str, Any]], user_id: Optional[int] = None, user_history: Optional[Dict[int, float]] = None, timestamp: Optional[int] = None) -> pd.DataFrame:
        """
        Extrae características avanzadas de las recomendaciones para el modelo LTR.

        Args:
            recommendations: Lista de recomendaciones con scores de diferentes fuentes
            user_id: ID del usuario (opcional)
            user_history: Historial de interacciones del usuario (opcional)
            timestamp: Timestamp de la solicitud (opcional, usado para evitar feature leakage)

        Returns:
            DataFrame con características extraídas
        """
        features = []

        # Calcular estadísticas globales para normalización
        all_scores = {
            "collab": [rec.get("collab_score", 0.0) for rec in recommendations],
            "content": [rec.get("content_score", 0.0) for rec in recommendations],
            "confidence": [rec.get("confidence", 0.0) for rec in recommendations],
            "popularity": [rec.get("popularity", 0.0) for rec in recommendations if "popularity" in rec],
            "price": [rec.get("price", 0.0) for rec in recommendations if "price" in rec]
        }

        # Calcular estadísticas para normalización relativa
        stats = {}
        for key, values in all_scores.items():
            if values:
                stats[key] = {
                    "max": max(values),
                    "min": min(values),
                    "mean": sum(values) / len(values),
                    "range": max(values) - min(values) if len(values) > 1 else 1.0
                }

        # Extraer características del historial de usuario si está disponible
        user_features = {}
        if user_id is not None and user_history is not None:
            # Tamaño del historial
            user_features["history_size"] = len(user_history)

            # Promedio de ratings/interacciones
            if user_history:
                user_features["avg_interaction"] = sum(user_history.values()) / len(user_history)

            # Categorías preferidas (si están disponibles en las recomendaciones)
            category_counts = {}
            for rec in recommendations:
                if "category" in rec and rec["category"]:
                    category = rec["category"]
                    category_counts[category] = category_counts.get(category, 0) + 1

            if category_counts:
                # Categoría más frecuente
                user_features["top_category"] = max(category_counts.items(), key=lambda x: x[1])[0]
                
            # NUEVO: Extraer secuencia de interacciones recientes
            if hasattr(user_history, 'items') and callable(getattr(user_history, 'items')):
                # Ordenar interacciones por timestamp (si disponible)
                recent_interactions = []
                for item_id, interaction in user_history.items():
                    if isinstance(interaction, dict) and "timestamp" in interaction:
                        recent_interactions.append((item_id, interaction["timestamp"]))
                
                if recent_interactions:
                    # Ordenar por timestamp descendente
                    recent_interactions.sort(key=lambda x: x[1], reverse=True)
                    
                    # Guardar los últimos 5 ítems con los que interactuó el usuario
                    user_features["recent_items"] = [item_id for item_id, _ in recent_interactions[:5]]
                    
                    # Si hay categorías disponibles, extraer la categoría más reciente
                    for rec in recommendations:
                        if rec.get("item_id") == user_features["recent_items"][0] and "category" in rec:
                            user_features["last_category"] = rec["category"]
                            break

        for rec in recommendations:
            # Características básicas
            feature_dict = {
                "item_id": rec.get("item_id", 0),
                "collab_score": rec.get("collab_score", 0.0),
                "content_score": rec.get("content_score", 0.0),
                "confidence": rec.get("confidence", 0.0),
            }

            # Características derivadas de scores
            feature_dict["score_diff"] = abs(feature_dict["collab_score"] - feature_dict["content_score"])
            feature_dict["score_product"] = feature_dict["collab_score"] * feature_dict["content_score"]
            feature_dict["score_sum"] = feature_dict["collab_score"] + feature_dict["content_score"]
            feature_dict["score_ratio"] = (
                feature_dict["collab_score"] / feature_dict["content_score"]
                if feature_dict["content_score"] > 0 else 0.0
            )

            # Características de fuente
            feature_dict["is_collab_only"] = 1.0 if feature_dict["collab_score"] > 0 and feature_dict["content_score"] == 0 else 0.0
            feature_dict["is_content_only"] = 1.0 if feature_dict["content_score"] > 0 and feature_dict["collab_score"] == 0 else 0.0
            feature_dict["is_both"] = 1.0 if feature_dict["collab_score"] > 0 and feature_dict["content_score"] > 0 else 0.0

            # Características normalizadas (relativas al conjunto actual)
            for key in ["collab_score", "content_score", "confidence"]:
                if key in stats and stats[key]["range"] > 0:
                    feature_dict[f"{key}_norm"] = (feature_dict[key] - stats[key]["min"]) / stats[key]["range"]
                else:
                    feature_dict[f"{key}_norm"] = feature_dict[key]

            # Características de producto
            if "popularity" in rec:
                # MODIFICADO: Verificar timestamp para evitar feature leakage
                if timestamp is not None and "popularity_timestamp" in rec:
                    # Solo usar popularidad hasta el timestamp dado
                    if rec["popularity_timestamp"] <= timestamp:
                        feature_dict["popularity"] = rec["popularity"]
                    else:
                        # Usar valor por defecto o popularidad anterior si está disponible
                        feature_dict["popularity"] = rec.get("previous_popularity", 0.0)
                else:
                    feature_dict["popularity"] = rec["popularity"]
                
                # Normalizar popularidad
                if "popularity" in stats and stats["popularity"]["range"] > 0:
                    feature_dict["popularity_norm"] = (feature_dict["popularity"] - stats["popularity"]["min"]) / stats["popularity"]["range"]

            if "price" in rec:
                feature_dict["price"] = rec["price"]
                # Normalizar precio
                if "price" in stats and stats["price"]["range"] > 0:
                    feature_dict["price_norm"] = (rec["price"] - stats["price"]["min"]) / stats["price"]["range"]

                    # Características derivadas de precio
                    if feature_dict["price"] > 0:
                        # Ratio valor/precio (score por unidad de precio)
                        feature_dict["value_price_ratio"] = feature_dict["score_sum"] / feature_dict["price"]

            # MODIFICADO: Manejar recencia de manera segura evitando feature leakage
            if "recency" in rec:
                if timestamp is not None and "last_interaction_timestamp" in rec:
                    # Calcular recencia basada en el timestamp de la solicitud
                    if rec["last_interaction_timestamp"] <= timestamp:
                        feature_dict["recency"] = timestamp - rec["last_interaction_timestamp"]
                    else:
                        # Si la interacción es posterior al timestamp, usar valor por defecto
                        feature_dict["recency"] = rec.get("recency", 0)
                else:
                    feature_dict["recency"] = rec["recency"]
                
                # Característica derivada: más reciente = mejor
                feature_dict["recency_score"] = 1.0 / (1.0 + feature_dict["recency"]) if feature_dict["recency"] > 0 else 1.0

            if "category" in rec:
                # One-hot encoding de categoría (simplificado)
                feature_dict[f"category_{rec['category']}"] = 1.0

                # Coincidencia con categoría preferida del usuario
                if user_features.get("top_category") == rec["category"]:
                    feature_dict["is_preferred_category"] = 1.0
                else:
                    feature_dict["is_preferred_category"] = 0.0
                
                # NUEVO: Coincidencia con la última categoría con la que interactuó el usuario
                if user_features.get("last_category") == rec["category"]:
                    feature_dict["is_last_category"] = 1.0
                else:
                    feature_dict["is_last_category"] = 0.0

            if "category_score" in rec:
                feature_dict["category_score"] = rec["category_score"]

            # Características de interacción usuario-item
            if user_id is not None and user_history is not None:
                # Item en historial del usuario
                item_id = rec.get("item_id", 0)
                if item_id in user_history:
                    feature_dict["in_history"] = 1.0
                    
                    # MODIFICADO: Extraer características más ricas de la interacción histórica
                    if isinstance(user_history[item_id], dict):
                        interaction_data = user_history[item_id]
                        feature_dict["history_score"] = interaction_data.get("score", 0.0)
                        
                        # NUEVO: Extraer timestamps para características temporales
                        if "timestamp" in interaction_data and timestamp is not None:
                            # Tiempo desde la última interacción con este ítem (en horas)
                            last_interaction_time = interaction_data["timestamp"]
                            hours_since_last_interaction = (timestamp - last_interaction_time) / 3600
                            feature_dict["hours_since_interaction"] = hours_since_last_interaction
                            
                            # Características derivadas de tiempo
                            feature_dict["days_since_interaction"] = hours_since_last_interaction / 24
                            feature_dict["recency_decay"] = np.exp(-feature_dict["days_since_interaction"] / 30)  # Decaimiento exponencial
                            
                        # NUEVO: Frecuencia de interacciones con este ítem
                        if "interaction_count" in interaction_data:
                            feature_dict["interaction_count"] = interaction_data["interaction_count"]
                            feature_dict["high_engagement"] = 1.0 if interaction_data["interaction_count"] > 3 else 0.0
                    else:
                        feature_dict["history_score"] = user_history[item_id]
                else:
                    feature_dict["in_history"] = 0.0
                    feature_dict["history_score"] = 0.0

                # Tamaño del historial como característica
                feature_dict["user_history_size"] = user_features.get("history_size", 0)

                # Promedio de interacciones del usuario
                if "avg_interaction" in user_features:
                    feature_dict["user_avg_interaction"] = user_features["avg_interaction"]
                
                # NUEVO: Verificar si el ítem está en la secuencia de ítems recientes
                if "recent_items" in user_features and item_id in user_features["recent_items"]:
                    feature_dict["in_recent_history"] = 1.0
                    feature_dict["recent_position"] = user_features["recent_items"].index(item_id) + 1
                else:
                    feature_dict["in_recent_history"] = 0.0
                    feature_dict["recent_position"] = 0.0

            # NUEVO: Características contextuales de tiempo
            if "request_timestamp" in rec:
                # Usar timestamp de la solicitud si está disponible
                req_timestamp = rec["request_timestamp"]
                
                # Extraer características temporales
                from datetime import datetime
                dt = datetime.fromtimestamp(req_timestamp)
                
                # Hora del día (0-23)
                feature_dict["hour_of_day"] = dt.hour
                
                # Normalizar hora del día a un valor entre 0 y 1
                feature_dict["hour_of_day_norm"] = dt.hour / 23.0
                
                # Día de la semana (0=lunes, 6=domingo)
                feature_dict["day_of_week"] = dt.weekday()
                
                # Es fin de semana
                feature_dict["is_weekend"] = 1.0 if dt.weekday() >= 5 else 0.0
                
                # Parte del día (codificada como one-hot)
                if dt.hour < 6:
                    feature_dict["time_night"] = 1.0
                elif dt.hour < 12:
                    feature_dict["time_morning"] = 1.0
                elif dt.hour < 18:
                    feature_dict["time_afternoon"] = 1.0
                else:
                    feature_dict["time_evening"] = 1.0

            # Características contextuales adicionales
            if "device" in rec:
                # One-hot encoding de dispositivo
                feature_dict[f"device_{rec['device']}"] = 1.0

            if "time_of_day" in rec:
                # One-hot encoding de hora del día
                feature_dict[f"time_{rec['time_of_day']}"] = 1.0

            # Características de confianza detalladas
            if "confidence_metadata" in rec:
                metadata = rec["confidence_metadata"]
                for key, value in metadata.items():
                    if isinstance(value, (int, float)):
                        feature_dict[f"confidence_{key}"] = value

            # Características avanzadas de producto
            if "average_rating" in rec:
                feature_dict["average_rating"] = rec["average_rating"]
                # Característica derivada: rating alto = mejor
                if rec["average_rating"] > 0:
                    feature_dict["rating_score"] = rec["average_rating"] / 5.0  # Normalizado a [0,1]

            if "num_ratings" in rec:
                feature_dict["num_ratings"] = rec["num_ratings"]
                # Característica derivada: más ratings = más confiable
                feature_dict["rating_confidence"] = min(1.0, rec["num_ratings"] / 100.0)  # Normalizado con saturación

            features.append(feature_dict)

        # Convertir a DataFrame
        df = pd.DataFrame(features)

        # Guardar nombres de características (excluyendo item_id)
        if self.feature_names is None and not df.empty:
            self.feature_names = [col for col in df.columns if col != "item_id"]

        return df

    def train(
        self,
        training_data: List[Dict[str, Any]],
        target_scores: Optional[List[float]] = None,
        timestamp: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Entrena el modelo LTR con datos de entrenamiento.

        Args:
            training_data: Lista de recomendaciones con características
            target_scores: Scores objetivo basados en interacciones reales del usuario.
                           Estos valores deben representar la relevancia real del ítem para el usuario
                           (ej. 1.0 si el usuario interactuó con el ítem en el conjunto de prueba,
                           0.0 si no lo hizo). Si no se proporcionan, se usará una combinación ponderada
                           de los scores de los modelos base, pero esto NO es lo recomendado.
            timestamp: Timestamp para calcular características, evitando feature leakage

        Returns:
            Diccionario con métricas de entrenamiento
        """
        try:
            start_time = time.time()

            # Extraer características
            features_df = self._extract_features(
                training_data, 
                user_id=training_data[0].get("user_id") if training_data else None,
                timestamp=timestamp
            )

            if features_df.empty:
                log_error("No se pudieron extraer características para entrenar el modelo LTR")
                return {"error": "No hay datos de entrenamiento"}

            # Validar que se proporcionen target_scores para entrenamiento robusto
            if target_scores is None:
                log_error("CRÍTICO: No se proporcionaron target_scores para el modelo LTR. " +
                         "El entrenamiento sin target_scores reales compromete significativamente " +
                         "la calidad del modelo. Use DataPreparer.generate_target_scores_for_ltr() " +
                         "para generar target_scores basados en interacciones reales.")
                return {"error": "target_scores requeridos para entrenar modelo LTR robusto"}
            else:
                log_info(f"Entrenando modelo LTR con {len(target_scores)} target_scores basados en interacciones reales.")
                y = np.array(target_scores)
                
                # Validar distribución de target_scores
                unique_scores = np.unique(y)
                if len(unique_scores) == 1:
                    log_warning(f"Todos los target_scores tienen el mismo valor ({unique_scores[0]}). " +
                               "Esto puede indicar un problema en la generación de target_scores.")
                else:
                    log_info(f"Distribución de target_scores: {len(unique_scores)} valores únicos, " +
                            f"rango [{y.min():.3f}, {y.max():.3f}]")

            # Separar características de identificadores
            X = features_df.drop(columns=["item_id"])

            # Normalizar características
            X_scaled = self.scaler.fit_transform(X)

            # Dividir en conjuntos de entrenamiento y validación
            X_train, X_val, y_train, y_val = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42
            )

            # Crear y entrenar modelo
            self.model = self._create_model()
            self.model.fit(X_train, y_train)

            # Evaluar modelo
            train_preds = self.model.predict(X_train)
            val_preds = self.model.predict(X_val)

            train_mse = mean_squared_error(y_train, train_preds)
            val_mse = mean_squared_error(y_val, val_preds)

            # Calcular NDCG para evaluar la calidad del ranking
            try:
                # Reformatear para ndcg_score
                y_true = y_val.reshape(1, -1)
                y_score = val_preds.reshape(1, -1)
                ndcg = ndcg_score(y_true, y_score)
            except Exception as e:
                log_error(f"Error calculando NDCG: {str(e)}")
                ndcg = 0.0

            # Guardar modelo
            self._save_model()

            # Marcar como entrenado
            self.trained = True

            # Calcular tiempo de entrenamiento
            training_time = time.time() - start_time

            # Importancia de características
            feature_importance = {}
            if hasattr(self.model, "feature_importances_"):
                for i, feature in enumerate(self.feature_names):
                    feature_importance[feature] = float(self.model.feature_importances_[i])

            # Retornar métricas
            metrics = {
                "train_mse": float(train_mse),
                "val_mse": float(val_mse),
                "ndcg": float(ndcg),
                "training_time": float(training_time),
                "feature_importance": feature_importance,
                "model_type": self.model_type,
                "num_features": len(self.feature_names),
                "training_samples": len(y_train),
            }

            log_info(f"Modelo LTR entrenado para account_id={self.account_id} con {len(y_train)} muestras")
            log_info(f"Métricas: MSE={val_mse:.4f}, NDCG={ndcg:.4f}")

            return metrics

        except Exception as e:
            log_error(f"Error entrenando modelo LTR: {str(e)}")
            return {"error": str(e)}

    def predict(
        self, 
        recommendations: List[Dict[str, Any]], 
        user_id: Optional[int] = None,
        user_history: Optional[Dict[int, float]] = None,
        timestamp: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Aplica el modelo LTR para rankear recomendaciones.

        Args:
            recommendations: Lista de recomendaciones a rankear
            user_id: ID del usuario
            user_history: Historial de interacciones del usuario
            timestamp: Timestamp actual para calcular características temporales

        Returns:
            Lista de recomendaciones con scores actualizados
        """
        try:
            if not self.trained:
                # Intentar cargar un modelo guardado
                if not self._load_model():
                    log_warning("Modelo LTR no entrenado, usando scores originales")
                    return recommendations

            # Extraer características
            features_df = self._extract_features(
                recommendations,
                user_id=user_id,
                user_history=user_history,
                timestamp=timestamp
            )

            if features_df.empty:
                return recommendations

            # Obtener IDs de items
            item_ids = features_df["item_id"].tolist()

            # Preparar características para predicción
            X = features_df.drop(columns=["item_id"])
            X_scaled = self.scaler.transform(X)

            # Predecir scores
            predicted_scores = self.model.predict(X_scaled)

            # Crear un mapeo de item_id a score predicho
            score_map = {item_id: score for item_id, score in zip(item_ids, predicted_scores)}

            # Actualizar recomendaciones con nuevos scores
            for rec in recommendations:
                item_id = rec.get("item_id")
                if item_id in score_map:
                    # Guardar score original
                    rec["original_score"] = rec.get("score", 0.0)
                    # Actualizar con score predicho
                    rec["score"] = float(score_map[item_id])
                    # Marcar como rankeado por LTR
                    rec["ranked_by"] = "learning_to_rank"

            # Ordenar por nuevo score
            recommendations.sort(key=lambda x: x.get("score", 0), reverse=True)

            return recommendations

        except Exception as e:
            log_error(f"Error aplicando modelo LTR: {str(e)}")
            return recommendations

    def _save_model(self) -> bool:
        """Guarda el modelo entrenado en disco."""
        try:
            if self.model is None:
                return False

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = os.path.join(self.models_dir, f"ltr_model_{timestamp}.joblib")
            scaler_path = os.path.join(self.models_dir, f"ltr_scaler_{timestamp}.joblib")
            metadata_path = os.path.join(self.models_dir, f"ltr_metadata_{timestamp}.joblib")

            # Guardar modelo y scaler
            joblib.dump(self.model, model_path)
            joblib.dump(self.scaler, scaler_path)

            # Guardar metadatos
            metadata = {
                "feature_names": self.feature_names,
                "model_type": self.model_type,
                "timestamp": timestamp,
                "account_id": self.account_id
            }
            joblib.dump(metadata, metadata_path)

            # Actualizar enlaces a los archivos más recientes
            latest_model_path = os.path.join(self.models_dir, "ltr_model_latest.joblib")
            latest_scaler_path = os.path.join(self.models_dir, "ltr_scaler_latest.joblib")
            latest_metadata_path = os.path.join(self.models_dir, "ltr_metadata_latest.joblib")

            # En Windows, no podemos usar symlinks fácilmente, así que copiamos los archivos
            import shutil
            shutil.copy2(model_path, latest_model_path)
            shutil.copy2(scaler_path, latest_scaler_path)
            shutil.copy2(metadata_path, latest_metadata_path)

            log_info(f"Modelo LTR guardado en {model_path}")
            return True

        except Exception as e:
            log_error(f"Error guardando modelo LTR: {str(e)}")
            return False

    def _load_model(self) -> bool:
        """Carga el modelo más reciente desde disco."""
        try:
            latest_model_path = os.path.join(self.models_dir, "ltr_model_latest.joblib")
            latest_scaler_path = os.path.join(self.models_dir, "ltr_scaler_latest.joblib")
            latest_metadata_path = os.path.join(self.models_dir, "ltr_metadata_latest.joblib")

            if not os.path.exists(latest_model_path):
                return False

            # Cargar modelo y scaler
            self.model = joblib.load(latest_model_path)
            self.scaler = joblib.load(latest_scaler_path)

            # Cargar metadatos
            metadata = joblib.load(latest_metadata_path)
            self.feature_names = metadata.get("feature_names")
            self.model_type = metadata.get("model_type")

            self.trained = True
            log_info(f"Modelo LTR cargado desde {latest_model_path}")
            return True

        except Exception as e:
            log_error(f"Error cargando modelo LTR: {str(e)}")
            return False


def extract_user_item_features(
    user_id: int,
    item_id: int,
    user_features: Dict[int, Dict[str, Any]],
    item_features: Dict[int, Dict[str, Any]],
    user_history: Optional[Dict[int, Dict[str, Any]]] = None,
    timestamp: Optional[int] = None
) -> Dict[str, Any]:
    """
    Extrae características de usuario e item para el modelo LTR.

    Args:
        user_id: ID del usuario
        item_id: ID del item
        user_features: Diccionario de características de usuarios
        item_features: Diccionario de características de items
        user_history: Historial de interacciones del usuario
        timestamp: Timestamp actual para calcular características temporales

    Returns:
        Diccionario con características combinadas
    """
    features = {}

    # Características de usuario
    if user_id in user_features:
        for key, value in user_features[user_id].items():
            features[f"user_{key}"] = value

    # Características de item
    if item_id in item_features:
        for key, value in item_features[item_id].items():
            features[f"item_{key}"] = value

    # Características de interacción usuario-item 
    if user_history and item_id in user_history:
        interaction_data = user_history[item_id]
        
        # Marcar que hay interacción previa
        features["has_previous_interaction"] = 1.0
        
        # Extraer datos ricos si están disponibles
        if isinstance(interaction_data, dict):
            if "score" in interaction_data:
                features["previous_interaction_score"] = interaction_data["score"]
            
            if "interaction_count" in interaction_data:
                features["interaction_count"] = interaction_data["interaction_count"]
                features["repeat_interaction"] = 1.0 if interaction_data["interaction_count"] > 1 else 0.0
            
            # Características temporales
            if "timestamp" in interaction_data and timestamp:
                # Tiempo desde la última interacción (en segundos, horas y días)
                seconds_diff = timestamp - interaction_data["timestamp"]
                features["seconds_since_last_interaction"] = seconds_diff
                features["hours_since_last_interaction"] = seconds_diff / 3600
                features["days_since_last_interaction"] = seconds_diff / (3600 * 24)
                
                # Decaimiento temporal exponencial (valorar más las interacciones recientes)
                features["recency_decay_7d"] = np.exp(-features["days_since_last_interaction"] / 7)  # 7 días
                features["recency_decay_30d"] = np.exp(-features["days_since_last_interaction"] / 30)  # 30 días
        else:
            # Si solo tenemos un valor numérico, asumimos que es un score
            features["previous_interaction_score"] = float(interaction_data)
    else:
        features["has_previous_interaction"] = 0.0

    # Características derivadas de la combinación usuario-item
    if "user_preferred_category" in features and "item_category" in features:
        features["category_match"] = 1.0 if features["user_preferred_category"] == features["item_category"] else 0.0

    # Características contextuales si hay timestamp
    if timestamp:
        from datetime import datetime
        dt = datetime.fromtimestamp(timestamp)
        
        # Hora del día y parte del día
        features["hour_of_day"] = dt.hour
        
        # Parte del día 
        if dt.hour < 6:
            features["time_night"] = 1.0
        elif dt.hour < 12:
            features["time_morning"] = 1.0
        elif dt.hour < 18:
            features["time_afternoon"] = 1.0
        else:
            features["time_evening"] = 1.0
        
        # Día de la semana y fin de semana
        features["day_of_week"] = dt.weekday()
        features["is_weekend"] = 1.0 if dt.weekday() >= 5 else 0.0
        
        # Mes y trimestre
        features["month"] = dt.month
        features["quarter"] = (dt.month - 1) // 3 + 1

    return features


def combine_features(
    recommendations: List[Dict[str, Any]],
    user_features: Optional[Dict[int, Dict[str, Any]]] = None,
    item_features: Optional[Dict[int, Dict[str, Any]]] = None,
    user_id: Optional[int] = None,
    user_history: Optional[Dict[int, Dict[str, Any]]] = None,
    timestamp: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Combina características de diferentes fuentes para el modelo LTR.

    Args:
        recommendations: Lista de recomendaciones
        user_features: Diccionario de características de usuarios
        item_features: Diccionario de características de items
        user_id: ID del usuario actual
        user_history: Historial de interacciones del usuario
        timestamp: Timestamp actual para calcular features temporales

    Returns:
        Lista de recomendaciones con características combinadas
    """
    enhanced_recommendations = []

    for rec in recommendations:
        item_id = rec.get("item_id")

        # Copiar recomendación original
        enhanced_rec = rec.copy()

        # Añadir características combinadas
        if user_features and item_features and user_id is not None:
            combined_features = extract_user_item_features(
                user_id, item_id, user_features, item_features, user_history, timestamp
            )
            enhanced_rec["combined_features"] = combined_features
            
        # Añadir información de timestamp si está disponible
        if timestamp is not None:
            enhanced_rec["request_timestamp"] = timestamp
            
        # Añadir información de interacción histórica si está disponible
        if user_history is not None and item_id in user_history:
            # Extraer datos de interacción y añadirlos a la recomendación
            interaction_data = user_history[item_id]
            
            if isinstance(interaction_data, dict):
                # Si ya es un diccionario, copiar los datos relevantes
                if "timestamp" in interaction_data:
                    enhanced_rec["last_interaction_timestamp"] = interaction_data["timestamp"]
                if "score" in interaction_data:
                    enhanced_rec["user_item_score"] = interaction_data["score"]
                if "interaction_count" in interaction_data:
                    enhanced_rec["interaction_count"] = interaction_data["interaction_count"]
            else:
                # Si es solo un valor numérico, tratarlo como score
                enhanced_rec["user_item_score"] = interaction_data

        enhanced_recommendations.append(enhanced_rec)

    return enhanced_recommendations

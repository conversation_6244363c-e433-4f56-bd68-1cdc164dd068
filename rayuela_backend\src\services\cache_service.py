"""
Servicio centralizado para la gestión de caché.

Este servicio centraliza toda la lógica de invalidación de caché, eliminando la 
dispersión y las dependencias frágiles del middleware. Los servicios de negocio
deben llamar explícitamente a este servicio después de operaciones relevantes.
"""
from typing import Optional, Any, Dict, List
from redis.asyncio import Redis
import json

from src.core.redis_utils import get_redis
from src.utils.base_logger import log_info, log_error, log_warning


class CacheService:
    """
    Servicio centralizado para la gestión de caché.
    
    Este servicio proporciona métodos claros y explícitos para invalidar diferentes
    tipos de caché sin depender de la extracción frágil de IDs de solicitudes.
    """
    
    def __init__(self):
        """Inicializa el servicio de caché."""
        self._redis: Optional[Redis] = None
    
    async def _get_redis(self) -> Redis:
        """Obtiene la conexión a Redis."""
        if not self._redis:
            self._redis = await get_redis()
        return self._redis
    
    # ==========================================
    # INVALIDACIÓN DE CACHÉ DE RECOMENDACIONES
    # ==========================================
    
    async def invalidate_user_recommendations(self, account_id: int, user_id: int) -> bool:
        """
        Invalida la caché de recomendaciones para un usuario específico.
        
        Este método invalida todas las cachés relacionadas con las recomendaciones
        de un usuario específico, incluyendo factores de modelos ML y resultados
        de recomendaciones previas.
        
        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            # Patrones de caché de recomendaciones para usuarios específicos
            patterns_to_invalidate = [
                f"recommendations:{account_id}:{user_id}:*",
                f"user_factors:{account_id}:{user_id}:*",
                f"user_recommendations:{account_id}:{user_id}:*",
                f"collaborative_recs:{account_id}:{user_id}:*",
                f"content_recs:{account_id}:{user_id}:*",
                f"hybrid_recs:{account_id}:{user_id}:*",
            ]
            
            total_deleted = 0
            for pattern in patterns_to_invalidate:
                keys = await redis.keys(pattern)
                if keys:
                    deleted = await redis.delete(*keys)
                    total_deleted += deleted
            
            log_info(f"Caché de recomendaciones invalidada para user_id={user_id}, account_id={account_id}. Claves eliminadas: {total_deleted}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de recomendaciones para user_id={user_id}, account_id={account_id}: {str(e)}")
            return False
    
    async def invalidate_account_recommendations(self, account_id: int) -> bool:
        """
        Invalida toda la caché de recomendaciones para una cuenta.
        
        Args:
            account_id: ID de la cuenta
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            # Patrones de caché de recomendaciones para toda la cuenta
            patterns_to_invalidate = [
                f"recommendations:{account_id}:*",
                f"user_factors:{account_id}:*",
                f"user_recommendations:{account_id}:*",
                f"collaborative_recs:{account_id}:*",
                f"content_recs:{account_id}:*",
                f"hybrid_recs:{account_id}:*",
            ]
            
            total_deleted = 0
            for pattern in patterns_to_invalidate:
                keys = await redis.keys(pattern)
                if keys:
                    deleted = await redis.delete(*keys)
                    total_deleted += deleted
            
            log_info(f"Caché de recomendaciones invalidada para account_id={account_id}. Claves eliminadas: {total_deleted}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de recomendaciones para account_id={account_id}: {str(e)}")
            return False
    
    # ==========================================
    # INVALIDACIÓN DE CACHÉ DE MODELOS ML
    # ==========================================
    
    async def invalidate_model_cache(self, account_id: int, model_type: Optional[str] = None) -> bool:
        """
        Invalida la caché de modelos ML.
        
        Args:
            account_id: ID de la cuenta
            model_type: Tipo de modelo específico (opcional)
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            if model_type:
                # Invalidar un tipo específico de modelo
                pattern = f"model:{account_id}:{model_type}:*"
            else:
                # Invalidar todos los modelos de la cuenta
                pattern = f"model:{account_id}:*"
            
            keys = await redis.keys(pattern)
            deleted = 0
            if keys:
                deleted = await redis.delete(*keys)
            
            log_info(f"Caché de modelos invalidada para account_id={account_id}, model_type={model_type}. Claves eliminadas: {deleted}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de modelos para account_id={account_id}, model_type={model_type}: {str(e)}")
            return False
    
    async def invalidate_metrics_cache(self, account_id: int, metric_type: Optional[str] = None) -> bool:
        """
        Invalida la caché de métricas.
        
        Args:
            account_id: ID de la cuenta
            metric_type: Tipo de métrica específico (opcional)
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            if metric_type:
                # Invalidar un tipo específico de métrica
                pattern = f"metric:{account_id}:{metric_type}:*"
            else:
                # Invalidar todas las métricas de la cuenta
                pattern = f"metric:{account_id}:*"
            
            keys = await redis.keys(pattern)
            deleted = 0
            if keys:
                deleted = await redis.delete(*keys)
            
            log_info(f"Caché de métricas invalidada para account_id={account_id}, metric_type={metric_type}. Claves eliminadas: {deleted}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de métricas para account_id={account_id}, metric_type={metric_type}: {str(e)}")
            return False
    
    # ==========================================
    # INVALIDACIÓN DE CACHÉ DE USAGE METER
    # ==========================================
    
    async def invalidate_account_usage_cache(self, account_id: int) -> bool:
        """
        Invalida la caché de uso de la cuenta (usage meter).
        
        Args:
            account_id: ID de la cuenta
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            # Patrones de caché de uso de cuenta
            patterns_to_invalidate = [
                f"subscription:{account_id}",
                f"usage:{account_id}:*",
                f"rate_limit:{account_id}:*",
                f"monthly_counter:{account_id}:*",
            ]
            
            total_deleted = 0
            for pattern in patterns_to_invalidate:
                if pattern.endswith(":*"):
                    keys = await redis.keys(pattern)
                    if keys:
                        deleted = await redis.delete(*keys)
                        total_deleted += deleted
                else:
                    # Clave específica sin patrón
                    deleted = await redis.delete(pattern)
                    total_deleted += deleted
            
            log_info(f"Caché de uso invalidada para account_id={account_id}. Claves eliminadas: {total_deleted}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de uso para account_id={account_id}: {str(e)}")
            return False
    
    async def invalidate_api_key_cache(self, api_key: str) -> bool:
        """
        Invalida la caché de una API key específica.
        
        Args:
            api_key: API key
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            redis = await self._get_redis()
            
            cache_key = f"account:{api_key}"
            deleted = await redis.delete(cache_key)
            
            if deleted:
                log_info(f"Caché de API key invalidada: {api_key[:8]}...")
            
            return True
            
        except Exception as e:
            log_error(f"Error invalidando caché de API key {api_key[:8]}...: {str(e)}")
            return False
    
    # ==========================================
    # MÉTODOS DE CONVENIENCIA PARA SERVICIOS
    # ==========================================
    
    async def invalidate_after_interaction(self, account_id: int, user_id: int) -> bool:
        """
        Invalida las cachés relevantes después de una interacción de usuario.
        
        Este método debe ser llamado por InteractionService después de crear
        o actualizar una interacción.
        
        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Invalidar recomendaciones del usuario específico
            await self.invalidate_user_recommendations(account_id, user_id)
            
            log_info(f"Cachés invalidadas después de interacción para user_id={user_id}, account_id={account_id}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando cachés después de interacción: {str(e)}")
            return False
    
    async def invalidate_after_product_update(self, account_id: int, product_id: int) -> bool:
        """
        Invalida las cachés relevantes después de actualizar un producto.
        
        Este método debe ser llamado por ProductService después de actualizar
        un producto.
        
        Args:
            account_id: ID de la cuenta
            product_id: ID del producto
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Invalidar todas las recomendaciones de la cuenta ya que un producto cambió
            await self.invalidate_account_recommendations(account_id)
            
            # Invalidar caché específica del producto si existe
            redis = await self._get_redis()
            product_keys = await redis.keys(f"product:{account_id}:{product_id}:*")
            if product_keys:
                await redis.delete(*product_keys)
            
            log_info(f"Cachés invalidadas después de actualización de producto para product_id={product_id}, account_id={account_id}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando cachés después de actualización de producto: {str(e)}")
            return False
    
    async def invalidate_after_model_training(self, account_id: int) -> bool:
        """
        Invalida las cachés relevantes después del entrenamiento de modelos.
        
        Este método debe ser llamado por TrainingPipeline después de entrenar
        nuevos modelos.
        
        Args:
            account_id: ID de la cuenta
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Invalidar todas las recomendaciones y modelos de la cuenta
            await self.invalidate_account_recommendations(account_id)
            await self.invalidate_model_cache(account_id)
            await self.invalidate_metrics_cache(account_id)
            
            log_info(f"Cachés invalidadas después de entrenamiento de modelo para account_id={account_id}")
            return True
            
        except Exception as e:
            log_error(f"Error invalidando cachés después de entrenamiento de modelo: {str(e)}")
            return False
    
    # ==========================================
    # MÉTODOS HEREDADOS (COMPATIBILIDAD)
    # ==========================================
    
    async def invalidate_user_cache(self, account_id: int, user_id: int) -> bool:
        """
        Método heredado para compatibilidad.
        Redirige a invalidate_user_recommendations.
        """
        log_warning("invalidate_user_cache está deprecated. Use invalidate_user_recommendations en su lugar.")
        return await self.invalidate_user_recommendations(account_id, user_id)
    
    async def invalidate_account_cache(self, account_id: int) -> bool:
        """
        Método heredado para compatibilidad.
        Redirige a invalidate_account_recommendations.
        """
        log_warning("invalidate_account_cache está deprecated. Use invalidate_account_recommendations en su lugar.")
        return await self.invalidate_account_recommendations(account_id)


# Instancia singleton para fácil acceso
_cache_service_instance = None

async def get_cache_service() -> CacheService:
    """Obtiene la instancia singleton del servicio de caché."""
    global _cache_service_instance
    if _cache_service_instance is None:
        _cache_service_instance = CacheService()
    return _cache_service_instance

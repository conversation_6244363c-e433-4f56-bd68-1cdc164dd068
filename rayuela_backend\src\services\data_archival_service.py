"""
Servicio de archivado de datos históricos a Google Cloud Storage.

Este servicio maneja la exportación de datos históricos de tablas de alto volumen
(interactions, audit_logs) a GCS antes de su eliminación, proporcionando una
estrategia de archivado costo-efectiva.
"""

import asyncio
import json
import os
import tempfile
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
from google.cloud import storage
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.config import settings
from src.db.session import get_async_session_factory
from src.utils.base_logger import log_info, log_error, log_warning


class DataArchivalService:
    """Servicio para archivar datos históricos en Google Cloud Storage."""
    
    def __init__(self):
        """Inicializa el servicio de archivado de datos."""
        self.storage_client = storage.Client() if settings.ENV == "production" else None
        self.bucket_name = settings.GCS_BUCKET_NAME
        self.archival_path = settings.GCS_ARCHIVAL_PATH
        self.format = settings.ARCHIVAL_FORMAT.lower()
        self.compression = settings.ARCHIVAL_COMPRESSION.lower()
        self.verify_export = settings.ARCHIVAL_VERIFY_EXPORT
        
        # Crear directorio local para desarrollo si no existe
        if settings.ENV != "production":
            os.makedirs(os.path.join("data", self.archival_path), exist_ok=True)
    
    async def archive_audit_logs(
        self,
        cutoff_date: datetime,
        account_id: Optional[int] = None,
        batch_size: int = 10000
    ) -> Dict[str, Any]:
        """
        Archiva logs de auditoría más antiguos que la fecha de corte.
        
        Args:
            cutoff_date: Fecha límite para archivar datos
            account_id: ID de cuenta específica (None para todas)
            batch_size: Tamaño del lote para procesamiento
            
        Returns:
            Diccionario con información sobre la operación de archivado
        """
        start_time = datetime.now(timezone.utc)
        table_name = "audit_logs"
        
        try:
            log_info(f"Starting archival of {table_name} older than {cutoff_date}")
            
            # Construir consulta base
            base_query = """
                SELECT id, account_id, user_id, action, resource_type, resource_id,
                       details, ip_address, user_agent, created_at
                FROM audit_logs 
                WHERE created_at < :cutoff_date
            """
            
            if account_id is not None:
                base_query += " AND account_id = :account_id"
            
            # Contar total de registros a archivar
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            
            async_session_factory = get_async_session_factory()
            async with async_session_factory() as db:
                # Obtener total de registros
                params = {"cutoff_date": cutoff_date}
                if account_id is not None:
                    params["account_id"] = account_id
                    
                result = await db.execute(text(count_query), params)
                total_records = result.scalar()
                
                if total_records == 0:
                    log_info(f"No {table_name} records found to archive")
                    return {
                        "table": table_name,
                        "archived_count": 0,
                        "total_count": 0,
                        "archive_path": None,
                        "success": True,
                        "duration_seconds": 0
                    }
                
                log_info(f"Found {total_records} {table_name} records to archive")
                
                # Procesar en lotes y archivar
                archived_count = 0
                archive_files = []
                
                for offset in range(0, total_records, batch_size):
                    batch_query = f"{base_query} ORDER BY created_at LIMIT :batch_size OFFSET :offset"
                    batch_params = {**params, "batch_size": batch_size, "offset": offset}
                    
                    # Obtener datos del lote
                    result = await db.execute(text(batch_query), batch_params)
                    batch_data = result.fetchall()
                    
                    if not batch_data:
                        break
                    
                    # Convertir a DataFrame
                    df = pd.DataFrame([dict(row._mapping) for row in batch_data])
                    
                    # Generar nombre de archivo para el lote
                    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
                    account_suffix = f"_account_{account_id}" if account_id else "_all_accounts"
                    batch_suffix = f"_batch_{offset//batch_size + 1}"
                    
                    file_name = f"{table_name}_{timestamp}{account_suffix}{batch_suffix}.{self.format}"
                    archive_path = f"{self.archival_path}/{table_name}/{cutoff_date.strftime('%Y/%m/%d')}/{file_name}"
                    
                    # Archivar el lote
                    success = await self._export_dataframe_to_gcs(df, archive_path)
                    
                    if success:
                        archived_count += len(batch_data)
                        archive_files.append(archive_path)
                        log_info(f"Archived batch {offset//batch_size + 1}: {len(batch_data)} records to {archive_path}")
                    else:
                        log_error(f"Failed to archive batch {offset//batch_size + 1}")
                        break
                
                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                
                if archived_count == total_records:
                    log_info(f"Successfully archived {archived_count}/{total_records} {table_name} records in {duration:.2f}s")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "total_count": total_records,
                        "archive_files": archive_files,
                        "cutoff_date": cutoff_date.isoformat(),
                        "success": True,
                        "duration_seconds": duration
                    }
                else:
                    log_error(f"Partial archival: {archived_count}/{total_records} {table_name} records")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "total_count": total_records,
                        "archive_files": archive_files,
                        "error": "Partial archival completed",
                        "success": False,
                        "duration_seconds": duration
                    }
                    
        except Exception as e:
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            error_msg = f"Error archiving {table_name}: {str(e)}"
            log_error(error_msg)
            return {
                "table": table_name,
                "archived_count": 0,
                "total_count": 0,
                "error": error_msg,
                "success": False,
                "duration_seconds": duration
            }
    
    async def archive_interactions(
        self,
        cutoff_date: datetime,
        account_id: Optional[int] = None,
        batch_size: int = 10000
    ) -> Dict[str, Any]:
        """
        Archiva interacciones más antiguas que la fecha de corte.
        
        Args:
            cutoff_date: Fecha límite para archivar datos
            account_id: ID de cuenta específica (None para todas)
            batch_size: Tamaño del lote para procesamiento
            
        Returns:
            Diccionario con información sobre la operación de archivado
        """
        start_time = datetime.now(timezone.utc)
        table_name = "interactions"
        
        try:
            log_info(f"Starting archival of {table_name} older than {cutoff_date}")
            
            # Construir consulta base
            base_query = """
                SELECT id, account_id, end_user_id, product_id, interaction_type,
                       rating, metadata, created_at, updated_at
                FROM interactions 
                WHERE created_at < :cutoff_date
            """
            
            if account_id is not None:
                base_query += " AND account_id = :account_id"
            
            # Contar total de registros a archivar
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            
            async_session_factory = get_async_session_factory()
            async with async_session_factory() as db:
                # Obtener total de registros
                params = {"cutoff_date": cutoff_date}
                if account_id is not None:
                    params["account_id"] = account_id
                    
                result = await db.execute(text(count_query), params)
                total_records = result.scalar()
                
                if total_records == 0:
                    log_info(f"No {table_name} records found to archive")
                    return {
                        "table": table_name,
                        "archived_count": 0,
                        "total_count": 0,
                        "archive_path": None,
                        "success": True,
                        "duration_seconds": 0
                    }
                
                log_info(f"Found {total_records} {table_name} records to archive")
                
                # Procesar en lotes y archivar
                archived_count = 0
                archive_files = []
                
                for offset in range(0, total_records, batch_size):
                    batch_query = f"{base_query} ORDER BY created_at LIMIT :batch_size OFFSET :offset"
                    batch_params = {**params, "batch_size": batch_size, "offset": offset}
                    
                    # Obtener datos del lote
                    result = await db.execute(text(batch_query), batch_params)
                    batch_data = result.fetchall()
                    
                    if not batch_data:
                        break
                    
                    # Convertir a DataFrame
                    df = pd.DataFrame([dict(row._mapping) for row in batch_data])
                    
                    # Generar nombre de archivo para el lote
                    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
                    account_suffix = f"_account_{account_id}" if account_id else "_all_accounts"
                    batch_suffix = f"_batch_{offset//batch_size + 1}"
                    
                    file_name = f"{table_name}_{timestamp}{account_suffix}{batch_suffix}.{self.format}"
                    archive_path = f"{self.archival_path}/{table_name}/{cutoff_date.strftime('%Y/%m/%d')}/{file_name}"
                    
                    # Archivar el lote
                    success = await self._export_dataframe_to_gcs(df, archive_path)
                    
                    if success:
                        archived_count += len(batch_data)
                        archive_files.append(archive_path)
                        log_info(f"Archived batch {offset//batch_size + 1}: {len(batch_data)} records to {archive_path}")
                    else:
                        log_error(f"Failed to archive batch {offset//batch_size + 1}")
                        break
                
                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                
                if archived_count == total_records:
                    log_info(f"Successfully archived {archived_count}/{total_records} {table_name} records in {duration:.2f}s")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "total_count": total_records,
                        "archive_files": archive_files,
                        "cutoff_date": cutoff_date.isoformat(),
                        "success": True,
                        "duration_seconds": duration
                    }
                else:
                    log_error(f"Partial archival: {archived_count}/{total_records} {table_name} records")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "total_count": total_records,
                        "archive_files": archive_files,
                        "error": "Partial archival completed",
                        "success": False,
                        "duration_seconds": duration
                    }
                    
        except Exception as e:
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            error_msg = f"Error archiving {table_name}: {str(e)}"
            log_error(error_msg)
            return {
                "table": table_name,
                "archived_count": 0,
                "total_count": 0,
                "error": error_msg,
                "success": False,
                "duration_seconds": duration
            }

    async def _export_dataframe_to_gcs(self, df: pd.DataFrame, archive_path: str) -> bool:
        """
        Exporta un DataFrame a GCS en el formato especificado.

        Args:
            df: DataFrame a exportar
            archive_path: Ruta de destino en GCS

        Returns:
            True si la exportación fue exitosa, False en caso contrario
        """
        try:
            if settings.ENV == "production":
                return await self._export_to_gcs_production(df, archive_path)
            else:
                return await self._export_to_local_storage(df, archive_path)
        except Exception as e:
            log_error(f"Error exporting DataFrame to {archive_path}: {str(e)}")
            return False

    async def _export_to_gcs_production(self, df: pd.DataFrame, archive_path: str) -> bool:
        """Exporta DataFrame a GCS en producción."""
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(archive_path)

            # Crear archivo temporal
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{self.format}") as temp_file:
                temp_path = temp_file.name

                # Exportar según el formato
                if self.format == "parquet":
                    compression_engine = self.compression if self.compression != "none" else None
                    df.to_parquet(temp_path, compression=compression_engine, index=False)
                elif self.format == "csv":
                    compression_param = self.compression if self.compression != "none" else None
                    df.to_csv(temp_path, compression=compression_param, index=False)
                elif self.format == "json":
                    compression_param = self.compression if self.compression != "none" else None
                    df.to_json(temp_path, compression=compression_param, orient="records", date_format="iso")
                else:
                    raise ValueError(f"Unsupported format: {self.format}")

                # Subir a GCS usando asyncio.to_thread para no bloquear
                await asyncio.to_thread(blob.upload_from_filename, temp_path)

                # Limpiar archivo temporal
                os.unlink(temp_path)

                # Verificar exportación si está habilitado
                if self.verify_export:
                    return await self._verify_gcs_export(blob, len(df))

                return True

        except Exception as e:
            log_error(f"Error exporting to GCS production: {str(e)}")
            return False

    async def _export_to_local_storage(self, df: pd.DataFrame, archive_path: str) -> bool:
        """Exporta DataFrame a almacenamiento local para desarrollo."""
        try:
            local_path = os.path.join("data", archive_path)

            # Crear directorio si no existe
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # Exportar según el formato
            if self.format == "parquet":
                compression_engine = self.compression if self.compression != "none" else None
                await asyncio.to_thread(df.to_parquet, local_path, compression=compression_engine, index=False)
            elif self.format == "csv":
                compression_param = self.compression if self.compression != "none" else None
                await asyncio.to_thread(df.to_csv, local_path, compression=compression_param, index=False)
            elif self.format == "json":
                compression_param = self.compression if self.compression != "none" else None
                await asyncio.to_thread(df.to_json, local_path, compression=compression_param, orient="records", date_format="iso")
            else:
                raise ValueError(f"Unsupported format: {self.format}")

            log_info(f"Exported {len(df)} records to local storage: {local_path}")
            return True

        except Exception as e:
            log_error(f"Error exporting to local storage: {str(e)}")
            return False

    async def _verify_gcs_export(self, blob: storage.Blob, expected_records: int) -> bool:
        """
        Verifica que la exportación a GCS fue exitosa.

        Args:
            blob: Blob de GCS a verificar
            expected_records: Número esperado de registros

        Returns:
            True si la verificación es exitosa, False en caso contrario
        """
        try:
            # Verificar que el blob existe y tiene contenido
            await asyncio.to_thread(blob.reload)

            if not blob.exists():
                log_error(f"Blob {blob.name} does not exist after upload")
                return False

            if blob.size == 0:
                log_error(f"Blob {blob.name} is empty after upload")
                return False

            # Para archivos pequeños, podemos verificar el contenido
            if blob.size < 10 * 1024 * 1024:  # 10MB
                try:
                    content = await asyncio.to_thread(blob.download_as_bytes)

                    if self.format == "parquet":
                        # Verificar que el archivo Parquet es válido
                        with tempfile.NamedTemporaryFile(suffix=".parquet") as temp_file:
                            temp_file.write(content)
                            temp_file.flush()

                            # Leer y verificar número de registros
                            df_verify = await asyncio.to_thread(pd.read_parquet, temp_file.name)
                            actual_records = len(df_verify)

                            if actual_records != expected_records:
                                log_warning(f"Record count mismatch: expected {expected_records}, got {actual_records}")
                                return False

                    elif self.format == "csv":
                        # Verificar CSV
                        with tempfile.NamedTemporaryFile(mode='wb', suffix=".csv") as temp_file:
                            temp_file.write(content)
                            temp_file.flush()

                            df_verify = await asyncio.to_thread(pd.read_csv, temp_file.name)
                            actual_records = len(df_verify)

                            if actual_records != expected_records:
                                log_warning(f"Record count mismatch: expected {expected_records}, got {actual_records}")
                                return False

                    log_info(f"Export verification successful: {expected_records} records in {blob.name}")
                    return True

                except Exception as e:
                    log_warning(f"Could not verify content of {blob.name}: {str(e)}")
                    # Si no podemos verificar el contenido, al menos verificamos que existe y no está vacío
                    return True
            else:
                # Para archivos grandes, solo verificamos existencia y tamaño
                log_info(f"Large file verification: {blob.name} exists with size {blob.size} bytes")
                return True

        except Exception as e:
            log_error(f"Error verifying GCS export: {str(e)}")
            return False

    async def list_archived_files(
        self,
        table_name: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        account_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Lista archivos archivados para una tabla específica.

        Args:
            table_name: Nombre de la tabla
            start_date: Fecha de inicio para filtrar
            end_date: Fecha de fin para filtrar
            account_id: ID de cuenta para filtrar

        Returns:
            Lista de archivos archivados con metadatos
        """
        try:
            if settings.ENV == "production":
                return await self._list_gcs_files(table_name, start_date, end_date, account_id)
            else:
                return await self._list_local_files(table_name, start_date, end_date, account_id)
        except Exception as e:
            log_error(f"Error listing archived files: {str(e)}")
            return []

    async def _list_gcs_files(
        self,
        table_name: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        account_id: Optional[int]
    ) -> List[Dict[str, Any]]:
        """Lista archivos en GCS."""
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            prefix = f"{self.archival_path}/{table_name}/"

            blobs = await asyncio.to_thread(bucket.list_blobs, prefix=prefix)
            files = []

            for blob in blobs:
                # Filtrar por fechas y account_id si se especifican
                if account_id and f"_account_{account_id}" not in blob.name:
                    continue

                file_info = {
                    "name": blob.name,
                    "size": blob.size,
                    "created": blob.time_created,
                    "updated": blob.updated,
                    "path": f"gs://{self.bucket_name}/{blob.name}"
                }

                # Filtrar por fechas si se especifican
                if start_date and blob.time_created < start_date.replace(tzinfo=timezone.utc):
                    continue
                if end_date and blob.time_created > end_date.replace(tzinfo=timezone.utc):
                    continue

                files.append(file_info)

            return sorted(files, key=lambda x: x["created"], reverse=True)

        except Exception as e:
            log_error(f"Error listing GCS files: {str(e)}")
            return []

    async def _list_local_files(
        self,
        table_name: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        account_id: Optional[int]
    ) -> List[Dict[str, Any]]:
        """Lista archivos locales."""
        try:
            local_path = os.path.join("data", self.archival_path, table_name)

            if not os.path.exists(local_path):
                return []

            files = []
            for root, dirs, filenames in os.walk(local_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)

                    # Filtrar por account_id si se especifica
                    if account_id and f"_account_{account_id}" not in filename:
                        continue

                    stat = os.stat(file_path)
                    created = datetime.fromtimestamp(stat.st_ctime, tz=timezone.utc)

                    # Filtrar por fechas si se especifican
                    if start_date and created < start_date:
                        continue
                    if end_date and created > end_date:
                        continue

                    files.append({
                        "name": os.path.relpath(file_path, os.path.join("data", self.archival_path)),
                        "size": stat.st_size,
                        "created": created,
                        "updated": datetime.fromtimestamp(stat.st_mtime, tz=timezone.utc),
                        "path": file_path
                    })

            return sorted(files, key=lambda x: x["created"], reverse=True)

        except Exception as e:
            log_error(f"Error listing local files: {str(e)}")
            return []

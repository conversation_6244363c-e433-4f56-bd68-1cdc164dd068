"""
Servicio unificado para el manejo de métricas de ML/Training.

Este servicio reemplaza el uso directo de campos JSONB redundantes
y centraliza el acceso a métricas a través de las tablas especializadas.
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from src.db.models.model_metadata import ModelMetadata
from src.db.models.model_metric import ModelMetric
from src.db.models.training_metrics import TrainingMetrics
from src.db.models.training_job import TrainingJob
from src.utils.base_logger import log_info, log_error, log_warning


class MetricsService:
    """
    Servicio centralizado para el manejo de métricas de modelos y entrenamiento.
    
    Elimina la dependencia en campos JSONB redundantes y proporciona
    una interfaz unificada para todas las operaciones de métricas.
    """

    def __init__(self):
        pass

    async def save_model_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        model_metadata_id: int,
        metrics: Dict[str, Union[float, int]],
        metric_type: str = "offline"
    ) -> List[ModelMetric]:
        """
        Guarda métricas de modelo en la tabla ModelMetric.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_metadata_id: ID del modelo
            metrics: Diccionario de métricas {nombre: valor}
            metric_type: Tipo de métrica (offline, online, system)
            
        Returns:
            Lista de métricas guardadas
        """
        try:
            saved_metrics = []
            
            for metric_name, metric_value in metrics.items():
                # Convertir valor a float si es posible
                try:
                    numeric_value = float(metric_value)
                except (ValueError, TypeError):
                    log_warning(f"Skipping non-numeric metric {metric_name}: {metric_value}")
                    continue
                
                # Crear nueva métrica
                model_metric = ModelMetric(
                    account_id=account_id,
                    model_metadata_id=model_metadata_id,
                    metric_name=metric_name,
                    metric_value=numeric_value,
                    metric_type=metric_type,
                    timestamp=datetime.utcnow()
                )
                
                # Mapear métricas conocidas a campos específicos
                if metric_name == "ndcg":
                    model_metric.ndcg = numeric_value
                elif metric_name == "diversity":
                    model_metric.diversity = numeric_value
                elif metric_name == "novelty":
                    model_metric.novelty = numeric_value
                elif metric_name == "coverage":
                    model_metric.coverage = numeric_value
                elif metric_name == "latency_ms":
                    model_metric.latency_ms = numeric_value
                elif metric_name == "throughput":
                    model_metric.throughput = numeric_value
                
                db.add(model_metric)
                saved_metrics.append(model_metric)
            
            await db.commit()
            
            # Actualizar el resumen en ModelMetadata
            await self._update_model_metrics_summary(db, account_id, model_metadata_id)
            
            log_info(f"Saved {len(saved_metrics)} model metrics for model {model_metadata_id}")
            return saved_metrics
            
        except Exception as e:
            await db.rollback()
            log_error(f"Error saving model metrics: {str(e)}")
            raise

    async def save_training_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        model_id: int,
        metrics: Dict[str, Union[float, int, str]]
    ) -> TrainingMetrics:
        """
        Guarda métricas de entrenamiento en la tabla TrainingMetrics.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_id: ID del modelo
            metrics: Diccionario de métricas de entrenamiento
            
        Returns:
            Métricas de entrenamiento guardadas
        """
        try:
            # Extraer métricas conocidas
            training_metrics = TrainingMetrics(
                account_id=account_id,
                model_id=model_id,
                accuracy=metrics.get("accuracy"),
                precision=metrics.get("precision"),
                recall=metrics.get("recall"),
                f1=metrics.get("f1"),
                training_time=metrics.get("training_time"),
                model_size_mb=metrics.get("model_size_mb"),
                additional_metrics=metrics,  # Guardar métricas adicionales
                timestamp=datetime.utcnow()
            )
            
            db.add(training_metrics)
            await db.commit()
            await db.refresh(training_metrics)
            
            log_info(f"Saved training metrics for model {model_id}")
            return training_metrics
            
        except Exception as e:
            await db.rollback()
            log_error(f"Error saving training metrics: {str(e)}")
            raise

    async def get_model_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        model_metadata_id: int,
        metric_type: Optional[str] = None,
        metric_names: Optional[List[str]] = None
    ) -> List[ModelMetric]:
        """
        Obtiene métricas de modelo desde ModelMetric.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_metadata_id: ID del modelo
            metric_type: Filtrar por tipo de métrica
            metric_names: Filtrar por nombres específicos de métricas
            
        Returns:
            Lista de métricas del modelo
        """
        try:
            query = select(ModelMetric).where(
                and_(
                    ModelMetric.account_id == account_id,
                    ModelMetric.model_metadata_id == model_metadata_id
                )
            )
            
            if metric_type:
                query = query.where(ModelMetric.metric_type == metric_type)
                
            if metric_names:
                query = query.where(ModelMetric.metric_name.in_(metric_names))
                
            query = query.order_by(desc(ModelMetric.timestamp))
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            log_error(f"Error getting model metrics: {str(e)}")
            return []

    async def get_training_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        model_id: int,
        limit: int = 10
    ) -> List[TrainingMetrics]:
        """
        Obtiene métricas de entrenamiento desde TrainingMetrics.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_id: ID del modelo
            limit: Límite de resultados
            
        Returns:
            Lista de métricas de entrenamiento
        """
        try:
            query = select(TrainingMetrics).where(
                and_(
                    TrainingMetrics.account_id == account_id,
                    TrainingMetrics.model_id == model_id
                )
            ).order_by(desc(TrainingMetrics.timestamp)).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            log_error(f"Error getting training metrics: {str(e)}")
            return []

    async def get_metrics_summary(
        self,
        db: AsyncSession,
        account_id: int,
        model_metadata_id: int
    ) -> Dict[str, Any]:
        """
        Obtiene un resumen completo de métricas para un modelo.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_metadata_id: ID del modelo
            
        Returns:
            Diccionario con resumen de métricas
        """
        try:
            # Obtener métricas del modelo
            model_metrics = await self.get_model_metrics(
                db, account_id, model_metadata_id
            )
            
            # Obtener métricas de entrenamiento
            training_metrics = await self.get_training_metrics(
                db, account_id, model_metadata_id, limit=1
            )
            
            # Organizar métricas por tipo
            metrics_by_type = {}
            for metric in model_metrics:
                if metric.metric_type not in metrics_by_type:
                    metrics_by_type[metric.metric_type] = {}
                metrics_by_type[metric.metric_type][metric.metric_name] = metric.metric_value
            
            # Incluir métricas de entrenamiento más recientes
            latest_training = training_metrics[0] if training_metrics else None
            training_summary = {}
            if latest_training:
                training_summary = {
                    "accuracy": latest_training.accuracy,
                    "precision": latest_training.precision,
                    "recall": latest_training.recall,
                    "f1": latest_training.f1,
                    "training_time": latest_training.training_time,
                    "model_size_mb": latest_training.model_size_mb,
                    "timestamp": latest_training.timestamp.isoformat()
                }
            
            return {
                "model_metrics": metrics_by_type,
                "training_metrics": training_summary,
                "total_metrics_count": len(model_metrics),
                "last_updated": max(
                    [m.timestamp for m in model_metrics] + 
                    ([latest_training.timestamp] if latest_training else [])
                ).isoformat() if model_metrics or latest_training else None
            }
            
        except Exception as e:
            log_error(f"Error getting metrics summary: {str(e)}")
            return {}

    async def compare_models(
        self,
        db: AsyncSession,
        account_id: int,
        model_ids: List[int],
        metric_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Compara métricas entre múltiples modelos.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_ids: Lista de IDs de modelos a comparar
            metric_names: Métricas específicas a comparar
            
        Returns:
            Diccionario con comparación de métricas
        """
        try:
            comparison = {}
            
            for model_id in model_ids:
                model_metrics = await self.get_model_metrics(
                    db, account_id, model_id, metric_names=metric_names
                )
                
                # Obtener la métrica más reciente para cada nombre
                latest_metrics = {}
                for metric in model_metrics:
                    if (metric.metric_name not in latest_metrics or 
                        metric.timestamp > latest_metrics[metric.metric_name]["timestamp"]):
                        latest_metrics[metric.metric_name] = {
                            "value": metric.metric_value,
                            "timestamp": metric.timestamp,
                            "type": metric.metric_type
                        }
                
                comparison[f"model_{model_id}"] = latest_metrics
            
            return comparison
            
        except Exception as e:
            log_error(f"Error comparing models: {str(e)}")
            return {}

    async def _update_model_metrics_summary(
        self,
        db: AsyncSession,
        account_id: int,
        model_metadata_id: int
    ) -> None:
        """
        Actualiza el resumen de métricas en ModelMetadata.performance_metrics.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_metadata_id: ID del modelo
        """
        try:
            # Contar métricas totales
            metrics_count_query = select(func.count(ModelMetric.id)).where(
                and_(
                    ModelMetric.account_id == account_id,
                    ModelMetric.model_metadata_id == model_metadata_id
                )
            )
            result = await db.execute(metrics_count_query)
            metrics_count = result.scalar()
            
            # Obtener métricas más recientes por tipo
            latest_metrics_query = select(
                ModelMetric.metric_type,
                func.max(ModelMetric.timestamp).label("latest_timestamp")
            ).where(
                and_(
                    ModelMetric.account_id == account_id,
                    ModelMetric.model_metadata_id == model_metadata_id
                )
            ).group_by(ModelMetric.metric_type)
            
            result = await db.execute(latest_metrics_query)
            metrics_by_type = {row.metric_type: row.latest_timestamp for row in result}
            
            # Actualizar el resumen en ModelMetadata
            summary = {
                "summary_generated_at": datetime.utcnow().isoformat(),
                "detailed_metrics_in_model_metrics": True,
                "metrics_count": metrics_count,
                "metrics_by_type": {
                    metric_type: timestamp.isoformat() 
                    for metric_type, timestamp in metrics_by_type.items()
                }
            }
            
            # Actualizar el registro
            update_query = select(ModelMetadata).where(
                and_(
                    ModelMetadata.account_id == account_id,
                    ModelMetadata.id == model_metadata_id
                )
            )
            result = await db.execute(update_query)
            model_metadata = result.scalar_one_or_none()
            
            if model_metadata:
                model_metadata.performance_metrics = summary
                await db.commit()
                
        except Exception as e:
            log_error(f"Error updating metrics summary: {str(e)}")
            # No propagar el error para no interrumpir el flujo principal

    async def cleanup_old_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        days_to_keep: int = 90
    ) -> Dict[str, int]:
        """
        Limpia métricas antiguas para optimizar almacenamiento.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            days_to_keep: Días de métricas a conservar
            
        Returns:
            Diccionario con estadísticas de limpieza
        """
        try:
            cutoff_date = datetime.utcnow().replace(day=1) - timedelta(days=days_to_keep)
            
            # Limpiar métricas de modelo antiguas
            model_metrics_query = select(ModelMetric).where(
                and_(
                    ModelMetric.account_id == account_id,
                    ModelMetric.timestamp < cutoff_date
                )
            )
            result = await db.execute(model_metrics_query)
            old_model_metrics = result.scalars().all()
            
            # Limpiar métricas de entrenamiento antiguas
            training_metrics_query = select(TrainingMetrics).where(
                and_(
                    TrainingMetrics.account_id == account_id,
                    TrainingMetrics.timestamp < cutoff_date
                )
            )
            result = await db.execute(training_metrics_query)
            old_training_metrics = result.scalars().all()
            
            # Eliminar registros antiguos
            for metric in old_model_metrics:
                await db.delete(metric)
                
            for metric in old_training_metrics:
                await db.delete(metric)
                
            await db.commit()
            
            cleanup_stats = {
                "model_metrics_deleted": len(old_model_metrics),
                "training_metrics_deleted": len(old_training_metrics),
                "cutoff_date": cutoff_date.isoformat()
            }
            
            log_info(f"Metrics cleanup completed for account {account_id}: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            await db.rollback()
            log_error(f"Error during metrics cleanup: {str(e)}")
            return {"error": str(e)} 
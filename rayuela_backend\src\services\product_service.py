"""
Servicio para operaciones relacionadas con productos.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.models import Product
from src.db.repositories.base import BaseRepository
from src.core.exceptions import NotFoundError, ValidationError
from src.services.cache_service import get_cache_service
from src.utils.base_logger import log_info, log_error


class ProductService:
    """
    Servicio para operaciones relacionadas con productos.

    Este servicio proporciona métodos para crear, actualizar, eliminar y consultar
    productos, gestionando la lógica de negocio relacionada con los productos.
    Utiliza el CacheService centralizado para invalidar cachés después de operaciones relevantes.
    """

    def __init__(self, db: AsyncSession):
        """
        Inicializa el servicio de productos.

        Args:
            db: Sesión de base de datos
        """
        self.db = db
        self.repository = BaseRepository(db, model=Product)

    async def get(self, account_id: int, product_id: int) -> Product:
        """
        Obtiene un producto por su ID.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto

        Returns:
            Producto encontrado

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        product = await self.repository.get_by_id(product_id)
        if not product:
            raise NotFoundError(f"Product with ID {product_id} not found")
        return product

    async def list(
        self,
        account_id: int,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        category: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        is_active: Optional[bool] = True,
    ) -> List[Product]:
        """
        Lista productos con filtros opcionales.

        Args:
            account_id: ID de la cuenta
            skip: Número de productos a omitir
            limit: Número máximo de productos a devolver
            search: Término de búsqueda en nombre y descripción
            category: Filtro por categoría
            min_price: Precio mínimo
            max_price: Precio máximo
            is_active: Filtro por estado activo

        Returns:
            Lista de productos
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Aplicar filtros
        filters = {}
        if search:
            # Implementar búsqueda por texto (simplificada)
            filters["name__icontains"] = search
        if category:
            filters["category"] = category
        if min_price is not None:
            filters["price__gte"] = min_price
        if max_price is not None:
            filters["price__lte"] = max_price
        if is_active is not None:
            filters["is_active"] = is_active

        return await self.repository.get_multi(
            skip=skip, limit=limit, **filters
        )

    async def count(
        self,
        account_id: int,
        search: Optional[str] = None,
        category: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        is_active: Optional[bool] = True,
    ) -> int:
        """
        Cuenta el total de productos que coinciden con los filtros.

        Args:
            account_id: ID de la cuenta
            search: Término de búsqueda en nombre y descripción
            category: Filtro por categoría
            min_price: Precio mínimo
            max_price: Precio máximo
            is_active: Filtro por estado activo

        Returns:
            Número total de productos
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Aplicar filtros
        filters = {}
        if search:
            filters["name__icontains"] = search
        if category:
            filters["category"] = category
        if min_price is not None:
            filters["price__gte"] = min_price
        if max_price is not None:
            filters["price__lte"] = max_price
        if is_active is not None:
            filters["is_active"] = is_active

        return await self.repository.count(**filters)

    async def create(self, account_id: int, data: Dict[str, Any]) -> Product:
        """
        Crea un nuevo producto.

        Args:
            account_id: ID de la cuenta
            data: Datos del producto

        Returns:
            Producto creado

        Raises:
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if not data.get("name"):
            raise ValidationError("Product name is required")

        if "price" in data and (not isinstance(data["price"], (int, float)) or data["price"] < 0):
            raise ValidationError("Product price must be a non-negative number")

        # Asignar account_id
        data["account_id"] = account_id

        # Crear producto dentro de una transacción
        async with self.db.begin():
            # Convertir a formato esperado por el repositorio
            product = await self.repository.create(obj_in=data)

        log_info(f"Producto creado: product_id={product.product_id}, account_id={account_id}")
        return product

    async def update(self, account_id: int, product_id: int, data: Dict[str, Any]) -> Product:
        """
        Actualiza un producto existente.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto
            data: Datos a actualizar

        Returns:
            Producto actualizado

        Raises:
            NotFoundError: Si el producto no existe
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if "price" in data and (not isinstance(data["price"], (int, float)) or data["price"] < 0):
            raise ValidationError("Product price must be a non-negative number")

        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        await self.get(account_id, product_id)  # Esto lanzará NotFoundError si no existe

        # Actualizar producto dentro de una transacción
        async with self.db.begin():
            # Convertir a formato esperado por el repositorio
            updated_product = await self.repository.update(id=product_id, obj_in=data)
            if not updated_product:
                raise NotFoundError(f"Product with ID {product_id} not found")

        # Invalidar cachés relevantes usando el servicio centralizado
        try:
            cache_service = await get_cache_service()
            await cache_service.invalidate_after_product_update(account_id, product_id)
        except Exception as cache_error:
            # No fallar la operación principal por errores de caché
            log_error(f"Error invalidando caché después de actualizar producto: {str(cache_error)}")

        log_info(f"Producto actualizado: product_id={product_id}, account_id={account_id}")
        return updated_product

    async def get_by_external_id(self, account_id: int, external_id: str) -> Product:
        """
        Obtiene un producto por su external_id.

        Args:
            account_id: ID de la cuenta
            external_id: ID externo del producto

        Returns:
            Producto encontrado

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        product = await self.repository.get_by_external_id(external_id)
        if not product:
            raise NotFoundError(f"Product with external_id '{external_id}' not found")
        return product

    async def update_by_external_id(self, account_id: int, external_id: str, data: Dict[str, Any]) -> Product:
        """
        Actualiza un producto por su external_id.

        Args:
            account_id: ID de la cuenta
            external_id: ID externo del producto
            data: Datos a actualizar

        Returns:
            Producto actualizado

        Raises:
            NotFoundError: Si el producto no existe
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if "price" in data and (not isinstance(data["price"], (int, float)) or data["price"] < 0):
            raise ValidationError("Product price must be a non-negative number")

        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        await self.get_by_external_id(account_id, external_id)  # Esto lanzará NotFoundError si no existe

        # Actualizar producto dentro de una transacción
        async with self.db.begin():
            updated_product = await self.repository.update_by_external_id(external_id, data)
            if not updated_product:
                raise NotFoundError(f"Product with external_id '{external_id}' not found")

        # Invalidar cachés relevantes usando el servicio centralizado
        try:
            cache_service = await get_cache_service()
            await cache_service.invalidate_after_product_update(account_id, updated_product.product_id)
        except Exception as cache_error:
            # No fallar la operación principal por errores de caché
            log_error(f"Error invalidando caché después de actualizar producto: {str(cache_error)}")

        log_info(f"Producto actualizado: external_id={external_id}, account_id={account_id}")
        return updated_product

    async def delete_by_external_id(self, account_id: int, external_id: str) -> bool:
        """
        Elimina un producto por su external_id.

        Args:
            account_id: ID de la cuenta
            external_id: ID externo del producto

        Returns:
            True si se eliminó exitosamente, False si no se encontró

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Verificar que el producto existe antes de eliminarlo
        await self.get_by_external_id(account_id, external_id)

        # Eliminar producto dentro de una transacción
        async with self.db.begin():
            deleted = await self.repository.delete_by_external_id(external_id)

        if deleted:
            # Invalidar cachés relevantes usando el servicio centralizado
            try:
                cache_service = await get_cache_service()
                # Note: No tenemos product_id aquí, pero podemos invalidar cachés generales
                await cache_service.invalidate_products_cache(account_id)
            except Exception as cache_error:
                # No fallar la operación principal por errores de caché
                log_error(f"Error invalidando caché después de eliminar producto: {str(cache_error)}")

            log_info(f"Producto eliminado: external_id={external_id}, account_id={account_id}")

        return deleted

    async def delete(self, account_id: int, product_id: int) -> bool:
        """
        Elimina un producto (soft delete).

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto

        Returns:
            True si se eliminó correctamente

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        product = await self.get(account_id, product_id)

        # Realizar soft delete dentro de una transacción
        async with self.db.begin():
            success = await self.repository.remove(product_id)
            if not success:
                raise NotFoundError(f"Product with ID {product_id} not found")

        # Invalidar cachés relevantes usando el servicio centralizado
        try:
            cache_service = await get_cache_service()
            await cache_service.invalidate_after_product_update(account_id, product_id)
        except Exception as cache_error:
            # No fallar la operación principal por errores de caché
            log_error(f"Error invalidando caché después de eliminar producto: {str(cache_error)}")

        log_info(f"Producto eliminado: product_id={product_id}, account_id={account_id}")
        return True

    async def update_inventory(self, account_id: int, product_id: int, quantity: int) -> Product:
        """
        Actualiza el inventario de un producto.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto
            quantity: Nueva cantidad en inventario

        Returns:
            Producto actualizado

        Raises:
            NotFoundError: Si el producto no existe
            ValidationError: Si la cantidad es inválida
        """
        if quantity < 0:
            raise ValidationError("Inventory quantity cannot be negative")

        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        await self.get(account_id, product_id)

        # Actualizar inventario dentro de una transacción
        async with self.db.begin():
            updated_product = await self.repository.update(
                id=product_id, 
                obj_in={"inventory_count": quantity}
            )
            if not updated_product:
                raise NotFoundError(f"Product with ID {product_id} not found")

        # Invalidar cachés relevantes usando el servicio centralizado
        try:
            cache_service = await get_cache_service()
            await cache_service.invalidate_after_product_update(account_id, product_id)
        except Exception as cache_error:
            # No fallar la operación principal por errores de caché
            log_error(f"Error invalidando caché después de actualizar inventario: {str(cache_error)}")

        log_info(f"Inventario actualizado: product_id={product_id}, quantity={quantity}, account_id={account_id}")
        return updated_product
"""
Servicio para gestionar funcionalidades específicas del Developer Sandbox.
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from src.db.models import (
    Product, EndUser, Interaction, ModelMetadata, 
    TrainingJob, BatchIngestionJob, Order, 
    AccountUsageMetrics, AuditLog
)
from typing import Dict, Any

class SandboxService:
    """
    Servicio para gestionar operaciones del Developer Sandbox.
    
    Permite a los usuarios del plan FREE limpiar sus datos experimentales
    para empezar con experimentos frescos.
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def reset_account_data(self, account_id: int) -> Dict[str, Any]:
        """
        Reset all experimental data for a sandbox account.
        
        This includes:
        - Products
        - End Users  
        - Interactions
        - Models and training jobs
        - Batch ingestion jobs
        - Orders
        - Audit logs (optionally)
        
        Does NOT delete:
        - Account information
        - System users
        - API Keys
        - Subscription information
        """
        reset_summary = {
            "products_deleted": 0,
            "end_users_deleted": 0,
            "interactions_deleted": 0,
            "models_deleted": 0,
            "training_jobs_deleted": 0,
            "batch_jobs_deleted": 0,
            "orders_deleted": 0,
            "audit_logs_deleted": 0,
        }
        
        try:
            # Count before deletion for summary
            products_count = await self._count_records(Product, account_id)
            end_users_count = await self._count_records(EndUser, account_id)
            interactions_count = await self._count_records(Interaction, account_id)
            models_count = await self._count_records(ModelMetadata, account_id)
            training_jobs_count = await self._count_records(TrainingJob, account_id)
            batch_jobs_count = await self._count_records(BatchIngestionJob, account_id)
            orders_count = await self._count_records(Order, account_id)
            audit_logs_count = await self._count_records(AuditLog, account_id)
            
            # Delete in proper order to respect foreign key constraints
            
            # 1. Delete interactions first (references products and end_users)
            await self.db.execute(
                delete(Interaction).where(Interaction.account_id == account_id)
            )
            
            # 2. Delete orders (references products and end_users)
            await self.db.execute(
                delete(Order).where(Order.account_id == account_id)
            )
            
            # 3. Delete products
            await self.db.execute(
                delete(Product).where(Product.account_id == account_id)
            )
            
            # 4. Delete end users
            await self.db.execute(
                delete(EndUser).where(EndUser.account_id == account_id)
            )
            
            # 5. Delete training jobs
            await self.db.execute(
                delete(TrainingJob).where(TrainingJob.account_id == account_id)
            )
            
            # 6. Delete batch ingestion jobs
            await self.db.execute(
                delete(BatchIngestionJob).where(BatchIngestionJob.account_id == account_id)
            )
            
            # 7. Delete model metadata
            await self.db.execute(
                delete(ModelMetadata).where(ModelMetadata.account_id == account_id)
            )
            
            # 8. Delete audit logs (optional - some may want to keep for debugging)
            await self.db.execute(
                delete(AuditLog).where(AuditLog.account_id == account_id)
            )
            
            # 9. Reset usage metrics to zero
            await self._reset_usage_metrics(account_id)
            
            # Commit all changes
            await self.db.commit()
            
            # Update summary with actual counts
            reset_summary.update({
                "products_deleted": products_count,
                "end_users_deleted": end_users_count,
                "interactions_deleted": interactions_count,
                "models_deleted": models_count,
                "training_jobs_deleted": training_jobs_count,
                "batch_jobs_deleted": batch_jobs_count,
                "orders_deleted": orders_count,
                "audit_logs_deleted": audit_logs_count,
            })
            
            return reset_summary
            
        except Exception as e:
            await self.db.rollback()
            raise e
    
    async def get_sandbox_status(self, account_id: int) -> Dict[str, Any]:
        """
        Get current sandbox status and data counts.
        """
        return {
            "products_count": await self._count_records(Product, account_id),
            "end_users_count": await self._count_records(EndUser, account_id),
            "interactions_count": await self._count_records(Interaction, account_id),
            "models_count": await self._count_records(ModelMetadata, account_id),
            "training_jobs_count": await self._count_records(TrainingJob, account_id),
            "batch_jobs_count": await self._count_records(BatchIngestionJob, account_id),
            "orders_count": await self._count_records(Order, account_id),
        }
    
    async def _count_records(self, model_class, account_id: int) -> int:
        """Helper method to count records for a given model and account."""
        result = await self.db.execute(
            select(model_class).where(model_class.account_id == account_id)
        )
        return len(result.scalars().all())
    
    async def _reset_usage_metrics(self, account_id: int):
        """Reset usage metrics to zero for the account."""
        # Get existing metrics
        result = await self.db.execute(
            select(AccountUsageMetrics).where(AccountUsageMetrics.account_id == account_id)
        )
        metrics = result.scalar_one_or_none()
        
        if metrics:
            # Reset metrics to zero
            metrics.api_calls_count = 0
            metrics.storage_used = 0
            metrics.billing_period_api_calls = 0
            metrics.billing_period_storage = 0
            
            self.db.add(metrics)
        else:
            # Create new metrics record if none exists
            new_metrics = AccountUsageMetrics(
                account_id=account_id,
                api_calls_count=0,
                storage_used=0,
                billing_period_api_calls=0,
                billing_period_storage=0
            )
            self.db.add(new_metrics) 
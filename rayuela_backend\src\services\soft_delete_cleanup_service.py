"""
Servicio de eliminación física programada para registros con soft delete.

Este servicio maneja la eliminación física de registros que han sido marcados como
eliminados (soft delete) y han excedido el período de retención final. Opcionalmente,
puede archivar los datos antes de la eliminación física.
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple, Type
from sqlalchemy import text, and_, select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from src.core.config import settings
from src.db.session import get_async_session_factory
from src.db.models import Account, SystemUser, EndUser, Product
from src.services.data_archival_service import DataArchivalService
from src.utils.base_logger import log_info, log_error, log_warning


class SoftDeleteCleanupService:
    """Servicio para eliminación física programada de registros con soft delete."""
    
    def __init__(self):
        """Inicializa el servicio de limpieza de soft deletes."""
        self.retention_days = settings.SOFT_DELETE_RETENTION_DAYS
        self.archive_before_deletion = settings.SOFT_DELETE_ARCHIVE_BEFORE_DELETION
        self.batch_size = settings.SOFT_DELETE_BATCH_SIZE
        self.archival_service = DataArchivalService() if self.archive_before_deletion else None
        
        # Mapeo de modelos que soportan soft delete
        self.soft_delete_models = {
            "accounts": Account,
            "system_users": SystemUser,
            "end_users": EndUser,
            "products": Product,
        }
    
    async def cleanup_all_soft_deleted_records(
        self,
        account_id: Optional[int] = None,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        Limpia todos los registros con soft delete que han excedido el período de retención.
        
        Args:
            account_id: ID de cuenta específica (None para todas)
            dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios
            
        Returns:
            Diccionario con información sobre la operación de limpieza
        """
        start_time = datetime.now(timezone.utc)
        cutoff_date = start_time - timedelta(days=self.retention_days)
        
        account_msg = f" for account {account_id}" if account_id else ""
        action = "DRY RUN - Would cleanup" if dry_run else "Cleaning up"
        
        log_info(f"{action} soft deleted records older than {self.retention_days} days{account_msg} (cutoff: {cutoff_date})")
        
        results = {
            "cutoff_date": cutoff_date.isoformat(),
            "retention_days": self.retention_days,
            "account_id": account_id,
            "dry_run": dry_run,
            "tables": {},
            "total_archived": 0,
            "total_deleted": 0,
            "success": True,
            "errors": []
        }
        
        try:
            # Procesar cada modelo con soft delete
            for table_name, model_class in self.soft_delete_models.items():
                try:
                    table_result = await self._cleanup_table_soft_deletes(
                        table_name=table_name,
                        model_class=model_class,
                        cutoff_date=cutoff_date,
                        account_id=account_id,
                        dry_run=dry_run
                    )
                    
                    results["tables"][table_name] = table_result
                    results["total_archived"] += table_result.get("archived_count", 0)
                    results["total_deleted"] += table_result.get("deleted_count", 0)
                    
                    if not table_result.get("success", False):
                        results["success"] = False
                        if table_result.get("error"):
                            results["errors"].append(f"{table_name}: {table_result['error']}")
                            
                except Exception as e:
                    error_msg = f"Error processing {table_name}: {str(e)}"
                    log_error(error_msg)
                    results["errors"].append(error_msg)
                    results["success"] = False
                    results["tables"][table_name] = {
                        "archived_count": 0,
                        "deleted_count": 0,
                        "error": error_msg,
                        "success": False
                    }
            
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            results["duration_seconds"] = duration
            
            if results["success"]:
                log_info(f"Completed soft delete cleanup{account_msg}: "
                        f"archived {results['total_archived']}, "
                        f"deleted {results['total_deleted']} records in {duration:.2f}s")
            else:
                log_error(f"Soft delete cleanup completed with errors{account_msg}: {results['errors']}")
            
            return results
            
        except Exception as e:
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            error_msg = f"Error in soft delete cleanup: {str(e)}"
            log_error(f"{error_msg} (after {duration:.2f}s){account_msg}")
            
            return {
                "cutoff_date": cutoff_date.isoformat(),
                "retention_days": self.retention_days,
                "account_id": account_id,
                "dry_run": dry_run,
                "error": error_msg,
                "success": False,
                "duration_seconds": duration
            }
    
    async def _cleanup_table_soft_deletes(
        self,
        table_name: str,
        model_class: Type[DeclarativeBase],
        cutoff_date: datetime,
        account_id: Optional[int] = None,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        Limpia registros con soft delete de una tabla específica.
        
        Args:
            table_name: Nombre de la tabla
            model_class: Clase del modelo SQLAlchemy
            cutoff_date: Fecha límite para eliminación
            account_id: ID de cuenta específica (None para todas)
            dry_run: Si es True, solo reporta sin hacer cambios
            
        Returns:
            Diccionario con información sobre la operación
        """
        start_time = datetime.now(timezone.utc)
        account_msg = f" for account {account_id}" if account_id else ""
        
        try:
            log_info(f"Processing soft deleted records in {table_name}{account_msg}")
            
            async_session_factory = get_async_session_factory()
            async with async_session_factory() as db:
                # Construir consulta para registros soft deleted antiguos
                query_conditions = [
                    model_class.is_active == False,
                    model_class.deleted_at.isnot(None),
                    model_class.deleted_at < cutoff_date
                ]
                
                # Agregar filtro de cuenta si aplica y el modelo lo soporta
                if account_id is not None and hasattr(model_class, 'account_id'):
                    query_conditions.append(model_class.account_id == account_id)
                
                # Contar registros a procesar
                count_query = select(func.count()).select_from(model_class).where(and_(*query_conditions))
                result = await db.execute(count_query)
                total_records = result.scalar()
                
                if total_records == 0:
                    log_info(f"No soft deleted records found in {table_name}{account_msg}")
                    return {
                        "table": table_name,
                        "archived_count": 0,
                        "deleted_count": 0,
                        "total_found": 0,
                        "success": True,
                        "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
                    }
                
                log_info(f"Found {total_records} soft deleted records in {table_name}{account_msg} to process")
                
                if dry_run:
                    return {
                        "table": table_name,
                        "archived_count": 0,
                        "deleted_count": 0,
                        "total_found": total_records,
                        "dry_run": True,
                        "success": True,
                        "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
                    }
                
                archived_count = 0
                deleted_count = 0
                
                # Paso 1: Archivar si está habilitado
                if self.archive_before_deletion and self.archival_service:
                    archive_result = await self._archive_soft_deleted_records(
                        table_name=table_name,
                        model_class=model_class,
                        cutoff_date=cutoff_date,
                        account_id=account_id
                    )
                    
                    if not archive_result.get("success", False):
                        log_error(f"Failed to archive soft deleted records from {table_name}: {archive_result.get('error')}")
                        return {
                            "table": table_name,
                            "archived_count": 0,
                            "deleted_count": 0,
                            "total_found": total_records,
                            "error": f"Archival failed: {archive_result.get('error')}",
                            "success": False,
                            "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
                        }
                    
                    archived_count = archive_result.get("archived_count", 0)
                    log_info(f"Successfully archived {archived_count} soft deleted records from {table_name}{account_msg}")
                
                # Paso 2: Eliminación física en lotes
                deleted_count = await self._physically_delete_records(
                    db=db,
                    model_class=model_class,
                    query_conditions=query_conditions,
                    table_name=table_name,
                    account_msg=account_msg
                )
                
                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                
                if deleted_count == total_records:
                    log_info(f"Successfully processed {table_name}{account_msg}: "
                            f"archived {archived_count}, deleted {deleted_count} records in {duration:.2f}s")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "deleted_count": deleted_count,
                        "total_found": total_records,
                        "success": True,
                        "duration_seconds": duration
                    }
                else:
                    log_warning(f"Partial cleanup of {table_name}{account_msg}: "
                               f"deleted {deleted_count}/{total_records} records")
                    return {
                        "table": table_name,
                        "archived_count": archived_count,
                        "deleted_count": deleted_count,
                        "total_found": total_records,
                        "error": "Partial deletion completed",
                        "success": False,
                        "duration_seconds": duration
                    }
                    
        except Exception as e:
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            error_msg = f"Error cleaning up {table_name}: {str(e)}"
            log_error(f"{error_msg} (after {duration:.2f}s){account_msg}")
            return {
                "table": table_name,
                "archived_count": 0,
                "deleted_count": 0,
                "error": error_msg,
                "success": False,
                "duration_seconds": duration
            }
    
    async def _archive_soft_deleted_records(
        self,
        table_name: str,
        model_class: Type[DeclarativeBase],
        cutoff_date: datetime,
        account_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Archiva registros con soft delete antes de la eliminación física.
        
        Args:
            table_name: Nombre de la tabla
            model_class: Clase del modelo SQLAlchemy
            cutoff_date: Fecha límite para archivado
            account_id: ID de cuenta específica
            
        Returns:
            Diccionario con información sobre el archivado
        """
        try:
            # Para tablas que ya tienen soporte de archivado específico
            if table_name == "audit_logs":
                # Usar el método específico para audit logs si existe
                return await self.archival_service.archive_audit_logs(
                    cutoff_date=cutoff_date,
                    account_id=account_id,
                    batch_size=self.batch_size
                )
            elif table_name == "interactions":
                # Usar el método específico para interactions si existe
                return await self.archival_service.archive_interactions(
                    cutoff_date=cutoff_date,
                    account_id=account_id,
                    batch_size=self.batch_size
                )
            else:
                # Para otras tablas, usar archivado genérico
                return await self._generic_archive_soft_deleted(
                    table_name=table_name,
                    model_class=model_class,
                    cutoff_date=cutoff_date,
                    account_id=account_id
                )
                
        except Exception as e:
            error_msg = f"Error archiving soft deleted records from {table_name}: {str(e)}"
            log_error(error_msg)
            return {
                "archived_count": 0,
                "error": error_msg,
                "success": False
            }

    async def _generic_archive_soft_deleted(
        self,
        table_name: str,
        model_class: Type[DeclarativeBase],
        cutoff_date: datetime,
        account_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Archivado genérico para tablas con soft delete que no tienen métodos específicos.

        Args:
            table_name: Nombre de la tabla
            model_class: Clase del modelo SQLAlchemy
            cutoff_date: Fecha límite para archivado
            account_id: ID de cuenta específica

        Returns:
            Diccionario con información sobre el archivado
        """
        try:
            import pandas as pd

            async_session_factory = get_async_session_factory()
            async with async_session_factory() as db:
                # Construir consulta para registros soft deleted
                query_conditions = [
                    model_class.is_active == False,
                    model_class.deleted_at.isnot(None),
                    model_class.deleted_at < cutoff_date
                ]

                if account_id is not None and hasattr(model_class, 'account_id'):
                    query_conditions.append(model_class.account_id == account_id)

                # Obtener registros a archivar
                query = select(model_class).where(and_(*query_conditions))
                result = await db.execute(query)
                records = result.scalars().all()

                if not records:
                    return {
                        "archived_count": 0,
                        "success": True
                    }

                # Convertir a DataFrame
                data = []
                for record in records:
                    record_dict = {}
                    for column in model_class.__table__.columns:
                        value = getattr(record, column.name)
                        # Convertir datetime a string para serialización
                        if isinstance(value, datetime):
                            value = value.isoformat()
                        record_dict[column.name] = value
                    data.append(record_dict)

                df = pd.DataFrame(data)

                # Generar nombre de archivo
                timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
                account_suffix = f"_account_{account_id}" if account_id else "_all_accounts"
                file_name = f"{table_name}_soft_deleted_{timestamp}{account_suffix}.{settings.ARCHIVAL_FORMAT}"
                archive_path = f"{settings.GCS_ARCHIVAL_PATH}/soft_deleted/{table_name}/{cutoff_date.strftime('%Y/%m/%d')}/{file_name}"

                # Exportar usando el servicio de archivado
                success = await self.archival_service._export_dataframe_to_gcs(df, archive_path)

                if success:
                    log_info(f"Successfully archived {len(records)} soft deleted records from {table_name} to {archive_path}")
                    return {
                        "archived_count": len(records),
                        "archive_path": archive_path,
                        "success": True
                    }
                else:
                    return {
                        "archived_count": 0,
                        "error": "Failed to export to GCS",
                        "success": False
                    }

        except Exception as e:
            error_msg = f"Error in generic archival for {table_name}: {str(e)}"
            log_error(error_msg)
            return {
                "archived_count": 0,
                "error": error_msg,
                "success": False
            }

    async def _physically_delete_records(
        self,
        db: AsyncSession,
        model_class: Type[DeclarativeBase],
        query_conditions: List,
        table_name: str,
        account_msg: str = ""
    ) -> int:
        """
        Realiza la eliminación física de registros en lotes.

        Args:
            db: Sesión de base de datos
            model_class: Clase del modelo SQLAlchemy
            query_conditions: Condiciones de la consulta
            table_name: Nombre de la tabla
            account_msg: Mensaje de cuenta para logging

        Returns:
            Número de registros eliminados
        """
        try:
            deleted_count = 0

            while True:
                # Obtener un lote de registros para eliminar
                batch_query = select(model_class).where(and_(*query_conditions)).limit(self.batch_size)
                result = await db.execute(batch_query)
                batch_records = result.scalars().all()

                if not batch_records:
                    break

                # Eliminar el lote
                for record in batch_records:
                    await db.delete(record)

                # Confirmar el lote
                await db.commit()

                batch_deleted = len(batch_records)
                deleted_count += batch_deleted

                log_info(f"Deleted batch of {batch_deleted} records from {table_name}{account_msg} "
                        f"(total: {deleted_count})")

                # Pausa pequeña entre lotes para no sobrecargar la base de datos
                await asyncio.sleep(0.1)

            return deleted_count

        except Exception as e:
            log_error(f"Error during physical deletion from {table_name}{account_msg}: {str(e)}")
            await db.rollback()
            raise

    async def get_soft_delete_statistics(
        self,
        account_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Obtiene estadísticas sobre registros con soft delete.

        Args:
            account_id: ID de cuenta específica (None para todas)

        Returns:
            Diccionario con estadísticas por tabla
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
            stats = {
                "cutoff_date": cutoff_date.isoformat(),
                "retention_days": self.retention_days,
                "account_id": account_id,
                "tables": {}
            }

            async_session_factory = get_async_session_factory()
            async with async_session_factory() as db:
                for table_name, model_class in self.soft_delete_models.items():
                    try:
                        # Contar registros soft deleted totales
                        total_conditions = [
                            model_class.is_active == False,
                            model_class.deleted_at.isnot(None)
                        ]

                        # Contar registros soft deleted elegibles para eliminación
                        eligible_conditions = [
                            model_class.is_active == False,
                            model_class.deleted_at.isnot(None),
                            model_class.deleted_at < cutoff_date
                        ]

                        # Agregar filtro de cuenta si aplica
                        if account_id is not None and hasattr(model_class, 'account_id'):
                            total_conditions.append(model_class.account_id == account_id)
                            eligible_conditions.append(model_class.account_id == account_id)

                        # Ejecutar consultas
                        total_query = select(func.count()).select_from(model_class).where(and_(*total_conditions))
                        eligible_query = select(func.count()).select_from(model_class).where(and_(*eligible_conditions))

                        total_result = await db.execute(total_query)
                        eligible_result = await db.execute(eligible_query)

                        total_soft_deleted = total_result.scalar()
                        eligible_for_deletion = eligible_result.scalar()

                        stats["tables"][table_name] = {
                            "total_soft_deleted": total_soft_deleted,
                            "eligible_for_deletion": eligible_for_deletion,
                            "retention_remaining": total_soft_deleted - eligible_for_deletion
                        }

                    except Exception as e:
                        log_error(f"Error getting statistics for {table_name}: {str(e)}")
                        stats["tables"][table_name] = {
                            "error": str(e)
                        }

            return stats

        except Exception as e:
            error_msg = f"Error getting soft delete statistics: {str(e)}"
            log_error(error_msg)
            return {
                "error": error_msg,
                "success": False
            }

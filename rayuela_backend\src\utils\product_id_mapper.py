"""
Utilidad para mapear entre product_id interno y external_id de productos.

Este módulo proporciona funciones para manejar el mapeo entre IDs internos
de productos (product_id) y los IDs externos proporcionados por clientes (external_id).
"""

from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from src.db.models.product import Product


async def resolve_product_external_id(
    db: AsyncSession, 
    account_id: int, 
    external_id: str
) -> Optional[int]:
    """
    Resuelve un external_id de producto a su product_id interno.

    Args:
        db: Sesión de base de datos
        account_id: ID de la cuenta
        external_id: ID externo del producto

    Returns:
        product_id interno o None si no se encuentra
    """
    try:
        query = select(Product.product_id).where(
            Product.account_id == account_id,
            Product.external_id == external_id,
            Product.is_active == True
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    except Exception:
        return None


async def resolve_product_internal_id(
    db: AsyncSession, 
    account_id: int, 
    product_id: int
) -> Optional[str]:
    """
    Resuelve un product_id interno a su external_id.

    Args:
        db: Sesión de base de datos
        account_id: ID de la cuenta
        product_id: ID interno del producto

    Returns:
        external_id o None si no se encuentra
    """
    try:
        query = select(Product.external_id).where(
            Product.account_id == account_id,
            Product.product_id == product_id,
            Product.is_active == True
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    except Exception:
        return None


def map_product_id_to_external_id(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Mapea product_id a external_product_id en un diccionario de datos.

    Args:
        data: Diccionario que puede contener product_id

    Returns:
        Diccionario con external_product_id en lugar de product_id
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    mapped_data = data.copy()

    # Si existe product_id, mapearlo a external_product_id
    if "product_id" in mapped_data:
        mapped_data["external_product_id"] = mapped_data.pop("product_id")

    return mapped_data


def map_external_product_id_to_product_id(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Mapea external_product_id a product_id en un diccionario de datos.

    Args:
        data: Diccionario que puede contener external_product_id

    Returns:
        Diccionario con product_id en lugar de external_product_id
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    mapped_data = data.copy()

    # Si existe external_product_id, mapearlo a product_id
    if "external_product_id" in mapped_data:
        mapped_data["product_id"] = mapped_data.pop("external_product_id")

    return mapped_data


def extract_product_id_from_request_data(data: Dict[str, Any]) -> Optional[str]:
    """
    Extrae el ID de producto de los datos de una solicitud,
    manejando diferentes formatos (product_id, external_product_id, productId, etc.).

    Args:
        data: Datos de la solicitud

    Returns:
        ID de producto como string o None si no se encuentra
    """
    if not isinstance(data, dict):
        return None

    # Buscar en diferentes formatos posibles
    product_id = (
        data.get("external_product_id")
        or data.get("product_id")
        or data.get("productId")
        or data.get("externalProductId")
    )

    if isinstance(product_id, (str, int)):
        return str(product_id)

    return None


def normalize_product_id_field(
    data: Dict[str, Any], 
    target_field: str = "external_product_id"
) -> Dict[str, Any]:
    """
    Normaliza el campo de ID de producto a un nombre específico.

    Args:
        data: Diccionario de datos
        target_field: Nombre del campo objetivo ("product_id" o "external_product_id")

    Returns:
        Diccionario con el campo normalizado
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    normalized_data = data.copy()

    # Extraer el ID de producto
    product_id = extract_product_id_from_request_data(normalized_data)

    if product_id is not None:
        # Remover todas las variantes del campo
        for field in ["product_id", "external_product_id", "productId", "externalProductId"]:
            normalized_data.pop(field, None)

        # Establecer el campo objetivo
        normalized_data[target_field] = product_id

    return normalized_data


class ProductIdMapper:
    """
    Clase para manejar el mapeo de IDs de producto entre formato interno y externo.
    """

    def __init__(self, db: AsyncSession, account_id: int):
        self.db = db
        self.account_id = account_id

    async def to_external_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convierte datos del formato interno (product_id) al formato externo (external_product_id).

        Args:
            data: Datos en formato interno

        Returns:
            Datos en formato externo
        """
        if "product_id" in data:
            external_id = await resolve_product_internal_id(
                self.db, self.account_id, data["product_id"]
            )
            if external_id:
                result = data.copy()
                result["external_product_id"] = external_id
                # Opcionalmente mantener product_id para compatibilidad
                return result
        return data

    async def to_internal_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convierte datos del formato externo (external_product_id) al formato interno (product_id).

        Args:
            data: Datos en formato externo

        Returns:
            Datos en formato interno
        """
        external_product_id = extract_product_id_from_request_data(data)
        if external_product_id:
            internal_id = await resolve_product_external_id(
                self.db, self.account_id, external_product_id
            )
            if internal_id:
                result = data.copy()
                result["product_id"] = internal_id
                # Remover campos externos para evitar confusión
                for field in ["external_product_id", "productId", "externalProductId"]:
                    result.pop(field, None)
                return result
        return data

    async def extract_product_id(self, data: Dict[str, Any]) -> Optional[int]:
        """
        Extrae el product_id interno de los datos, resolviendo external_id si es necesario.

        Args:
            data: Datos de entrada

        Returns:
            product_id interno o None
        """
        external_product_id = extract_product_id_from_request_data(data)
        if external_product_id:
            return await resolve_product_external_id(
                self.db, self.account_id, external_product_id
            )
        return None 
"""
Secure Celery application configuration for background tasks.
This configuration enforces tenant isolation and prevents RLS bypass.

⚠️ SECURITY CRITICAL: This implementation mitigates RLS bypass risks ⚠️
"""

from celery import Celery
from celery.schedules import crontab
from src.core.config import settings
from typing import List, Dict, Any
import logging

# Setup security logging
security_logger = logging.getLogger("security.celery")

# Create Celery app
celery_app = Celery(
    "ml_training_worker_secure",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
)

# Configure Celery with enhanced security settings
celery_app.conf.update(
    # Serialization settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",

    # Time settings
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,

    # Enhanced security settings
    task_time_limit=3600 * 2,  # 2 hours default time limit
    task_soft_time_limit=3600 * 1.5,  # 1.5 hours soft time limit
    task_reject_on_worker_lost=True,  # Reject tasks if worker is lost
    task_acks_late=True,  # Acknowledge tasks after completion

    # Worker settings for security
    worker_max_tasks_per_child=10,  # Restart worker after 10 tasks
    worker_prefetch_multiplier=1,  # Don't prefetch more than one task
    worker_disable_rate_limits=False,  # Enable rate limiting

    # Queue settings
    task_default_queue="default",
    task_queues={
        "training": {
            "exchange": "training",
            "routing_key": "training",
        },
        "batch_processing": {
            "exchange": "batch_processing",
            "routing_key": "batch_processing",
        },
        "maintenance_secure": {  # Secure maintenance queue
            "exchange": "maintenance_secure",
            "routing_key": "maintenance_secure",
        },
        "default": {
            "exchange": "default",
            "routing_key": "default",
        },
    },

    # Secure task routing
    task_routes={
        # Training tasks
        "train_model": {"queue": "training"},
        "train_model_for_job": {"queue": "training"},

        # Batch processing tasks
        "process_batch_data": {"queue": "batch_processing"},

        # SECURE maintenance tasks (tenant-specific)
        "cleanup_old_audit_logs_secure": {"queue": "maintenance_secure"},
        "cleanup_old_interactions_secure": {"queue": "maintenance_secure"},
        "cleanup_old_data_secure_validated": {"queue": "maintenance_secure"},
        "cleanup_soft_deleted_records_secure": {"queue": "maintenance_secure"},
        
        # Per-tenant orchestration tasks
        "cleanup_all_tenants_secure": {"queue": "maintenance_secure"},
        "schedule_per_tenant_maintenance": {"queue": "maintenance_secure"},

        # Infrastructure tasks
        "monitor_high_volume_tables": {"queue": "maintenance_secure"},
        "manage_partitions_task": {"queue": "maintenance_secure"},
        "reset_monthly_api_calls": {"queue": "default"},
# "update_storage_usage": {"queue": "default"},  # Función no implementada
        "measure_storage_usage": {"queue": "default"},

        # Storage meter tasks
        "get_storage_usage": {"queue": "default"},
    }
)

# ============================================================================
# SECURITY CRITICAL: SECURE TASK SCHEDULER
# ============================================================================

def get_active_tenant_list() -> List[int]:
    """
    Get list of active tenant IDs for secure per-tenant task scheduling.
    
    Returns:
        List of active tenant account IDs
    """
    try:
        # Import here to avoid circular imports
        from src.db.repositories.account import AccountRepository
        from src.db.session import get_db
        from asgiref.sync import async_to_sync
        
        async def _get_tenants():
            async with get_db() as db:
                repo = AccountRepository(db)
                accounts = await repo.get_active_accounts()
                return [account.account_id for account in accounts]
        
        return async_to_sync(_get_tenants)()
    except Exception as e:
        security_logger.error(f"[SECURITY ERROR] Failed to get tenant list: {str(e)}")
        return []

# ============================================================================
# SECURE PERIODIC TASK CONFIGURATION
# ============================================================================

# SECURITY: All tasks now operate per-tenant with proper isolation
celery_app.conf.beat_schedule = {
    # =======================================================================
    # SECURE PER-TENANT MAINTENANCE TASKS
    # =======================================================================
    
    "schedule-per-tenant-audit-logs-cleanup": {
        "task": "schedule_per_tenant_maintenance",
        "schedule": 86400.0,  # Once a day
        "kwargs": {
            "task_name": "cleanup_old_audit_logs_secure",
            "task_kwargs": {"days_to_keep": 90},
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    "schedule-per-tenant-interactions-cleanup": {
        "task": "schedule_per_tenant_maintenance", 
        "schedule": 86400.0 * 7,  # Once a week
        "kwargs": {
            "task_name": "cleanup_old_interactions_secure",
            "task_kwargs": {"days_to_keep": 180, "batch_size": 10000},
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    "schedule-per-tenant-soft-delete-cleanup": {
        "task": "schedule_per_tenant_maintenance",
        "schedule": 86400.0 * 30,  # Once a month
        "kwargs": {
            "task_name": "cleanup_soft_deleted_records_secure",
            "task_kwargs": {"retention_days": 90, "dry_run": False},
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    # CRITICAL: RLS-compliant data cleanup (account_id REQUIRED)
    "schedule-per-tenant-secure-data-cleanup": {
        "task": "schedule_per_tenant_maintenance",
        "schedule": 86400.0 * 14,  # Once every two weeks
        "kwargs": {
            "task_name": "cleanup_old_data_secure_validated",
            "task_kwargs": {"days_to_keep": 90},
            "enforce_tenant_isolation": True,
            "require_account_id": True  # CRITICAL: account_id is mandatory
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    # =======================================================================
    # ARCHIVE AND CLEANUP TASKS (SECURE)
    # =======================================================================
    
    "schedule-per-tenant-archive-audit-logs": {
        "task": "schedule_per_tenant_maintenance",
        "schedule": 86400.0,  # Daily
        "kwargs": {
            "task_name": "archive_and_cleanup_old_audit_logs_secure",
            "task_kwargs": {"days_to_keep": 30, "batch_size": 10000},
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    "schedule-per-tenant-archive-interactions": {
        "task": "schedule_per_tenant_maintenance",
        "schedule": 86400.0 * 7,  # Weekly
        "kwargs": {
            "task_name": "archive_and_cleanup_old_interactions_secure",
            "task_kwargs": {"days_to_keep": 60, "batch_size": 10000},
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance_secure"},
    },
    
    # =======================================================================
    # INFRASTRUCTURE TASKS (NO TENANT DATA ACCESS)
    # =======================================================================
    
    "monitor-high-volume-tables": {
        "task": "monitor_high_volume_tables_secure",
        "schedule": 3600.0,  # Once an hour
        "kwargs": {"tenant_data_access": False},  # No tenant data access
        "options": {"queue": "maintenance_secure"},
    },
    
    "manage-partitions": {
        "task": "manage_partitions_task_secure",
        "schedule": crontab(hour=1, minute=0),  # Daily at 1:00 AM
        "kwargs": {"tenant_data_access": False},  # No tenant data access
        "options": {"queue": "maintenance_secure"},
    },
    
    # =======================================================================
    # SUBSCRIPTION AND STORAGE TASKS (TENANT-AWARE BUT NO RLS BYPASS)
    # =======================================================================
    
    "reset-monthly-api-calls": {
        "task": "src.tasks.subscription_tasks.reset_monthly_api_calls",
        "schedule": 86400.0,  # Daily
        "options": {"queue": "default"},
    },
    
    "update-storage-usage": {
# "task": "src.tasks.subscription_tasks.update_storage_usage",  # Función no implementada 
        "schedule": 86400.0,  # Daily
        "options": {"queue": "default"},
    },
    
    "measure-storage-usage": {
        "task": "src.tasks.storage_meter_tasks.measure_storage_usage",
        "schedule": 86400.0,  # Daily
        "options": {"queue": "default"},
    },
}

# ============================================================================
# SECURITY VALIDATION MIDDLEWARE
# ============================================================================

@celery_app.task(bind=True)
def validate_task_security(self, task_name: str, kwargs: Dict[str, Any]) -> bool:
    """
    Validates that a task meets security requirements.
    
    Args:
        task_name: Name of the task to validate
        kwargs: Task arguments
        
    Returns:
        True if task is secure, False otherwise
    """
    task_id = self.request.id or "unknown"
    
    # List of tasks that require account_id
    TENANT_REQUIRED_TASKS = [
        "cleanup_old_audit_logs_secure",
        "cleanup_old_interactions_secure", 
        "cleanup_old_data_secure_validated",
        "cleanup_soft_deleted_records_secure",
        "archive_and_cleanup_old_audit_logs_secure",
        "archive_and_cleanup_old_interactions_secure"
    ]
    
    # Validate tenant-specific tasks
    if task_name in TENANT_REQUIRED_TASKS:
        account_id = kwargs.get("account_id")
        
        if account_id is None:
            security_logger.error(
                f"[SECURITY VIOLATION] Task {task_id} ({task_name}): "
                f"account_id is required but not provided"
            )
            return False
            
        if not isinstance(account_id, int) or account_id <= 0:
            security_logger.error(
                f"[SECURITY VIOLATION] Task {task_id} ({task_name}): "
                f"Invalid account_id={account_id}"
            )
            return False
    
    # Log security approval
    security_logger.info(
        f"[SECURITY AUDIT] Task {task_id} ({task_name}) security validation passed"
    )
    return True

# ============================================================================
# IMPORT SECURE TASKS
# ============================================================================

# Import secure task implementations
try:
    import src.workers.celery_tasks_secure
    import src.workers.celery_tasks_partition
    import src.tasks.subscription_tasks
    import src.tasks.storage_meter_tasks
except ImportError as e:
    security_logger.warning(f"Could not import some optional task modules: {e}")

# Security configuration completed
security_logger.info("Secure Celery configuration loaded successfully") 
#!/bin/bash
set -e

# Determine what service to start based on WORKER_TYPE environment variable
WORKER_TYPE=${WORKER_TYPE:-"api"}

echo "🚀 Starting Rayuela service: $WORKER_TYPE"

if [ "$WORKER_TYPE" = "beat" ]; then
    echo "🕐 Starting Celery Beat scheduler..."
    # Start health server in background for beat
    python /app/health_server.py &
    exec celery -A src.workers.celery_app beat --loglevel=info
    
elif [ "$WORKER_TYPE" = "maintenance" ]; then
    echo "⚙️ Starting Celery Worker for maintenance tasks..."
    # Start health server in background for worker
    python /app/health_server.py &
    exec celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=10 --queues=maintenance --hostname=maintenance@%h
    
elif [ "$WORKER_TYPE" = "worker" ]; then
    echo "👷 Starting default Celery Worker..."
    # Start health server in background for worker
    python /app/health_server.py &
    exec celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --optimization=fair
    
else
    # Default: Start FastAPI application (API service)
    echo "🌐 Starting FastAPI application..."
    
    # Test critical dependencies first
    echo "🧪 Testing critical dependencies..."
    python test_deps.py
    
    if [ $? -ne 0 ]; then
        echo "❌ Critical dependency check failed"
        exit 1
    fi
    
    # Start the FastAPI application using gunicorn with uvicorn workers
    echo "✅ Dependencies OK. Starting FastAPI app with Gunicorn..."
    exec gunicorn main:app -c gunicorn_conf.py 
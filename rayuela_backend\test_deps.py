#!/usr/bin/env python3
import sys
import importlib

required_modules = ['fastapi', 'uvicorn', 'sqlalchemy', 'asyncpg', 'pydantic', 'dotenv', 'google.cloud.storage', 'redis', 'celery']

print("🧪 Testing critical dependencies...")
failed = []

for module in required_modules:
    try:
        importlib.import_module(module)
        print(f"✅ {module} - OK")
    except ImportError as e:
        print(f"❌ {module} - FAILED: {e}")
        failed.append(module)

if failed:
    print(f"\n🚨 FAILED MODULES: {', '.join(failed)}")
    sys.exit(1)
else:
    print("\n✅ All critical dependencies available!")
    
try:
    from fastapi import FastAPI
    app = FastAPI()
    print("✅ FastAPI app creation - OK")
except Exception as e:
    print(f"❌ FastAPI app creation - FAILED: {e}")
    sys.exit(1)

print("🎉 Container dependency test PASSED!")

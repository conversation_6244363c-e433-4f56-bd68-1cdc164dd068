import pytest
from fastapi import HTTPException
from src.core.security import verify_password, get_password_hash
from src.core.deps import get_current_account, get_current_active_user
from src.db.models import SystemUser, Account

class TestAuthFlow:
    """Tests de integración para el flujo de autenticación."""
    
    @pytest.mark.asyncio
    async def test_login_flow(
        self,
        test_client,
        db_session,
        test_accounts,
        test_users,
        test_tokens
    ):
        """Test para el flujo completo de login."""
        # Obtener credenciales de prueba
        account = test_accounts[0]
        user = test_users[account.id]["regular"]
        tokens = test_tokens[account.id]["regular"]
        
        # Verificar que el token JWT es válido
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200
        assert response.json()["email"] == user.email
        
        # Verificar que el token API es válido
        response = test_client.get(
            "/api/v1/products",
            headers={"X-API-Key": tokens["api_key"]}
        )
        assert response.status_code == 200
    
    async def test_invalid_token(
        self,
        test_client,
        test_tokens
    ):
        """Test para tokens inválidos."""
        # Token JWT inválido
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
        
        # API Key inválida
        response = test_client.get(
            "/api/v1/products",
            headers={"X-API-Key": "invalid_key"}
        )
        assert response.status_code == 401
    
    async def test_token_expiration(
        self,
        test_client,
        db_session,
        test_accounts,
        test_users
    ):
        """Test para la expiración de tokens."""
        # Crear un token con expiración muy corta
        user = test_users[test_accounts[0].account_id]["regular"]
        expired_token = create_access_token(
            data={"sub": user.email, "account_id": test_accounts[0].account_id},
            expires_delta=timedelta(milliseconds=100)
        )
        
        # Esperar a que expire el token
        import time
        time.sleep(0.2)
        
        # Intentar usar el token expirado
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        assert response.status_code == 401
    
    async def test_account_resolution(
        self,
        db_session,
        test_accounts,
        test_users,
        test_tokens
    ):
        """Test para la resolución de cuenta desde el token."""
        # Obtener token de prueba
        account = test_accounts[0]
        user = test_users[account.account_id]["regular"]
        tokens = test_tokens[account.account_id]["regular"]
        
        # Verificar que get_current_account resuelve la cuenta correcta
        resolved_account = await get_current_account(tokens["access_token"])
        assert resolved_account.account_id == account.account_id
        
        # Get admin email for comparison instead of account.email
        admin_email = await account.get_admin_email_async(db_session)
        assert admin_email is not None  # Basic check that admin email exists
        
        # Verificar que get_current_active_user resuelve el usuario correcto
        resolved_user = await get_current_active_user(tokens["access_token"])
        assert resolved_user.id == user.id
        assert resolved_user.email == user.email
        assert resolved_user.account_id == account.account_id
    
    async def test_cross_account_access(
        self,
        test_client,
        test_accounts,
        test_tokens
    ):
        """Test para intentar acceder a recursos de otra cuenta."""
        # Obtener tokens de diferentes cuentas
        account_a_tokens = test_tokens[test_accounts[0].account_id]["regular"]
        account_b_tokens = test_tokens[test_accounts[1].account_id]["regular"]
        
        # Intentar acceder a recursos de cuenta B con token de cuenta A
        response = test_client.get(
            f"/api/v1/accounts/{test_accounts[1].account_id}",
            headers={"Authorization": f"Bearer {account_a_tokens['access_token']}"}
        )
        assert response.status_code == 403
    
    async def test_password_verification(
        self,
        db_session,
        test_users
    ):
        """Test para la verificación de contraseñas."""
        # Obtener usuario de prueba
        user = test_users[list(test_users.keys())[0]]["regular"]
        
        # Verificar contraseña correcta
        assert verify_password("hashed_password", user.hashed_password)
        
        # Verificar contraseña incorrecta
        assert not verify_password("wrong_password", user.hashed_password)
    
    async def test_token_revocation(
        self,
        test_client,
        db_session,
        test_accounts,
        test_users,
        test_tokens
    ):
        """Test para la revocación de tokens."""
        # Obtener token de prueba
        account = test_accounts[0]
        tokens = test_tokens[account.account_id]["regular"]
        
        # Revocar token
        response = test_client.post(
            "/api/v1/auth/logout",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200
        
        # Intentar usar el token revocado
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 401
    
    async def test_refresh_token(
        self,
        test_client,
        db_session,
        test_accounts,
        test_users,
        test_tokens
    ):
        """Test para la renovación de tokens."""
        # Obtener token de prueba
        account = test_accounts[0]
        tokens = test_tokens[account.account_id]["regular"]
        
        # Renovar token
        response = test_client.post(
            "/api/v1/auth/refresh",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200
        new_token = response.json()["access_token"]
        
        # Verificar que el nuevo token funciona
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": f"Bearer {new_token}"}
        )
        assert response.status_code == 200
    
    async def test_api_key_rotation(
        self,
        test_client,
        db_session,
        test_accounts,
        test_tokens
    ):
        """Test para la rotación de API keys."""
        # Obtener API key de prueba
        account = test_accounts[0]
        old_api_key = test_tokens[account.account_id]["regular"]["api_key"]
        
        # Rotar API key
        response = test_client.post(
            "/api/v1/auth/rotate-api-key",
            headers={"X-API-Key": old_api_key}
        )
        assert response.status_code == 200
        new_api_key = response.json()["api_key"]
        
        # Verificar que la API key antigua ya no funciona
        response = test_client.get(
            "/api/v1/products",
            headers={"X-API-Key": old_api_key}
        )
        assert response.status_code == 401
        
        # Verificar que la nueva API key funciona
        response = test_client.get(
            "/api/v1/products",
            headers={"X-API-Key": new_api_key}
        )
        assert response.status_code == 200 
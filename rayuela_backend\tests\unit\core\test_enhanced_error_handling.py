#!/usr/bin/env python3
"""
Tests para el sistema de manejo de errores mejorado.

Este módulo contiene tests para verificar que el nuevo sistema de manejo de errores
funciona correctamente con request_id, retry_after headers, y estructura JSON consistente.
"""

import pytest
import json
import uuid
from unittest.mock import Mock, patch
from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.responses import Response

from src.core.exceptions import (
    CustomHTTPException,
    RateLimitExceededError,
    ValidationError,
    NotFoundError,
    InternalServerError,
    ErrorCode
)
from src.core.error_handlers import (
    create_error_response,
    handle_rate_limit,
    handle_validation_error,
    handle_not_found,
    handle_internal_error,
    handle_custom_http_exception
)
from src.middleware.request_id_middleware import RequestIDMiddleware, get_request_id


class TestRequestIDMiddleware:
    """Tests para el middleware de Request ID."""

    @pytest.fixture
    def mock_request(self):
        """Mock request para tests."""
        request = Mock(spec=Request)
        request.headers = {}
        request.state = Mock()
        return request

    @pytest.fixture  
    def mock_response(self):
        """Mock response para tests."""
        response = Mock(spec=Response)
        response.headers = {}
        return response

    @pytest.fixture
    def middleware(self):
        """Instancia del middleware para tests."""
        app = Mock()
        return RequestIDMiddleware(app)

    @pytest.mark.asyncio
    async def test_generates_request_id_when_not_provided(self, middleware, mock_request, mock_response):
        """Test que el middleware genera un request_id cuando no se proporciona."""
        async def mock_call_next(request):
            return mock_response

        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Verificar que se generó un request_id
        assert hasattr(mock_request.state, 'request_id')
        assert mock_request.state.request_id is not None
        assert len(mock_request.state.request_id) == 36  # UUID format
        
        # Verificar que se añadió a los headers de respuesta
        assert "X-Request-ID" in response.headers

    @pytest.mark.asyncio
    async def test_uses_existing_request_id_when_provided(self, middleware, mock_request, mock_response):
        """Test que el middleware usa el request_id existente cuando se proporciona."""
        existing_request_id = str(uuid.uuid4())
        mock_request.headers = {"x-request-id": existing_request_id}

        async def mock_call_next(request):
            return mock_response

        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Verificar que usa el request_id proporcionado
        assert mock_request.state.request_id == existing_request_id
        assert response.headers["X-Request-ID"] == existing_request_id

    @pytest.mark.asyncio
    async def test_handles_exceptions_with_request_id(self, middleware, mock_request):
        """Test que el middleware maneja excepciones incluyendo request_id."""
        async def mock_call_next(request):
            raise Exception("Test exception")

        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # Verificar que es una JSONResponse con error
        assert isinstance(response, JSONResponse)
        assert response.status_code == 500
        
        # Verificar contenido del error
        content = json.loads(response.body.decode())
        assert content["error_code"] == "INTERNAL_ERROR"
        assert content["request_id"] == mock_request.state.request_id

    def test_get_request_id_function(self, mock_request):
        """Test de la función get_request_id."""
        test_request_id = str(uuid.uuid4())
        mock_request.state.request_id = test_request_id
        
        result = get_request_id(mock_request)
        assert result == test_request_id

    def test_get_request_id_returns_unknown_when_not_set(self, mock_request):
        """Test que get_request_id retorna 'unknown' cuando no hay request_id."""
        # No establecer request_id
        delattr(mock_request.state, 'request_id') if hasattr(mock_request.state, 'request_id') else None
        
        result = get_request_id(mock_request)
        assert result == "unknown"


class TestEnhancedErrorHandlers:
    """Tests para los manejadores de error mejorados."""

    @pytest.fixture
    def mock_request(self):
        """Mock request con request_id para tests."""
        request = Mock(spec=Request)
        request.state = Mock()
        request.state.request_id = str(uuid.uuid4())
        request.url = Mock()
        request.url.path = "/api/v1/test"
        return request

    def test_create_error_response_basic(self, mock_request):
        """Test básico de create_error_response."""
        response = create_error_response(
            request=mock_request,
            status_code=400,
            message="Test error",
            error_code="TEST_ERROR"
        )
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 400
        
        content = json.loads(response.body.decode())
        assert content["message"] == "Test error"
        assert content["error_code"] == "TEST_ERROR"
        assert content["request_id"] == mock_request.state.request_id
        assert content["path"] == "/api/v1/test"
        assert "timestamp" in content

    def test_create_error_response_with_additional_data(self, mock_request):
        """Test de create_error_response con datos adicionales."""
        additional_data = {"validation_error": True, "field": "email"}
        
        response = create_error_response(
            request=mock_request,
            status_code=422,
            message="Validation failed",
            error_code="VALIDATION_ERROR",
            additional_data=additional_data
        )
        
        content = json.loads(response.body.decode())
        assert content["validation_error"] is True
        assert content["field"] == "email"

    def test_create_error_response_with_headers(self, mock_request):
        """Test de create_error_response con headers adicionales."""
        headers = {"Retry-After": "60"}
        
        response = create_error_response(
            request=mock_request,
            status_code=429,
            message="Rate limit exceeded",
            error_code="RATE_LIMIT_EXCEEDED",
            headers=headers
        )
        
        assert response.headers["Retry-After"] == "60"
        assert response.headers["X-Request-ID"] == mock_request.state.request_id

    @pytest.mark.asyncio
    async def test_handle_rate_limit_with_retry_after(self, mock_request):
        """Test del manejador de rate limit con retry_after."""
        exc = RateLimitExceededError("Too many requests", retry_after=120)
        
        response = await handle_rate_limit(mock_request, exc)
        
        assert response.status_code == 429
        assert response.headers["Retry-After"] == "120"
        
        content = json.loads(response.body.decode())
        assert content["error_code"] == "RATE_LIMIT_EXCEEDED"
        assert content["retry_after"] == 120
        assert "retry_after_description" in content

    @pytest.mark.asyncio
    async def test_handle_validation_error(self, mock_request):
        """Test del manejador de errores de validación."""
        exc = ValidationError("Invalid email format")
        
        response = await handle_validation_error(mock_request, exc)
        
        assert response.status_code == 422
        content = json.loads(response.body.decode())
        assert content["error_code"] == "VALIDATION_ERROR"
        assert content["validation_error"] is True

    @pytest.mark.asyncio
    async def test_handle_not_found(self, mock_request):
        """Test del manejador de errores not found."""
        exc = NotFoundError("Resource not found")
        
        response = await handle_not_found(mock_request, exc)
        
        assert response.status_code == 404
        content = json.loads(response.body.decode())
        assert content["error_code"] == "RESOURCE_NOT_FOUND"

    @pytest.mark.asyncio
    async def test_handle_internal_error_with_support_message(self, mock_request):
        """Test del manejador de errores internos con mensaje de soporte."""
        exc = InternalServerError("Database connection failed")
        
        response = await handle_internal_error(mock_request, exc)
        
        assert response.status_code == 500
        content = json.loads(response.body.decode())
        assert content["error_code"] == "INTERNAL_ERROR"
        assert content["internal_error"] is True
        assert "support_message" in content
        assert mock_request.state.request_id in content["support_message"]

    @pytest.mark.asyncio
    async def test_handle_custom_http_exception_with_dict_detail(self, mock_request):
        """Test del manejador genérico con detail como diccionario."""
        detail = {
            "message": "Custom error occurred",
            "error_code": "CUSTOM_ERROR",
            "extra_info": "Additional context"
        }
        exc = CustomHTTPException(status_code=400, detail=detail)
        
        response = await handle_custom_http_exception(mock_request, exc)
        
        assert response.status_code == 400
        content = json.loads(response.body.decode())
        assert content["message"] == "Custom error occurred"
        assert content["error_code"] == "CUSTOM_ERROR"
        assert content["extra_info"] == "Additional context"

    @pytest.mark.asyncio
    async def test_handle_custom_http_exception_with_string_detail(self, mock_request):
        """Test del manejador genérico con detail como string."""
        exc = CustomHTTPException(
            status_code=400, 
            detail="Simple error message",
            error_code=ErrorCode.INVALID_DATA
        )
        
        response = await handle_custom_http_exception(mock_request, exc)
        
        assert response.status_code == 400
        content = json.loads(response.body.decode())
        assert content["message"] == "Simple error message"
        assert content["error_code"] == "INVALID_DATA"

    @pytest.mark.asyncio
    async def test_handle_custom_http_exception_429_adds_retry_after(self, mock_request):
        """Test que el manejador genérico añade retry_after para errores 429."""
        exc = RateLimitExceededError("Rate limit exceeded", retry_after=90)
        exc.status_code = 429  # Asegurar que es 429
        
        response = await handle_custom_http_exception(mock_request, exc)
        
        assert response.status_code == 429
        assert response.headers["Retry-After"] == "90"
        
        content = json.loads(response.body.decode())
        assert content["retry_after"] == 90
        assert "retry_after_description" in content

    def test_rate_limit_exceeded_error_with_custom_retry_after(self):
        """Test que RateLimitExceededError acepta retry_after personalizado."""
        exc = RateLimitExceededError("Custom limit exceeded", retry_after=300)
        
        assert exc.status_code == 429
        assert exc.retry_after == 300
        assert isinstance(exc.detail, str)

    def test_rate_limit_exceeded_error_default_retry_after(self):
        """Test que RateLimitExceededError usa retry_after por defecto."""
        exc = RateLimitExceededError("Default limit exceeded")
        
        assert exc.retry_after == 60  # Default value


class TestErrorCodeConsistency:
    """Tests para verificar la consistencia de códigos de error."""

    def test_error_codes_are_strings(self):
        """Test que todos los ErrorCode son strings."""
        for error_code in ErrorCode:
            assert isinstance(error_code.value, str)
            assert error_code.value.isupper()
            assert "_" in error_code.value or error_code.value == "CONFLICT"

    def test_custom_http_exception_with_error_code(self):
        """Test que CustomHTTPException maneja ErrorCode correctamente."""
        exc = CustomHTTPException(
            status_code=400,
            detail="Test message",
            error_code=ErrorCode.VALIDATION_ERROR
        )
        
        assert isinstance(exc.detail, dict)
        assert exc.detail["error_code"] == ErrorCode.VALIDATION_ERROR
        assert exc.detail["message"] == "Test message"

    def test_custom_http_exception_with_dict_detail(self):
        """Test que CustomHTTPException maneja detail como dict."""
        detail_dict = {
            "message": "Custom error",
            "extra_field": "extra_value"
        }
        
        exc = CustomHTTPException(
            status_code=400,
            detail=detail_dict,
            error_code=ErrorCode.INVALID_DATA
        )
        
        assert exc.detail["error_code"] == ErrorCode.INVALID_DATA
        assert exc.detail["message"] == "Custom error"
        assert exc.detail["extra_field"] == "extra_value"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 
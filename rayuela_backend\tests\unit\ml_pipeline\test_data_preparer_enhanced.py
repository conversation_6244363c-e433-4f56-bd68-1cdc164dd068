"""
Tests para verificar la funcionalidad mejorada del DataPreparer para LTR.
"""
import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from src.ml_pipeline.training_pipeline import DataPreparer


class TestDataPreparerEnhanced:
    """Tests para verificar las mejoras del DataPreparer para LTR."""

    @pytest.fixture
    def data_preparer(self):
        """Fixture para crear una instancia de DataPreparer."""
        return DataPreparer()

    @pytest.fixture
    def sample_interactions_df(self):
        """Fixture para crear un DataFrame de interacciones de ejemplo."""
        base_time = datetime.now()
        return pd.DataFrame([
            {
                "user_id": 1,
                "item_id": 101,
                "interaction_type": "PURCHASE",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=5)
            },
            {
                "user_id": 1,
                "item_id": 102,
                "interaction_type": "VIEW",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=3)
            },
            {
                "user_id": 1,
                "item_id": 103,
                "interaction_type": "RATING",
                "value": 4.5,
                "timestamp": base_time - timedelta(hours=1)
            },
            {
                "user_id": 2,
                "item_id": 101,
                "interaction_type": "CLICK",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=4)
            },
            {
                "user_id": 2,
                "item_id": 104,
                "interaction_type": "CART",
                "value": 2.0,
                "timestamp": base_time - timedelta(hours=2)
            }
        ])

    def test_interaction_weights_are_properly_defined(self, data_preparer):
        """Test que verifica que los pesos de interacción están correctamente definidos."""
        expected_types = [
            "PURCHASE", "RATING", "CART", "FAVORITE", "WISHLIST",
            "CLICK", "LIKE", "SEARCH", "VIEW"
        ]
        
        for interaction_type in expected_types:
            assert interaction_type in data_preparer.interaction_weights
            weight = data_preparer.interaction_weights[interaction_type]
            assert 0.0 <= weight <= 1.0  # Pesos deben estar en rango válido
        
        # Verificar orden lógico de pesos (PURCHASE > VIEW, etc.)
        assert (data_preparer.interaction_weights["PURCHASE"] > 
                data_preparer.interaction_weights["VIEW"])
        assert (data_preparer.interaction_weights["CART"] > 
                data_preparer.interaction_weights["CLICK"])

    def test_add_relevance_scores_basic(self, data_preparer, sample_interactions_df):
        """Test básico para verificar que se añaden correctamente los relevance_scores."""
        result_df = data_preparer._add_relevance_scores(sample_interactions_df)
        
        # Verificar que se añadieron las columnas necesarias
        assert 'relevance_score' in result_df.columns
        assert 'base_relevance' in result_df.columns
        assert 'value_multiplier' in result_df.columns
        
        # Verificar que los scores están en el rango correcto
        assert result_df['relevance_score'].min() >= 0.0
        assert result_df['relevance_score'].max() <= 1.0

    def test_temporal_split_basic(self, data_preparer, sample_interactions_df):
        """Test básico para verificar la división temporal de datos."""
        # Añadir relevance_scores primero
        df_with_scores = data_preparer._add_relevance_scores(sample_interactions_df)
        
        train_df, test_df = data_preparer._temporal_split(
            df_with_scores, 
            test_size=0.4,
            min_interactions_per_user=2,
            ensure_future_interactions=True
        )
        
        # Verificar que se generaron resultados
        assert isinstance(train_df, pd.DataFrame)
        assert isinstance(test_df, pd.DataFrame)
        
        # Al menos uno de los dos debería tener datos
        assert not (train_df.empty and test_df.empty)

    def test_generate_target_scores_basic(self, data_preparer):
        """Test básico para verificar la generación de target_scores para LTR."""
        recommendations = [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative"
            }
        ]
        
        test_interactions = pd.DataFrame([
            {
                "user_id": 1,
                "item_id": 101,
                "interaction_type": "PURCHASE",
                "value": 1.0,
                "relevance_score": 1.0
            }
        ])
        
        enriched_recs, target_scores = data_preparer.generate_target_scores_for_ltr(
            recommendations=recommendations,
            test_interactions=test_interactions,
            use_relevance_scores=True,
            negative_sampling_ratio=0.0  # Sin negative sampling para test simple
        )
        
        # Verificar que se generaron resultados
        assert len(enriched_recs) > 0
        assert len(target_scores) > 0
        assert len(enriched_recs) == len(target_scores)
        
        # Verificar que las recomendaciones están enriquecidas
        for rec in enriched_recs:
            assert 'is_hit' in rec
            assert 'target_relevance' in rec 
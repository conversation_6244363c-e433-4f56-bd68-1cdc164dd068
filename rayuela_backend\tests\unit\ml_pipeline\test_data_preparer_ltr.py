"""
Tests para verificar la funcionalidad mejorada del DataPreparer para LTR.
"""
import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from src.ml_pipeline.training_pipeline import DataPreparer


class TestDataPreparerLTR:
    """Tests para verificar las mejoras del DataPreparer para LTR."""

    @pytest.fixture
    def data_preparer(self):
        """Fixture para crear una instancia de DataPreparer."""
        return DataPreparer()

    @pytest.fixture
    def sample_interactions_df(self):
        """Fixture para crear un DataFrame de interacciones de ejemplo."""
        base_time = datetime.now()
        return pd.DataFrame([
            {
                "user_id": 1,
                "item_id": 101,
                "interaction_type": "PURCHASE",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=5)
            },
            {
                "user_id": 1,
                "item_id": 102,
                "interaction_type": "VIEW",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=3)
            },
            {
                "user_id": 1,
                "item_id": 103,
                "interaction_type": "RATING",
                "value": 4.5,
                "timestamp": base_time - timedelta(hours=1)
            },
            {
                "user_id": 2,
                "item_id": 101,
                "interaction_type": "CLICK",
                "value": 1.0,
                "timestamp": base_time - timedelta(hours=4)
            },
            {
                "user_id": 2,
                "item_id": 104,
                "interaction_type": "CART",
                "value": 2.0,
                "timestamp": base_time - timedelta(hours=2)
            }
        ])

    @pytest.fixture
    def sample_recommendations(self):
        """Fixture para crear recomendaciones de ejemplo."""
        return [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative"
            },
            {
                "user_id": 1,
                "item_id": 105,  # Item no interactuado
                "collab_score": 0.7,
                "content_score": 0.5,
                "score": 0.7,
                "rank": 2,
                "model_type": "collaborative"
            },
            {
                "user_id": 2,
                "item_id": 104,
                "collab_score": 0.6,
                "content_score": 0.8,
                "score": 0.8,
                "rank": 1,
                "model_type": "content"
            }
        ]

    def test_add_relevance_scores(self, data_preparer, sample_interactions_df):
        """Test que verifica que se añaden correctamente los relevance_scores."""
        result_df = data_preparer._add_relevance_scores(sample_interactions_df)
        
        # Verificar que se añadieron las columnas necesarias
        assert 'relevance_score' in result_df.columns
        assert 'base_relevance' in result_df.columns
        assert 'value_multiplier' in result_df.columns
        
        # Verificar que los scores están en el rango correcto
        assert result_df['relevance_score'].min() >= 0.0
        assert result_df['relevance_score'].max() <= 1.0
        
        # Verificar pesos específicos por tipo de interacción
        purchase_rows = result_df[result_df['interaction_type'] == 'PURCHASE']
        view_rows = result_df[result_df['interaction_type'] == 'VIEW']
        
        assert purchase_rows.iloc[0]['base_relevance'] == 1.0  # PURCHASE tiene peso máximo
        assert view_rows.iloc[0]['base_relevance'] == 0.3      # VIEW tiene peso bajo
        
        # Verificar ajuste por valor para RATING
        rating_rows = result_df[result_df['interaction_type'] == 'RATING']
        assert rating_rows.iloc[0]['value_multiplier'] == 1.2  # Rating >= 4.0 recibe bonus

    def test_temporal_split(self, data_preparer, sample_interactions_df):
        """Test que verifica la división temporal de datos."""
        train_df, test_df = data_preparer._temporal_split(
            sample_interactions_df, 
            test_size=0.4,
            min_interactions_per_user=2,
            ensure_future_interactions=True
        )
        
        # Verificar que ambos sets no están vacíos
        assert not train_df.empty
        assert not test_df.empty
        
        # Verificar que la división es temporal (test posterior a train)
        if not train_df.empty and not test_df.empty:
            max_train_time = train_df['timestamp'].max()
            min_test_time = test_df['timestamp'].min()
            # Puede haber overlap por usuario, pero en general test debería ser posterior
            
        # Verificar que se mantienen usuarios en ambos sets cuando es posible
        train_users = set(train_df['user_id'].unique())
        test_users = set(test_df['user_id'].unique())
        
        # Debería haber al menos un usuario que aparece en ambos
        common_users = train_users.intersection(test_users)
        assert len(common_users) > 0

    def test_generate_target_scores_for_ltr(self, data_preparer, sample_recommendations):
        """Test que verifica la generación de target_scores para LTR."""
        # Crear test_interactions con relevance_scores
        test_interactions = pd.DataFrame([
            {
                "user_id": 1,
                "item_id": 101,
                "interaction_type": "PURCHASE",
                "value": 1.0,
                "relevance_score": 1.0
            },
            {
                "user_id": 2,
                "item_id": 104,
                "interaction_type": "CART",
                "value": 2.0,
                "relevance_score": 0.8
            }
        ])
        
        enriched_recs, target_scores = data_preparer.generate_target_scores_for_ltr(
            recommendations=sample_recommendations,
            test_interactions=test_interactions,
            use_relevance_scores=True,
            negative_sampling_ratio=1.0
        )
        
        # Verificar que se generaron resultados
        assert len(enriched_recs) > 0
        assert len(target_scores) > 0
        assert len(enriched_recs) == len(target_scores)
        
        # Verificar que hay tanto muestras positivas como negativas
        positive_scores = [s for s in target_scores if s > 0]
        negative_scores = [s for s in target_scores if s == 0]
        
        assert len(positive_scores) > 0  # Debe haber interacciones positivas
        assert len(negative_scores) > 0  # Debe haber muestras negativas
        
        # Verificar que las recomendaciones están enriquecidas
        for rec in enriched_recs:
            assert 'is_hit' in rec
            assert 'target_relevance' in rec
            
        # Verificar scores específicos
        # user_id=1, item_id=101 debería tener relevance=1.0 (PURCHASE)
        user1_item101 = [rec for rec in enriched_recs 
                        if rec['user_id'] == 1 and rec['item_id'] == 101]
        if user1_item101:
            assert user1_item101[0]['target_relevance'] == 1.0
            assert user1_item101[0]['is_hit'] == True

    def test_calculate_value_multiplier(self, data_preparer):
        """Test que verifica el cálculo de multiplicadores por valor."""
        # Test para RATING con valores altos
        multiplier_high = data_preparer._calculate_value_multiplier("RATING", 4.5)
        assert multiplier_high == 1.2  # Bonus para rating alto
        
        # Test para RATING con valores medios
        multiplier_mid = data_preparer._calculate_value_multiplier("RATING", 3.0)
        assert multiplier_mid == 1.0  # Neutral
        
        # Test para RATING con valores bajos
        multiplier_low = data_preparer._calculate_value_multiplier("RATING", 2.0)
        assert multiplier_low == 0.7  # Penalty
        
        # Test para PURCHASE con cantidad
        multiplier_purchase = data_preparer._calculate_value_multiplier("PURCHASE", 3.0)
        assert multiplier_purchase == 1.3  # 1.0 + (3.0/10.0) = 1.3
        
        # Test para valor None
        multiplier_none = data_preparer._calculate_value_multiplier("VIEW", None)
        assert multiplier_none == 1.0

    def test_interaction_weights_coverage(self, data_preparer):
        """Test que verifica que todos los tipos de interacción importantes tienen pesos definidos."""
        expected_types = [
            "PURCHASE", "RATING", "CART", "FAVORITE", "WISHLIST",
            "CLICK", "LIKE", "SEARCH", "VIEW"
        ]
        
        for interaction_type in expected_types:
            assert interaction_type in data_preparer.interaction_weights
            weight = data_preparer.interaction_weights[interaction_type]
            assert 0.0 <= weight <= 1.0  # Pesos deben estar en rango válido
        
        # Verificar orden lógico de pesos (PURCHASE > VIEW, etc.)
        assert (data_preparer.interaction_weights["PURCHASE"] > 
                data_preparer.interaction_weights["VIEW"])
        assert (data_preparer.interaction_weights["CART"] > 
                data_preparer.interaction_weights["CLICK"])

    def test_negative_sampling(self, data_preparer):
        """Test que verifica la generación de muestras negativas."""
        user_recs = [
            {"user_id": 1, "item_id": 101, "score": 0.8},
            {"user_id": 1, "item_id": 102, "score": 0.7}
        ]
        positive_items = {103, 104}  # Items con los que sí interactuó
        
        negative_samples, negative_scores = data_preparer._generate_negative_samples(
            user_id=1,
            positive_items=positive_items,
            user_recommendations=user_recs,
            negative_ratio=2.0
        )
        
        # Verificar que se generaron muestras negativas
        expected_negatives = max(1, int(len(positive_items) * 2.0))  # 2 * 2 = 4
        assert len(negative_samples) == expected_negatives
        assert len(negative_scores) == expected_negatives
        
        # Verificar que todas las muestras negativas tienen relevancia 0
        assert all(score == 0.0 for score in negative_scores)
        
        # Verificar propiedades de las muestras negativas
        for sample in negative_samples:
            assert sample['user_id'] == 1
            assert sample['is_hit'] == False
            assert sample['target_relevance'] == 0.0
            assert sample['is_negative_sample'] == True
            assert sample['model_type'] == 'negative_sample'

    def test_temporal_split_edge_cases(self, data_preparer):
        """Test que verifica casos edge en la división temporal."""
        # Test con DataFrame vacío
        empty_df = pd.DataFrame()
        train_empty, test_empty = data_preparer.prepare_data(empty_df)
        assert train_empty.empty
        assert test_empty.empty
        
        # Test con un solo usuario con pocas interacciones
        single_user_df = pd.DataFrame([
            {
                "user_id": 1,
                "item_id": 101,
                "interaction_type": "VIEW",
                "value": 1.0,
                "timestamp": datetime.now()
            }
        ])
        
        train_single, test_single = data_preparer.prepare_data(
            single_user_df,
            temporal_split=True,
            min_interactions_per_user=5  # Más de las que tiene
        )
        
        # Usuario con pocas interacciones debería ir a train
        assert not train_single.empty
        # Test puede estar vacío en este caso

    def test_prepare_data_with_different_strategies(self, data_preparer, sample_interactions_df):
        """Test que verifica diferentes estrategias de preparación de datos."""
        # Test con división temporal
        train_temporal, test_temporal = data_preparer.prepare_data(
            sample_interactions_df,
            temporal_split=True,
            ensure_future_interactions=True
        )
        
        # Test con división aleatoria
        train_random, test_random = data_preparer.prepare_data(
            sample_interactions_df,
            temporal_split=False,
            random_state=42
        )
        
        # Ambos métodos deberían producir resultados
        assert not train_temporal.empty
        assert not train_random.empty
        
        # Los tamaños pueden ser diferentes debido a las estrategias
        total_temporal = len(train_temporal) + len(test_temporal)
        total_random = len(train_random) + len(test_random)
        
        # El total debería ser el mismo más los relevance_scores añadidos
        assert total_temporal >= len(sample_interactions_df)
        assert total_random >= len(sample_interactions_df) 
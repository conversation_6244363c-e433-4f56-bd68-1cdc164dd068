"""
Tests para verificar que el modelo LTR se entrena con el objetivo correcto.
"""
import pytest
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock

from src.ml_pipeline.learning_to_rank import LearningToRankModel


class TestLTRTrainingObjective:
    """Tests para verificar que el modelo LTR se entrena con el objetivo correcto."""

    @pytest.fixture
    def mock_model(self):
        """Fixture para mockear el modelo LTR."""
        mock = MagicMock()
        mock.fit = MagicMock()
        mock.predict = MagicMock(return_value=np.array([0.5, 0.7, 0.3]))
        mock.feature_importances_ = np.array([0.2, 0.3, 0.5])
        return mock

    @pytest.fixture
    def sample_recommendations(self):
        """Fixture para crear recomendaciones de ejemplo."""
        return [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative",
                "is_hit": True,
                "target_relevance": 1.0
            },
            {
                "user_id": 1,
                "item_id": 102,
                "collab_score": 0.7,
                "content_score": 0.5,
                "score": 0.7,
                "rank": 2,
                "model_type": "collaborative",
                "is_hit": False,
                "target_relevance": 0.0
            },
            {
                "user_id": 1,
                "item_id": 103,
                "collab_score": 0.2,
                "content_score": 0.9,
                "score": 0.9,
                "rank": 3,
                "model_type": "content",
                "is_hit": True,
                "target_relevance": 1.0
            }
        ]

    def test_ltr_uses_target_scores_when_provided(self, mock_model, sample_recommendations):
        """Test que verifica que el modelo LTR usa los target_scores cuando se proporcionan."""
        # Mockear el método _create_model para que devuelva nuestro mock
        with patch.object(LearningToRankModel, '_create_model', return_value=mock_model):
            # Mockear StandardScaler
            with patch('sklearn.preprocessing.StandardScaler') as mock_scaler:
                mock_scaler_instance = MagicMock()
                mock_scaler_instance.fit_transform.return_value = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
                mock_scaler_instance.transform.return_value = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
                mock_scaler.return_value = mock_scaler_instance
                
                # Mockear train_test_split
                with patch('sklearn.model_selection.train_test_split') as mock_split:
                    mock_split.return_value = (
                        np.array([[1, 2, 3], [4, 5, 6]]),  # X_train
                        np.array([[7, 8, 9]]),             # X_val
                        np.array([1.0, 0.0]),              # y_train
                        np.array([1.0])                    # y_val
                    )
                    
                    # Crear instancia del modelo LTR
                    ltr_model = LearningToRankModel(account_id=1, model_type="gbdt")
                    
                    # Extraer target_scores de las recomendaciones
                    target_scores = [rec["target_relevance"] for rec in sample_recommendations]
                    
                    # Entrenar modelo con target_scores explícitos
                    ltr_model.train(
                        training_data=sample_recommendations,
                        target_scores=target_scores
                    )
                    
                    # Verificar que el modelo se entrenó con los target_scores correctos
                    # y no con una combinación ponderada de los scores base
                    mock_model.fit.assert_called_once()
                    
                    # Obtener los argumentos con los que se llamó a fit
                    _, y_train_arg = mock_model.fit.call_args[0]
                    
                    # Verificar que y_train_arg contiene los valores esperados (1.0, 0.0)
                    # que corresponden a los target_scores y no a una combinación ponderada
                    np.testing.assert_array_equal(y_train_arg, np.array([1.0, 0.0]))

    def test_ltr_fails_when_no_target_scores_provided(self, mock_model, sample_recommendations):
        """Test que verifica que el modelo LTR falla de manera controlada cuando no se proporcionan target_scores."""
        # Crear instancia del modelo LTR
        ltr_model = LearningToRankModel(account_id=1, model_type="gbdt")
        
        # Entrenar modelo SIN target_scores explícitos
        with patch('src.utils.base_logger.log_error') as mock_log_error:
            result = ltr_model.train(
                training_data=sample_recommendations,
                target_scores=None  # No proporcionar target_scores
            )
            
            # Verificar que se emitió un error
            mock_log_error.assert_called_once()
            
            # Verificar que se retornó un error
            assert "error" in result
            assert "target_scores requeridos" in result["error"]
            
            # Verificar que el modelo NO se entrenó
            assert not ltr_model.trained

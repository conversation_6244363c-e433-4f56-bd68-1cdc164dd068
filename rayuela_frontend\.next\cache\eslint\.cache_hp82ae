[{"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx": "1", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\billing\\page.tsx": "2", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx": "3", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\models\\page.tsx": "4", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\page.tsx": "5", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx": "6", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\page.tsx": "7", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx": "8", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx": "9", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx": "10", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx": "11", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\contact-sales\\page.tsx": "12", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx": "13", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\quickstart\\python\\page.tsx": "14", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\features\\page.tsx": "15", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\home\\page.tsx": "16", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\layout.tsx": "17", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\cookies\\page.tsx": "18", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\dpa\\page.tsx": "19", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\layout.tsx": "20", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\notice\\page.tsx": "21", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\privacy\\page.tsx": "22", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\terms\\page.tsx": "23", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\login\\page.tsx": "24", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\pricing\\page.tsx": "25", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\verify-email\\[token]\\page.tsx": "26", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\api\\health\\route.ts": "27", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx": "28", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\page.tsx": "29", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\register\\page.tsx": "30", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\sitemap.ts": "31", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\AdminRoute.tsx": "32", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\InitialApiKeyModal.tsx": "33", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\LoginForm.tsx": "34", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\RegisterForm.tsx": "35", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\CurrentPlanCard.tsx": "36", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlanCard.tsx": "37", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlansGrid.tsx": "38", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ApiStatus.tsx": "39", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingButton.tsx": "40", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingPortalButton.tsx": "41", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ConfidenceMetricsChart.tsx": "42", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\DateRangeSelector.tsx": "43", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\EmailVerificationBanner.tsx": "44", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\GettingStartedChecklist.tsx": "45", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Header.tsx": "46", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\MetricRecommendations.tsx": "47", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\QuickActions.tsx": "48", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\RecommendationMetricsChart.tsx": "49", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\SandboxResetButton.tsx": "50", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Sidebar.tsx": "51", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageChart.tsx": "52", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageDashboard.tsx": "53", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\documentation\\iconography-guide.tsx": "54", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\DataIngestionModal.tsx": "55", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\TrainingModal.tsx": "56", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\accordion.tsx": "57", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert-dialog.tsx": "58", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert.tsx": "59", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\animation-examples.tsx": "60", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\badge.tsx": "61", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx": "62", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\calendar.tsx": "63", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\card.tsx": "64", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\checkbox.tsx": "65", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\contrast-improvements.tsx": "66", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\dialog.tsx": "67", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\form.tsx": "68", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\icon.tsx": "69", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\iconography-improvements-showcase.tsx": "70", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\input.tsx": "71", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\label.tsx": "72", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\layout.tsx": "73", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\logo.tsx": "74", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\popover.tsx": "75", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\progress.tsx": "76", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\select.tsx": "77", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\skeleton.tsx": "78", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\sonner.tsx": "79", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\spacing-system.tsx": "80", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\table.tsx": "81", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tabs.tsx": "82", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip-helper.tsx": "83", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip.tsx": "84", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\typography-showcase.tsx": "85", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\ui-improvements-showcase.tsx": "86", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\analysis.ts": "87", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api\\recommendation-metrics.ts": "88", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api-new.ts": "89", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api.ts": "90", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx": "91", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\chart-utils.ts": "92", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\constants.ts": "93", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\error-handler.ts": "94", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\api-client.ts": "95", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\migration-helper.ts": "96", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\rayuelaAPI.ts": "97", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\schemas\\index.ts": "98", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\index.ts": "99", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useIngestionJobs.ts": "100", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useModels.ts": "101", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useTrainingJobs.ts": "102", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks.ts": "103", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\recommendationRules.ts": "104", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\seo.ts": "105", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useAccountInfo.ts": "106", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useApiKeys.ts": "107", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\usePlans.ts": "108", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useRecommendationMetrics.ts": "109", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageHistory.ts": "110", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageSummary.ts": "111", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\billing.ts": "112", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\format.tsx": "113", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils.ts": "114", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.js": "115", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.ts": "116", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\generate-og-image.js": "117", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\test-openapi-generation.ts": "118", "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\types\\checklist.ts": "119"}, {"size": 26716, "mtime": 1750779675487, "results": "120", "hashOfConfig": "121"}, {"size": 3760, "mtime": 1750792521325, "results": "122", "hashOfConfig": "121"}, {"size": 2319, "mtime": 1750097912302, "results": "123", "hashOfConfig": "121"}, {"size": 7624, "mtime": 1750553525426, "results": "124", "hashOfConfig": "121"}, {"size": 14849, "mtime": 1750787778429, "results": "125", "hashOfConfig": "121"}, {"size": 21887, "mtime": 1750787812725, "results": "126", "hashOfConfig": "121"}, {"size": 17084, "mtime": 1750793583840, "results": "127", "hashOfConfig": "121"}, {"size": 18367, "mtime": 1750793601707, "results": "128", "hashOfConfig": "121"}, {"size": 20331, "mtime": 1750555851754, "results": "129", "hashOfConfig": "121"}, {"size": 10604, "mtime": 1750383021571, "results": "130", "hashOfConfig": "121"}, {"size": 478, "mtime": 1750097913004, "results": "131", "hashOfConfig": "121"}, {"size": 9929, "mtime": 1750266331725, "results": "132", "hashOfConfig": "121"}, {"size": 8810, "mtime": 1750097913235, "results": "133", "hashOfConfig": "121"}, {"size": 10535, "mtime": 1750556509149, "results": "134", "hashOfConfig": "121"}, {"size": 6441, "mtime": 1750097913379, "results": "135", "hashOfConfig": "121"}, {"size": 9782, "mtime": 1750770473556, "results": "136", "hashOfConfig": "121"}, {"size": 365, "mtime": 1750097913049, "results": "137", "hashOfConfig": "121"}, {"size": 15089, "mtime": 1750097913508, "results": "138", "hashOfConfig": "121"}, {"size": 32153, "mtime": 1750266312726, "results": "139", "hashOfConfig": "121"}, {"size": 2258, "mtime": 1750223072241, "results": "140", "hashOfConfig": "121"}, {"size": 16680, "mtime": 1750266309161, "results": "141", "hashOfConfig": "121"}, {"size": 40452, "mtime": 1750097913762, "results": "142", "hashOfConfig": "121"}, {"size": 27427, "mtime": 1750097913827, "results": "143", "hashOfConfig": "121"}, {"size": 710, "mtime": 1750771153337, "results": "144", "hashOfConfig": "121"}, {"size": 8143, "mtime": 1750793613918, "results": "145", "hashOfConfig": "121"}, {"size": 3643, "mtime": 1749492521016, "results": "146", "hashOfConfig": "121"}, {"size": 287, "mtime": 1748519405030, "results": "147", "hashOfConfig": "121"}, {"size": 1057, "mtime": 1750222740893, "results": "148", "hashOfConfig": "121"}, {"size": 927, "mtime": 1750097912256, "results": "149", "hashOfConfig": "121"}, {"size": 914, "mtime": 1750097914558, "results": "150", "hashOfConfig": "121"}, {"size": 3089, "mtime": 1749090290031, "results": "151", "hashOfConfig": "121"}, {"size": 877, "mtime": 1750217695638, "results": "152", "hashOfConfig": "121"}, {"size": 11276, "mtime": 1750556485369, "results": "153", "hashOfConfig": "121"}, {"size": 6563, "mtime": 1750793626203, "results": "154", "hashOfConfig": "121"}, {"size": 6311, "mtime": 1750097914699, "results": "155", "hashOfConfig": "121"}, {"size": 3240, "mtime": 1750793566845, "results": "156", "hashOfConfig": "121"}, {"size": 5632, "mtime": 1750794392438, "results": "157", "hashOfConfig": "121"}, {"size": 5078, "mtime": 1750569869152, "results": "158", "hashOfConfig": "121"}, {"size": 3563, "mtime": 1750097914873, "results": "159", "hashOfConfig": "121"}, {"size": 2775, "mtime": 1750793651265, "results": "160", "hashOfConfig": "121"}, {"size": 2076, "mtime": 1750793667709, "results": "161", "hashOfConfig": "121"}, {"size": 4239, "mtime": 1750772157366, "results": "162", "hashOfConfig": "121"}, {"size": 3361, "mtime": 1750793682868, "results": "163", "hashOfConfig": "121"}, {"size": 2596, "mtime": 1750281496798, "results": "164", "hashOfConfig": "121"}, {"size": 38616, "mtime": 1750793734298, "results": "165", "hashOfConfig": "121"}, {"size": 1524, "mtime": 1750097915309, "results": "166", "hashOfConfig": "121"}, {"size": 10531, "mtime": 1750793756001, "results": "167", "hashOfConfig": "121"}, {"size": 2668, "mtime": 1750097915369, "results": "168", "hashOfConfig": "121"}, {"size": 21559, "mtime": 1750771731594, "results": "169", "hashOfConfig": "121"}, {"size": 5876, "mtime": 1750770267031, "results": "170", "hashOfConfig": "121"}, {"size": 5367, "mtime": 1750793769387, "results": "171", "hashOfConfig": "121"}, {"size": 9543, "mtime": 1750794448198, "results": "172", "hashOfConfig": "121"}, {"size": 16622, "mtime": 1750793979068, "results": "173", "hashOfConfig": "121"}, {"size": 9835, "mtime": 1750281337684, "results": "174", "hashOfConfig": "121"}, {"size": 9375, "mtime": 1750787739602, "results": "175", "hashOfConfig": "121"}, {"size": 9223, "mtime": 1750694351391, "results": "176", "hashOfConfig": "121"}, {"size": 2235, "mtime": 1750097916496, "results": "177", "hashOfConfig": "121"}, {"size": 4572, "mtime": 1750097916550, "results": "178", "hashOfConfig": "121"}, {"size": 2050, "mtime": 1750097916563, "results": "179", "hashOfConfig": "121"}, {"size": 5015, "mtime": 1750267481350, "results": "180", "hashOfConfig": "121"}, {"size": 2260, "mtime": 1750097916610, "results": "181", "hashOfConfig": "121"}, {"size": 2208, "mtime": 1750097916624, "results": "182", "hashOfConfig": "121"}, {"size": 2917, "mtime": 1750097916636, "results": "183", "hashOfConfig": "121"}, {"size": 2166, "mtime": 1750097916670, "results": "184", "hashOfConfig": "121"}, {"size": 1088, "mtime": 1750097916677, "results": "185", "hashOfConfig": "121"}, {"size": 6595, "mtime": 1750442081530, "results": "186", "hashOfConfig": "121"}, {"size": 3948, "mtime": 1750097916993, "results": "187", "hashOfConfig": "121"}, {"size": 4624, "mtime": 1750097917041, "results": "188", "hashOfConfig": "121"}, {"size": 4749, "mtime": 1750787760535, "results": "189", "hashOfConfig": "121"}, {"size": 18348, "mtime": 1750441898294, "results": "190", "hashOfConfig": "121"}, {"size": 994, "mtime": 1750097917242, "results": "191", "hashOfConfig": "121"}, {"size": 635, "mtime": 1750097917249, "results": "192", "hashOfConfig": "121"}, {"size": 5400, "mtime": 1750771524538, "results": "193", "hashOfConfig": "121"}, {"size": 1079, "mtime": 1750097917259, "results": "194", "hashOfConfig": "121"}, {"size": 1683, "mtime": 1750097917306, "results": "195", "hashOfConfig": "121"}, {"size": 819, "mtime": 1750097917315, "results": "196", "hashOfConfig": "121"}, {"size": 5928, "mtime": 1750097917352, "results": "197", "hashOfConfig": "121"}, {"size": 382, "mtime": 1750097917361, "results": "198", "hashOfConfig": "121"}, {"size": 589, "mtime": 1750097917372, "results": "199", "hashOfConfig": "121"}, {"size": 3947, "mtime": 1750097917916, "results": "200", "hashOfConfig": "121"}, {"size": 3236, "mtime": 1750097917947, "results": "201", "hashOfConfig": "121"}, {"size": 1975, "mtime": 1750097917966, "results": "202", "hashOfConfig": "121"}, {"size": 8748, "mtime": 1750097917988, "results": "203", "hashOfConfig": "121"}, {"size": 1952, "mtime": 1750097918008, "results": "204", "hashOfConfig": "121"}, {"size": 7459, "mtime": 1750097918044, "results": "205", "hashOfConfig": "121"}, {"size": 8715, "mtime": 1750097918090, "results": "206", "hashOfConfig": "121"}, {"size": 44508, "mtime": 1750794051119, "results": "207", "hashOfConfig": "121"}, {"size": 3979, "mtime": 1750794635711, "results": "208", "hashOfConfig": "121"}, {"size": 1, "mtime": 1749161534067, "results": "209", "hashOfConfig": "121"}, {"size": 9597, "mtime": 1750794568304, "results": "210", "hashOfConfig": "121"}, {"size": 13611, "mtime": 1750794859263, "results": "211", "hashOfConfig": "121"}, {"size": 5461, "mtime": 1750794927868, "results": "212", "hashOfConfig": "121"}, {"size": 15006, "mtime": 1748519405047, "results": "213", "hashOfConfig": "121"}, {"size": 4507, "mtime": 1749588879246, "results": "214", "hashOfConfig": "121"}, {"size": 2743, "mtime": 1750058002811, "results": "215", "hashOfConfig": "121"}, {"size": 1996, "mtime": 1750058002911, "results": "216", "hashOfConfig": "121"}, {"size": 118636, "mtime": 1750788084752, "results": "217", "hashOfConfig": "121"}, {"size": 114, "mtime": 1750058003810, "results": "218", "hashOfConfig": "121"}, {"size": 527, "mtime": 1750555066472, "results": "219", "hashOfConfig": "121"}, {"size": 7883, "mtime": 1750794988431, "results": "220", "hashOfConfig": "121"}, {"size": 2222, "mtime": 1750553152059, "results": "221", "hashOfConfig": "121"}, {"size": 7929, "mtime": 1750795053620, "results": "222", "hashOfConfig": "121"}, {"size": 583, "mtime": 1748519405050, "results": "223", "hashOfConfig": "121"}, {"size": 12402, "mtime": 1750222554841, "results": "224", "hashOfConfig": "121"}, {"size": 3387, "mtime": 1749582311992, "results": "225", "hashOfConfig": "121"}, {"size": 4265, "mtime": 1750694300273, "results": "226", "hashOfConfig": "121"}, {"size": 7745, "mtime": 1750222559034, "results": "227", "hashOfConfig": "121"}, {"size": 1619, "mtime": 1750793552175, "results": "228", "hashOfConfig": "121"}, {"size": 9903, "mtime": 1750795252547, "results": "229", "hashOfConfig": "121"}, {"size": 5398, "mtime": 1750282113311, "results": "230", "hashOfConfig": "121"}, {"size": 6700, "mtime": 1750560521921, "results": "231", "hashOfConfig": "121"}, {"size": 7829, "mtime": 1750792507903, "results": "232", "hashOfConfig": "121"}, {"size": 6022, "mtime": 1750771203086, "results": "233", "hashOfConfig": "121"}, {"size": 172, "mtime": 1748519405052, "results": "234", "hashOfConfig": "121"}, {"size": 10632, "mtime": 1750218396315, "results": "235", "hashOfConfig": "236"}, {"size": 11165, "mtime": 1749492521056, "results": "237", "hashOfConfig": "121"}, {"size": 3941, "mtime": 1748915794149, "results": "238", "hashOfConfig": "236"}, {"size": 12167, "mtime": 1750223700806, "results": "239", "hashOfConfig": "121"}, {"size": 657, "mtime": 1748519405054, "results": "240", "hashOfConfig": "121"}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8d3bfx", {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 13, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xktfx2", {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\api-keys\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\billing\\page.tsx", ["598"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\models\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\usage\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\contact-sales\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\quickstart\\python\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\features\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\home\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\cookies\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\dpa\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\notice\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\privacy\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\legal\\terms\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\login\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\pricing\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\verify-email\\[token]\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\api\\health\\route.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\register\\page.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\sitemap.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\AdminRoute.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\InitialApiKeyModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\auth\\RegisterForm.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\CurrentPlanCard.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlanCard.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\billing\\PlansGrid.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ApiStatus.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingButton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\BillingPortalButton.tsx", ["599"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\ConfidenceMetricsChart.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\DateRangeSelector.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\EmailVerificationBanner.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\GettingStartedChecklist.tsx", ["600", "601", "602"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Header.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\MetricRecommendations.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\QuickActions.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\RecommendationMetricsChart.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\SandboxResetButton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\Sidebar.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageChart.tsx", ["603", "604", "605", "606", "607"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\dashboard\\UsageDashboard.tsx", ["608", "609", "610"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\documentation\\iconography-guide.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\DataIngestionModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\pipeline\\TrainingModal.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\accordion.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\alert.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\animation-examples.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\badge.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\calendar.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\card.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\checkbox.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\contrast-improvements.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\dialog.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\form.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\icon.tsx", ["611"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\iconography-improvements-showcase.tsx", ["612", "613"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\input.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\label.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\layout.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\logo.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\popover.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\progress.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\select.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\sonner.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\spacing-system.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\table.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tabs.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip-helper.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\tooltip.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\typography-showcase.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\ui-improvements-showcase.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\analysis.ts", ["614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api\\recommendation-metrics.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api-new.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\api.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\auth.tsx", ["633"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\chart-utils.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\constants.ts", ["634"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\error-handler.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\api-client.ts", ["635", "636"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\migration-helper.ts", ["637", "638"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\rayuelaAPI.ts", ["639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\generated\\schemas\\index.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\index.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useIngestionJobs.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useModels.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks\\useTrainingJobs.ts", ["652", "653", "654", "655", "656"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\hooks.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\recommendationRules.ts", ["657"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\seo.ts", ["658"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useAccountInfo.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useApiKeys.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\usePlans.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useRecommendationMetrics.ts", ["659"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageHistory.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\useUsageSummary.ts", ["660"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\billing.ts", ["661"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils\\format.tsx", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\lib\\utils.ts", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.js", [], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\fetch-openapi.ts", ["662", "663", "664", "665"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\generate-og-image.js", ["666", "667"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\scripts\\test-openapi-generation.ts", ["668", "669", "670", "671"], [], "D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\types\\checklist.ts", [], [], {"ruleId": "672", "severity": 1, "message": "673", "line": 78, "column": 31, "nodeType": "674", "messageId": "675", "endLine": 78, "endColumn": 34, "suggestions": "676"}, {"ruleId": "677", "severity": 1, "message": "678", "line": 10, "column": 11, "nodeType": "679", "messageId": "680", "endLine": 10, "endColumn": 35, "suggestions": "681"}, {"ruleId": "682", "severity": 1, "message": "683", "line": 401, "column": 6, "nodeType": "684", "endLine": 401, "endColumn": 8, "suggestions": "685"}, {"ruleId": "682", "severity": 1, "message": "686", "line": 478, "column": 6, "nodeType": "684", "endLine": 478, "endColumn": 30, "suggestions": "687"}, {"ruleId": "682", "severity": 1, "message": "686", "line": 541, "column": 6, "nodeType": "684", "endLine": 541, "endColumn": 64, "suggestions": "688"}, {"ruleId": "689", "severity": 1, "message": "690", "line": 18, "column": 16, "nodeType": null, "messageId": "691", "endLine": 18, "endColumn": 27}, {"ruleId": "689", "severity": 1, "message": "692", "line": 127, "column": 9, "nodeType": null, "messageId": "691", "endLine": 127, "endColumn": 21}, {"ruleId": "689", "severity": 1, "message": "693", "line": 142, "column": 9, "nodeType": null, "messageId": "691", "endLine": 142, "endColumn": 20}, {"ruleId": "689", "severity": 1, "message": "694", "line": 156, "column": 9, "nodeType": null, "messageId": "691", "endLine": 156, "endColumn": 24}, {"ruleId": "689", "severity": 1, "message": "695", "line": 196, "column": 9, "nodeType": null, "messageId": "691", "endLine": 196, "endColumn": 23}, {"ruleId": "689", "severity": 1, "message": "696", "line": 61, "column": 22, "nodeType": null, "messageId": "691", "endLine": 61, "endColumn": 34}, {"ruleId": "689", "severity": 1, "message": "697", "line": 61, "column": 43, "nodeType": null, "messageId": "691", "endLine": 61, "endColumn": 53}, {"ruleId": "689", "severity": 1, "message": "698", "line": 140, "column": 9, "nodeType": null, "messageId": "691", "endLine": 140, "endColumn": 15}, {"ruleId": "677", "severity": 1, "message": "678", "line": 91, "column": 11, "nodeType": "679", "messageId": "680", "endLine": 91, "endColumn": 26, "suggestions": "699"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 270, "column": 66, "nodeType": "674", "messageId": "675", "endLine": 270, "endColumn": 69, "suggestions": "700"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 293, "column": 82, "nodeType": "674", "messageId": "675", "endLine": 293, "endColumn": 85, "suggestions": "701"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 69, "column": 43, "nodeType": "674", "messageId": "675", "endLine": 69, "endColumn": 46, "suggestions": "702"}, {"ruleId": "689", "severity": 1, "message": "703", "line": 75, "column": 10, "nodeType": null, "messageId": "691", "endLine": 75, "endColumn": 21}, {"ruleId": "672", "severity": 1, "message": "673", "line": 221, "column": 80, "nodeType": "674", "messageId": "675", "endLine": 221, "endColumn": 83, "suggestions": "704"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 239, "column": 64, "nodeType": "674", "messageId": "675", "endLine": 239, "endColumn": 67, "suggestions": "705"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 240, "column": 70, "nodeType": "674", "messageId": "675", "endLine": 240, "endColumn": 73, "suggestions": "706"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 244, "column": 89, "nodeType": "674", "messageId": "675", "endLine": 244, "endColumn": 92, "suggestions": "707"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 246, "column": 65, "nodeType": "674", "messageId": "675", "endLine": 246, "endColumn": 68, "suggestions": "708"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 259, "column": 42, "nodeType": "674", "messageId": "675", "endLine": 259, "endColumn": 45, "suggestions": "709"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 261, "column": 60, "nodeType": "674", "messageId": "675", "endLine": 261, "endColumn": 63, "suggestions": "710"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 270, "column": 64, "nodeType": "674", "messageId": "675", "endLine": 270, "endColumn": 67, "suggestions": "711"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 271, "column": 70, "nodeType": "674", "messageId": "675", "endLine": 271, "endColumn": 73, "suggestions": "712"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 276, "column": 59, "nodeType": "674", "messageId": "675", "endLine": 276, "endColumn": 62, "suggestions": "713"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 292, "column": 62, "nodeType": "674", "messageId": "675", "endLine": 292, "endColumn": 65, "suggestions": "714"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 301, "column": 61, "nodeType": "674", "messageId": "675", "endLine": 301, "endColumn": 64, "suggestions": "715"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 325, "column": 73, "nodeType": "674", "messageId": "675", "endLine": 325, "endColumn": 76, "suggestions": "716"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 443, "column": 27, "nodeType": "674", "messageId": "675", "endLine": 443, "endColumn": 30, "suggestions": "717"}, {"ruleId": "689", "severity": 1, "message": "718", "line": 571, "column": 3, "nodeType": null, "messageId": "691", "endLine": 571, "endColumn": 14}, {"ruleId": "672", "severity": 1, "message": "673", "line": 681, "column": 67, "nodeType": "674", "messageId": "675", "endLine": 681, "endColumn": 70, "suggestions": "719"}, {"ruleId": "689", "severity": 1, "message": "720", "line": 816, "column": 22, "nodeType": null, "messageId": "691", "endLine": 816, "endColumn": 27}, {"ruleId": "689", "severity": 1, "message": "721", "line": 12, "column": 3, "nodeType": null, "messageId": "691", "endLine": 12, "endColumn": 19}, {"ruleId": "672", "severity": 1, "message": "673", "line": 223, "column": 74, "nodeType": "674", "messageId": "675", "endLine": 223, "endColumn": 77, "suggestions": "722"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 74, "column": 27, "nodeType": "674", "messageId": "675", "endLine": 74, "endColumn": 30, "suggestions": "723"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 75, "column": 27, "nodeType": "674", "messageId": "675", "endLine": 75, "endColumn": 30, "suggestions": "724"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 47, "column": 19, "nodeType": "674", "messageId": "675", "endLine": 47, "endColumn": 22, "suggestions": "725"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 75, "column": 22, "nodeType": "674", "messageId": "675", "endLine": 75, "endColumn": 25, "suggestions": "726"}, {"ruleId": null, "message": "727", "line": 271, "column": 1, "severity": 1, "nodeType": null, "fix": "728"}, {"ruleId": null, "message": "727", "line": 304, "column": 1, "severity": 1, "nodeType": null, "fix": "729"}, {"ruleId": null, "message": "727", "line": 345, "column": 1, "severity": 1, "nodeType": null, "fix": "730"}, {"ruleId": null, "message": "727", "line": 541, "column": 1, "severity": 1, "nodeType": null, "fix": "731"}, {"ruleId": null, "message": "727", "line": 567, "column": 1, "severity": 1, "nodeType": null, "fix": "732"}, {"ruleId": null, "message": "727", "line": 867, "column": 1, "severity": 1, "nodeType": null, "fix": "733"}, {"ruleId": null, "message": "727", "line": 951, "column": 1, "severity": 1, "nodeType": null, "fix": "734"}, {"ruleId": null, "message": "727", "line": 1035, "column": 1, "severity": 1, "nodeType": null, "fix": "735"}, {"ruleId": null, "message": "727", "line": 1052, "column": 1, "severity": 1, "nodeType": null, "fix": "736"}, {"ruleId": null, "message": "727", "line": 1106, "column": 1, "severity": 1, "nodeType": null, "fix": "737"}, {"ruleId": null, "message": "727", "line": 1151, "column": 1, "severity": 1, "nodeType": null, "fix": "738"}, {"ruleId": null, "message": "727", "line": 1174, "column": 1, "severity": 1, "nodeType": null, "fix": "739"}, {"ruleId": null, "message": "727", "line": 1317, "column": 1, "severity": 1, "nodeType": null, "fix": "740"}, {"ruleId": "689", "severity": 1, "message": "741", "line": 62, "column": 66, "nodeType": null, "messageId": "691", "endLine": 62, "endColumn": 67}, {"ruleId": "689", "severity": 1, "message": "741", "line": 68, "column": 63, "nodeType": null, "messageId": "691", "endLine": 68, "endColumn": 64}, {"ruleId": "689", "severity": 1, "message": "741", "line": 115, "column": 63, "nodeType": null, "messageId": "691", "endLine": 115, "endColumn": 64}, {"ruleId": "689", "severity": 1, "message": "741", "line": 121, "column": 60, "nodeType": null, "messageId": "691", "endLine": 121, "endColumn": 61}, {"ruleId": "689", "severity": 1, "message": "742", "line": 163, "column": 32, "nodeType": null, "messageId": "691", "endLine": 163, "endColumn": 42}, {"ruleId": "672", "severity": 1, "message": "673", "line": 32, "column": 29, "nodeType": "674", "messageId": "675", "endLine": 32, "endColumn": 32, "suggestions": "743"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 86, "column": 101, "nodeType": "674", "messageId": "675", "endLine": 86, "endColumn": 104, "suggestions": "744"}, {"ruleId": "689", "severity": 1, "message": "745", "line": 179, "column": 11, "nodeType": null, "messageId": "691", "endLine": 179, "endColumn": 28}, {"ruleId": "672", "severity": 1, "message": "673", "line": 49, "column": 28, "nodeType": "674", "messageId": "675", "endLine": 49, "endColumn": 31, "suggestions": "746"}, {"ruleId": "689", "severity": 1, "message": "747", "line": 154, "column": 12, "nodeType": null, "messageId": "691", "endLine": 154, "endColumn": 17}, {"ruleId": "672", "severity": 1, "message": "673", "line": 24, "column": 36, "nodeType": "674", "messageId": "675", "endLine": 24, "endColumn": 39, "suggestions": "748"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 108, "column": 17, "nodeType": "674", "messageId": "675", "endLine": 108, "endColumn": 20, "suggestions": "749"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 163, "column": 17, "nodeType": "674", "messageId": "675", "endLine": 163, "endColumn": 20, "suggestions": "750"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 164, "column": 17, "nodeType": "674", "messageId": "675", "endLine": 164, "endColumn": 20, "suggestions": "751"}, {"ruleId": "752", "severity": 1, "message": "753", "line": 4, "column": 12, "nodeType": "754", "messageId": "755", "endLine": 4, "endColumn": 25}, {"ruleId": "752", "severity": 1, "message": "753", "line": 5, "column": 14, "nodeType": "754", "messageId": "755", "endLine": 5, "endColumn": 29}, {"ruleId": "672", "severity": 1, "message": "673", "line": 22, "column": 13, "nodeType": "674", "messageId": "675", "endLine": 22, "endColumn": 16, "suggestions": "756"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 37, "column": 79, "nodeType": "674", "messageId": "675", "endLine": 37, "endColumn": 82, "suggestions": "757"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 260, "column": 37, "nodeType": "674", "messageId": "675", "endLine": 260, "endColumn": 40, "suggestions": "758"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 260, "column": 62, "nodeType": "674", "messageId": "675", "endLine": 260, "endColumn": 65, "suggestions": "759"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["760", "761"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["762"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'accountData' and 'getChecklistStatus'. Either include them or remove the dependency array.", "ArrayExpression", ["763"], "React Hook useEffect has a missing dependency: 'updateChecklistState'. Either include it or remove the dependency array.", ["764"], ["765"], "@typescript-eslint/no-unused-vars", "'TabsContent' is defined but never used.", "unusedVar", "'apiCallsData' is assigned a value but never used.", "'storageData' is assigned a value but never used.", "'apiCallsOptions' is assigned a value but never used.", "'storageOptions' is assigned a value but never used.", "'plansLoading' is assigned a value but never used.", "'plansError' is assigned a value but never used.", "'totals' is assigned a value but never used.", ["766"], ["767", "768"], ["769", "770"], ["771", "772"], "'getRuleById' is defined but never used.", ["773", "774"], ["775", "776"], ["777", "778"], ["779", "780"], ["781", "782"], ["783", "784"], ["785", "786"], ["787", "788"], ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797", "798"], ["799", "800"], "'isNewApiKey' is defined but never used.", ["801", "802"], "'model' is defined but never used.", "'RegisterResponse' is defined but never used.", ["803", "804"], ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-redeclare').", {"range": "813", "text": "814"}, {"range": "815", "text": "814"}, {"range": "816", "text": "814"}, {"range": "817", "text": "814"}, {"range": "818", "text": "814"}, {"range": "819", "text": "814"}, {"range": "820", "text": "814"}, {"range": "821", "text": "814"}, {"range": "822", "text": "814"}, {"range": "823", "text": "814"}, {"range": "824", "text": "814"}, {"range": "825", "text": "814"}, {"range": "826", "text": "814"}, "'_' is defined but never used.", "'parameters' is defined but never used.", ["827", "828"], ["829", "830"], "'ConfidenceMetrics' is defined but never used.", ["831", "832"], "'error' is defined but never used.", ["833", "834"], ["835", "836"], ["837", "838"], ["839", "840"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["841", "842"], ["843", "844"], ["845", "846"], ["847", "848"], {"messageId": "849", "fix": "850", "desc": "851"}, {"messageId": "852", "fix": "853", "desc": "854"}, {"messageId": "855", "fix": "856", "desc": "857"}, {"desc": "858", "fix": "859"}, {"desc": "860", "fix": "861"}, {"desc": "862", "fix": "863"}, {"messageId": "855", "fix": "864", "desc": "857"}, {"messageId": "849", "fix": "865", "desc": "851"}, {"messageId": "852", "fix": "866", "desc": "854"}, {"messageId": "849", "fix": "867", "desc": "851"}, {"messageId": "852", "fix": "868", "desc": "854"}, {"messageId": "849", "fix": "869", "desc": "851"}, {"messageId": "852", "fix": "870", "desc": "854"}, {"messageId": "849", "fix": "871", "desc": "851"}, {"messageId": "852", "fix": "872", "desc": "854"}, {"messageId": "849", "fix": "873", "desc": "851"}, {"messageId": "852", "fix": "874", "desc": "854"}, {"messageId": "849", "fix": "875", "desc": "851"}, {"messageId": "852", "fix": "876", "desc": "854"}, {"messageId": "849", "fix": "877", "desc": "851"}, {"messageId": "852", "fix": "878", "desc": "854"}, {"messageId": "849", "fix": "879", "desc": "851"}, {"messageId": "852", "fix": "880", "desc": "854"}, {"messageId": "849", "fix": "881", "desc": "851"}, {"messageId": "852", "fix": "882", "desc": "854"}, {"messageId": "849", "fix": "883", "desc": "851"}, {"messageId": "852", "fix": "884", "desc": "854"}, {"messageId": "849", "fix": "885", "desc": "851"}, {"messageId": "852", "fix": "886", "desc": "854"}, {"messageId": "849", "fix": "887", "desc": "851"}, {"messageId": "852", "fix": "888", "desc": "854"}, {"messageId": "849", "fix": "889", "desc": "851"}, {"messageId": "852", "fix": "890", "desc": "854"}, {"messageId": "849", "fix": "891", "desc": "851"}, {"messageId": "852", "fix": "892", "desc": "854"}, {"messageId": "849", "fix": "893", "desc": "851"}, {"messageId": "852", "fix": "894", "desc": "854"}, {"messageId": "849", "fix": "895", "desc": "851"}, {"messageId": "852", "fix": "896", "desc": "854"}, {"messageId": "849", "fix": "897", "desc": "851"}, {"messageId": "852", "fix": "898", "desc": "854"}, {"messageId": "849", "fix": "899", "desc": "851"}, {"messageId": "852", "fix": "900", "desc": "854"}, {"messageId": "849", "fix": "901", "desc": "851"}, {"messageId": "852", "fix": "902", "desc": "854"}, {"messageId": "849", "fix": "903", "desc": "851"}, {"messageId": "852", "fix": "904", "desc": "854"}, {"messageId": "849", "fix": "905", "desc": "851"}, {"messageId": "852", "fix": "906", "desc": "854"}, {"messageId": "849", "fix": "907", "desc": "851"}, {"messageId": "852", "fix": "908", "desc": "854"}, {"messageId": "849", "fix": "909", "desc": "851"}, {"messageId": "852", "fix": "910", "desc": "854"}, [7341, 7400], " ", [8329, 8388], [9476, 9535], [14381, 14440], [14894, 14953], [22256, 22315], [24236, 24295], [26199, 26258], [26552, 26611], [27877, 27936], [28804, 28863], [29526, 29585], [33747, 33806], {"messageId": "849", "fix": "911", "desc": "851"}, {"messageId": "852", "fix": "912", "desc": "854"}, {"messageId": "849", "fix": "913", "desc": "851"}, {"messageId": "852", "fix": "914", "desc": "854"}, {"messageId": "849", "fix": "915", "desc": "851"}, {"messageId": "852", "fix": "916", "desc": "854"}, {"messageId": "849", "fix": "917", "desc": "851"}, {"messageId": "852", "fix": "918", "desc": "854"}, {"messageId": "849", "fix": "919", "desc": "851"}, {"messageId": "852", "fix": "920", "desc": "854"}, {"messageId": "849", "fix": "921", "desc": "851"}, {"messageId": "852", "fix": "922", "desc": "854"}, {"messageId": "849", "fix": "923", "desc": "851"}, {"messageId": "852", "fix": "924", "desc": "854"}, {"messageId": "849", "fix": "925", "desc": "851"}, {"messageId": "852", "fix": "926", "desc": "854"}, {"messageId": "849", "fix": "927", "desc": "851"}, {"messageId": "852", "fix": "928", "desc": "854"}, {"messageId": "849", "fix": "929", "desc": "851"}, {"messageId": "852", "fix": "930", "desc": "854"}, {"messageId": "849", "fix": "931", "desc": "851"}, {"messageId": "852", "fix": "932", "desc": "854"}, "suggestUnknown", {"range": "933", "text": "934"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "935", "text": "936"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceEmptyInterfaceWithSuper", {"range": "937", "text": "938"}, "Replace empty interface with a type alias.", "Update the dependencies array to be: [accountData, getChecklistStatus]", {"range": "939", "text": "940"}, "Update the dependencies array to be: [accountData, updateChecklistState, usageData]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [accountData, usageData, isAccountLoading, isUsageLoading, updateChecklistState]", {"range": "943", "text": "944"}, {"range": "945", "text": "946"}, {"range": "947", "text": "934"}, {"range": "948", "text": "936"}, {"range": "949", "text": "934"}, {"range": "950", "text": "936"}, {"range": "951", "text": "934"}, {"range": "952", "text": "936"}, {"range": "953", "text": "934"}, {"range": "954", "text": "936"}, {"range": "955", "text": "934"}, {"range": "956", "text": "936"}, {"range": "957", "text": "934"}, {"range": "958", "text": "936"}, {"range": "959", "text": "934"}, {"range": "960", "text": "936"}, {"range": "961", "text": "934"}, {"range": "962", "text": "936"}, {"range": "963", "text": "934"}, {"range": "964", "text": "936"}, {"range": "965", "text": "934"}, {"range": "966", "text": "936"}, {"range": "967", "text": "934"}, {"range": "968", "text": "936"}, {"range": "969", "text": "934"}, {"range": "970", "text": "936"}, {"range": "971", "text": "934"}, {"range": "972", "text": "936"}, {"range": "973", "text": "934"}, {"range": "974", "text": "936"}, {"range": "975", "text": "934"}, {"range": "976", "text": "936"}, {"range": "977", "text": "934"}, {"range": "978", "text": "936"}, {"range": "979", "text": "934"}, {"range": "980", "text": "936"}, {"range": "981", "text": "934"}, {"range": "982", "text": "936"}, {"range": "983", "text": "934"}, {"range": "984", "text": "936"}, {"range": "985", "text": "934"}, {"range": "986", "text": "936"}, {"range": "987", "text": "934"}, {"range": "988", "text": "936"}, {"range": "989", "text": "934"}, {"range": "990", "text": "936"}, {"range": "991", "text": "934"}, {"range": "992", "text": "936"}, {"range": "993", "text": "934"}, {"range": "994", "text": "936"}, {"range": "995", "text": "934"}, {"range": "996", "text": "936"}, {"range": "997", "text": "934"}, {"range": "998", "text": "936"}, {"range": "999", "text": "934"}, {"range": "1000", "text": "936"}, {"range": "1001", "text": "934"}, {"range": "1002", "text": "936"}, {"range": "1003", "text": "934"}, {"range": "1004", "text": "936"}, {"range": "1005", "text": "934"}, {"range": "1006", "text": "936"}, {"range": "1007", "text": "934"}, {"range": "1008", "text": "936"}, {"range": "1009", "text": "934"}, {"range": "1010", "text": "936"}, {"range": "1011", "text": "934"}, {"range": "1012", "text": "936"}, {"range": "1013", "text": "934"}, {"range": "1014", "text": "936"}, [2945, 2948], "unknown", [2945, 2948], "never", [308, 365], "type BillingPortalButtonProps = ButtonProps", [13677, 13679], "[account<PERSON><PERSON>, getChecklistStatus]", [16255, 16279], "[accountData, updateChecklistState, usageData]", [18377, 18435], "[accountData, usageData, isAccountLoading, isUsageLoading, updateChecklistState]", [2866, 2999], "type StatusIconProps = Omit<SemanticIconProps, 'context'>", [12580, 12583], [12580, 12583], [13546, 13549], [13546, 13549], [1886, 1889], [1886, 1889], [6733, 6736], [6733, 6736], [7437, 7440], [7437, 7440], [7547, 7550], [7547, 7550], [7860, 7863], [7860, 7863], [7988, 7991], [7988, 7991], [8374, 8377], [8374, 8377], [8487, 8490], [8487, 8490], [8831, 8834], [8831, 8834], [8923, 8926], [8923, 8926], [9259, 9262], [9259, 9262], [9749, 9752], [9749, 9752], [10103, 10106], [10103, 10106], [11153, 11156], [11153, 11156], [15405, 15408], [15405, 15408], [24279, 24282], [24279, 24282], [14234, 14237], [14234, 14237], [1948, 1951], [1948, 1951], [1986, 1989], [1986, 1989], [1048, 1051], [1048, 1051], [1822, 1825], [1822, 1825], [1236, 1239], [1236, 1239], [2062, 2065], [2062, 2065], [1278, 1281], [1278, 1281], [849, 852], [849, 852], [3281, 3284], [3281, 3284], [5813, 5816], [5813, 5816], [5862, 5865], [5862, 5865], [699, 702], [699, 702], [1037, 1040], [1037, 1040], [8321, 8324], [8321, 8324], [8346, 8349], [8346, 8349]]
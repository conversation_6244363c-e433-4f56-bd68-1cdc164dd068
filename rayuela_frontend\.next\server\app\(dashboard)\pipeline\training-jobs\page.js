(()=>{var e={};e.id=507,e.ids=[507],e.modules={2844:(e,s,r)=>{Promise.resolve().then(r.bind(r,78578))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5085:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["(dashboard)",{children:["pipeline",{children:["training-jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55435)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/pipeline/training-jobs/page",pathname:"/pipeline/training-jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16412:(e,s,r)=>{Promise.resolve().then(r.bind(r,55435))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55435:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\pipeline\\\\training-jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\training-jobs\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78578:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>J});var t=r(60687),a=r(43210),n=r(44493),i=r(29523),l=r(85726),o=r(6211),d=r(91821),c=r(47033),m=r(78200),x=r(97840),h=r(93613),u=r(80462),p=r(99270),j=r(13861),g=r(13943),b=r(85650),v=r(41585),f=r(85814),N=r.n(f),_=r(89667),y=r(80013),w=r(15079),C=r(63503),E=r(81184),A=r(5336),S=r(84027),k=r(41862);function P({onTrainingStart:e,trigger:s}){let[r,n]=(0,a.useState)(!1),[l,o]=(0,a.useState)("hybrid"),[c,u]=(0,a.useState)(!1),[p,j]=(0,a.useState)({learning_rate:.001,epochs:50,batch_size:32,embedding_dim:64,regularization:.001}),[g,b]=(0,a.useState)(!1),[v,f]=(0,a.useState)(null),[N,E]=(0,a.useState)(!1),P=(e,s)=>{j(r=>({...r,[e]:s}))},F=async()=>{b(!0),f(null);try{let s={model_type:l,force:!1};c&&(s.hyperparameters={learning_rate:p.learning_rate,epochs:p.epochs,batch_size:p.batch_size,embedding_dim:p.embedding_dim,regularization:p.regularization});let r=await e(s);console.log("Training started:",r),E(!0),setTimeout(()=>{n(!1),E(!1),u(!1),o("hybrid")},3e3)}catch(e){f(e instanceof Error?e.message:"Error iniciando entrenamiento")}finally{b(!1)}};return(0,t.jsxs)(C.lG,{open:r,onOpenChange:n,children:[(0,t.jsx)(C.zM,{asChild:!0,children:s||(0,t.jsxs)(i.Button,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),(0,t.jsxs)(C.Cf,{className:"sm:max-w-lg",children:[(0,t.jsxs)(C.c7,{children:[(0,t.jsx)(C.L3,{children:"Nuevo Entrenamiento de Modelo"}),(0,t.jsx)(C.rr,{children:"Inicia el entrenamiento de un modelo de recomendaci\xf3n personalizado con tus datos"})]}),N?(0,t.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Entrenamiento iniciado!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu modelo est\xe1 siendo entrenado"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"modelType",children:"Tipo de modelo"}),(0,t.jsxs)(w.l6,{value:l,onValueChange:e=>o(e),children:[(0,t.jsx)(w.bq,{children:(0,t.jsx)(w.yv,{placeholder:"Selecciona el tipo de modelo"})}),(0,t.jsxs)(w.gC,{children:[(0,t.jsx)(w.eb,{value:"hybrid",children:"H\xedbrido (recomendado)"}),(0,t.jsx)(w.eb,{value:"collaborative",children:"Filtrado colaborativo"}),(0,t.jsx)(w.eb,{value:"content",children:"Basado en contenido"})]})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["hybrid"===l&&"Combina m\xfaltiples t\xe9cnicas para mejores resultados","collaborative"===l&&"Basado en comportamiento de usuarios similares","content"===l&&"Basado en caracter\xedsticas de productos"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"advanced",type:"checkbox",className:"rounded border-gray-300",checked:c,onChange:e=>u(e.target.checked)}),(0,t.jsxs)(y.J,{htmlFor:"advanced",className:"flex items-center",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"Configuraci\xf3n avanzada"]})]}),c&&(0,t.jsxs)("div",{className:"space-y-3 p-4 border rounded-lg bg-muted/50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"learning_rate",children:"Learning Rate"}),(0,t.jsx)(_.p,{id:"learning_rate",type:"number",step:"0.0001",value:p.learning_rate,onChange:e=>P("learning_rate",parseFloat(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"epochs",children:"\xc9pocas"}),(0,t.jsx)(_.p,{id:"epochs",type:"number",value:p.epochs,onChange:e=>P("epochs",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"batch_size",children:"Batch Size"}),(0,t.jsx)(_.p,{id:"batch_size",type:"number",value:p.batch_size,onChange:e=>P("batch_size",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"embedding_dim",children:"Embedding Dim"}),(0,t.jsx)(_.p,{id:"embedding_dim",type:"number",value:p.embedding_dim,onChange:e=>P("embedding_dim",parseInt(e.target.value))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"regularization",children:"Regularizaci\xf3n"}),(0,t.jsx)(_.p,{id:"regularization",type:"number",step:"0.0001",value:p.regularization,onChange:e=>P("regularization",parseFloat(e.target.value))})]})]}),v&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:v})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"El entrenamiento puede tomar varios minutos dependiendo del volumen de datos"}),(0,t.jsx)("p",{children:"Se requiere un m\xednimo de 100 interacciones para entrenar un modelo"})]})]}),(0,t.jsxs)(C.Es,{children:[(0,t.jsx)(i.Button,{variant:"outline",onClick:()=>{g||(n(!1),f(null),E(!1),u(!1),o("hybrid"))},disabled:g,children:"Cancelar"}),!N&&(0,t.jsx)(i.Button,{onClick:F,disabled:g,children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Entrenando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Iniciar Entrenamiento"]})})]})]})]})}var F=r(13668);function J(){let{jobs:e,isLoading:s,error:r,startTraining:f}=function(){let[e,s]=(0,a.useState)([]),[r,t]=(0,a.useState)(!0),[n,i]=(0,a.useState)(null),l=async()=>{try{t(!0),i(null);try{let e=[].map(e=>{let s={id:e.job_id,model_name:e.model?.artifact_name||"Recommendation Model",model_version:e.model?.artifact_version||"v1.0",status:e.status.toUpperCase(),created_at:e.created_at,started_at:e.started_at||void 0,completed_at:e.completed_at||void 0,error_message:e.error_message||void 0,task_id:e.task_id||void 0,parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(([e,s])=>"number"==typeof s||"string"==typeof s)):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(([e,s])=>"number"==typeof s)):void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),r=new Date(s.completed_at).getTime();s.duration=Math.round((r-e)/1e3)}return s});s(e);return}catch(e){console.warn("Listing endpoint not available, falling back to localStorage approach:",e)}let e=localStorage.getItem("trainingJobIds"),r=e?JSON.parse(e):[],a=[];for(let e of r)try{let s=(await (0,E._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data,r={id:s.job_id,model_name:s.model?.artifact_name||"Recommendation Model",model_version:s.model?.artifact_version||"v1.0",status:s.status.toUpperCase(),created_at:s.created_at,started_at:s.started_at||void 0,completed_at:s.completed_at||void 0,error_message:s.error_message||void 0,task_id:s.task_id||void 0,parameters:s.parameters?Object.fromEntries(Object.entries(s.parameters).filter(([e,s])=>"number"==typeof s||"string"==typeof s)):void 0,metrics:s.metrics?Object.fromEntries(Object.entries(s.metrics).filter(([e,s])=>"number"==typeof s)):void 0};if(r.started_at&&r.completed_at){let e=new Date(r.started_at).getTime(),s=new Date(r.completed_at).getTime();r.duration=Math.round((s-e)/1e3)}a.push(r)}catch(s){console.warn(`Error fetching training job ${e}:`,s)}a.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()),s(a)}catch(e){i(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{t(!1)}},o=async e=>{try{let e=await (0,E._C)().trainModelsApiV1PipelineTrainPost(),s=e.data.job_id;if(s){let e=localStorage.getItem("trainingJobIds"),r=e?JSON.parse(e):[];r.unshift(s);let t=r.slice(0,20);localStorage.setItem("trainingJobIds",JSON.stringify(t))}return await l(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return{jobs:e,isLoading:r,error:n,fetchJobs:l,getJobStatus:async e=>{try{return(await (0,E._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},startTraining:o}}(),[A,S]=(0,a.useState)(""),[k,J]=(0,a.useState)("all"),[z,T]=(0,a.useState)(null),I=e.filter(e=>{let s="all"===k||e.status.toLowerCase()===k,r=""===A||e.model_name.toLowerCase().includes(A.toLowerCase())||e.model_version.toLowerCase().includes(A.toLowerCase())||e.id.toString().includes(A);return s&&r}),q=()=>{S(""),J("all")},D=e=>"FAILED"===e.status,M=e=>{console.log("Retrying job:",e)};return s?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,t.jsx)(l.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(l.E,{className:"h-4 w-96"})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(l.E,{className:"h-6 w-48"}),(0,t.jsx)(l.E,{className:"h-4 w-32"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(l.E,{className:"h-64 w-full"})})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)(N(),{href:"/pipeline",className:"text-muted-foreground hover:text-foreground",children:(0,t.jsx)(c.A,{className:"h-5 w-5"})}),(0,t.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-3",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-purple-500"}),"Historial de Entrenamientos"]})]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Seguimiento completo de todos tus trabajos de entrenamiento de modelos"}),(0,t.jsxs)("div",{className:"flex gap-4 mt-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("span",{children:["Total: ",e.length]}),(0,t.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,t.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,t.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})]}),(0,t.jsx)(P,{onTrainingStart:f,trigger:(0,t.jsxs)(i.Button,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})})]})}),r&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Error"}),(0,t.jsx)(d.TN,{children:r})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{className:"border-b border-border/20 bg-muted/20",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5"}),"Filtros"]})}),(0,t.jsxs)(n.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(_.p,{placeholder:"Buscar por modelo, versi\xf3n o ID...",value:A,onChange:e=>S(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(y.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,t.jsxs)(w.l6,{value:k,onValueChange:e=>J(e),children:[(0,t.jsx)(w.bq,{id:"status-filter",className:"w-40",children:(0,t.jsx)(w.yv,{})}),(0,t.jsxs)(w.gC,{children:[(0,t.jsx)(w.eb,{value:"all",children:"Todos"}),(0,t.jsx)(w.eb,{value:"pending",children:"Pendientes"}),(0,t.jsx)(w.eb,{value:"processing",children:"Procesando"}),(0,t.jsx)(w.eb,{value:"completed",children:"Completados"}),(0,t.jsx)(w.eb,{value:"failed",children:"Fallidos"})]})]}),(A||"all"!==k)&&(0,t.jsx)(i.Button,{variant:"outline",size:"sm",onClick:q,children:"Limpiar"})]})]}),(A||"all"!==k)&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",I.length," de ",e.length," trabajos"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"border-b border-border/20 bg-muted/20",children:[(0,t.jsx)(n.ZB,{children:"Trabajos de Entrenamiento"}),(0,t.jsx)(n.BT,{children:"Lista completa de entrenamientos con detalles y m\xe9tricas"})]}),(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{className:"bg-muted/10",children:(0,t.jsxs)(o.Hj,{className:"border-b border-border/30",children:[(0,t.jsx)(o.nd,{className:"font-semibold",children:"Job ID"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Modelo / Versi\xf3n"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Estado"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,t.jsx)(o.nd,{className:"font-semibold",children:"M\xe9tricas"}),(0,t.jsx)(o.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,t.jsx)(o.BF,{children:I.length>0?I.map((e,s)=>(0,t.jsxs)(o.Hj,{className:`
                        border-b border-border/20 
                        hover:bg-muted/30 
                        transition-colors
                        ${s%2==0?"bg-background":"bg-muted/5"}
                      `,children:[(0,t.jsx)(o.nA,{className:"font-medium py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,F.cR)(e.status),"#",e.id]})}),(0,t.jsx)(o.nA,{className:"py-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.model_name}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.model_version})]})}),(0,t.jsx)(o.nA,{className:"py-4",children:(0,F.KC)(e.status)}),(0,t.jsx)(o.nA,{className:"py-4 text-muted-foreground",children:(0,b.GP)(new Date(e.created_at),"d 'de' MMM, yyyy HH:mm",{locale:v.es})}),(0,t.jsx)(o.nA,{className:"py-4 text-muted-foreground",children:e.duration?(0,F.a3)(e.duration):"PROCESSING"===e.status?"⏳ En curso":"—"}),(0,t.jsx)(o.nA,{className:"py-4",children:e.metrics?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Acc: ",(100*e.metrics.accuracy).toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"text-muted-foreground",children:["F1: ",(100*e.metrics.f1_score).toFixed(1),"%"]})]}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,t.jsx)(o.nA,{className:"text-right py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,t.jsxs)(C.lG,{children:[(0,t.jsx)(C.zM,{asChild:!0,children:(0,t.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>T(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(C.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(C.c7,{children:[(0,t.jsxs)(C.L3,{children:["Detalles del Job #",e.id]}),(0,t.jsx)(C.rr,{children:"Informaci\xf3n completa del trabajo de entrenamiento"})]}),z&&(0,t.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Modelo"}),(0,t.jsxs)("p",{className:"text-sm",children:[z.model_name," ",z.model_version]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Estado"}),(0,t.jsx)("div",{className:"mt-1",children:(0,F.KC)(z.status)})]})]}),z.parameters&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Par\xe1metros"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-2 rounded mt-1 overflow-x-auto",children:JSON.stringify(z.parameters,null,2)})]}),z.metrics&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"M\xe9tricas"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-1",children:Object.entries(z.metrics).map(([e,s])=>(0,t.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,t.jsx)("div",{className:"text-xs font-medium",children:e}),(0,t.jsxs)("div",{className:"text-sm",children:[(100*s).toFixed(2),"%"]})]},e))})]}),z.error_message&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,t.jsxs)(d.Fc,{variant:"destructive",className:"mt-1",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{className:"text-sm",children:z.error_message})]})]}),z.task_id&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Task ID"}),(0,t.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:z.task_id})]})]})]})]}),D(e)&&(0,t.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>M(e.id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})})]},e.id)):(0,t.jsx)(o.Hj,{children:(0,t.jsx)(o.nA,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No hay trabajos de entrenamiento a\xfan"}),(0,t.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando inicies entrenamientos"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,t.jsx)(i.Button,{variant:"outline",size:"sm",onClick:q,children:"Limpiar filtros"})]})})})})})]})})})]}),(0,t.jsxs)(d.Fc,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Informaci\xf3n sobre entrenamientos"}),(0,t.jsx)(d.TN,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,t.jsx)("p",{children:"Los trabajos de entrenamiento pueden tomar desde minutos hasta horas dependiendo del tama\xf1o de los datos y la complejidad del modelo."}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Pendiente:"})," El trabajo est\xe1 en cola esperando recursos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Procesando:"})," El entrenamiento est\xe1 en curso"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Completado:"})," El modelo se entren\xf3 exitosamente"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Fallido:"})," Ocurri\xf3 un error durante el entrenamiento"]})]})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,713,814,423,400,576,920,807,320,387],()=>r(5085));module.exports=t})();
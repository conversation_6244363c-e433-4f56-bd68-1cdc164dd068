(()=>{var e={};e.id=670,e.ids=[670],e.modules={2860:(e,r,a)=>{Promise.resolve().then(a.bind(a,64766))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10510:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},21820:e=>{"use strict";e.exports=require("os")},25334:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32192:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},33873:e=>{"use strict";e.exports=require("path")},36677:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=a(65239),s=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(r,l);let d={children:["",{children:["(dashboard)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,64954)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},39308:(e,r,a)=>{Promise.resolve().then(a.bind(a,64954))},40228:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56748:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58559:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58887:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59327:(e,r,a)=>{"use strict";a.d(r,{h:()=>u});var t=a(52581),s=a(51060);class i extends Error{constructor(e,r,a,t){super(e),this.status=r,this.errorCode=a,this.details=t,this.name="ApiError"}static isApiError(e){return e instanceof i}static fromResponse(e){return new i(e.message,e.status_code,e.error_code,e.details)}}let n=s.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>(e.token&&(e.headers.Authorization=`Bearer ${e.token}`),e.apiKey&&!e.url?.endsWith("/auth/token")&&(e.headers["X-API-Key"]=e.apiKey),delete e.token,delete e.apiKey,e)),n.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw i.fromResponse(r)}if(e.request)throw new i("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new i(e.message,0,"REQUEST_ERROR",null)});var o=a(85814),l=a.n(o),d=a(43210),c=a.n(d);function u(e,r="Ha ocurrido un error"){return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof i)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c().createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c().createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(c().createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(c().createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",c().createElement(l(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(c().createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",c().createElement(l(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(c().createElement("div",{},"Error de validacion: "+e.message+". ",c().createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(c().createElement("div",{},"Datos insuficientes para generar recomendaciones. ",c().createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||r):void(t.o.error(r),console.groupEnd())}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64766:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>j});var t=a(60687),s=a(43210),i=a(44493),n=a(29523),o=a(96882),l=a(62688);let d=(0,l.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var c=a(41550),u=a(19959);let p=(0,l.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var m=a(40228);let h=(0,l.A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);var x=a(89667),y=a(80013),f=a(85763),g=a(91821),v=a(85726),b=a(59327),k=a(44957);function j(){let{user:e,token:r,apiKey:a}=(0,k.A)(),[l,j]=(0,s.useState)(!0),[A,N]=(0,s.useState)(null),[E,w]=(0,s.useState)(null);return(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6 text-gray-800 dark:text-white",children:"Settings"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Manage your account settings and preferences."}),(0,t.jsxs)(g.Fc,{variant:"warning",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)(g.XL,{children:"Funcionalidad en desarrollo"}),(0,t.jsxs)(g.TN,{children:[(0,t.jsx)("p",{children:"Estamos trabajando en mejorar esta secci\xf3n. Algunas funcionalidades est\xe1n en desarrollo y estar\xe1n disponibles pr\xf3ximamente."}),(0,t.jsx)("p",{className:"mt-2",children:'Actualmente, solo la pesta\xf1a "Account" est\xe1 parcialmente funcional. Las dem\xe1s pesta\xf1as estar\xe1n disponibles en futuras actualizaciones.'})]})]}),(0,t.jsxs)(f.tU,{defaultValue:"account",className:"w-full",children:[(0,t.jsxs)(f.j7,{className:"mb-6",children:[(0,t.jsx)(f.Xi,{value:"account",children:"Account"}),(0,t.jsx)(f.Xi,{value:"notifications",children:"Notifications"}),(0,t.jsx)(f.Xi,{value:"api",children:"API Settings"})]}),(0,t.jsx)(f.av,{value:"account",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Account Information"}),(0,t.jsx)(i.BT,{children:"Update your account details"})]}),(0,t.jsx)(i.Wu,{className:"space-y-4",children:l?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.E,{className:"h-4 w-20"}),(0,t.jsx)(v.E,{className:"h-10 w-full"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.E,{className:"h-4 w-20"}),(0,t.jsx)(v.E,{className:"h-10 w-full"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.E,{className:"h-4 w-20"}),(0,t.jsx)(v.E,{className:"h-10 w-full"})]})]}):E?(0,t.jsx)("div",{className:"text-red-500 p-4 border border-red-200 rounded-md bg-red-50 dark:bg-red-900/20 dark:border-red-800",children:E}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,t.jsx)(y.J,{htmlFor:"name",children:"Nombre de Usuario"})]}),(0,t.jsx)(x.p,{id:"name",placeholder:"Tu nombre",value:e?.email?.split("@")[0]||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"El nombre de usuario se deriva de tu email."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,t.jsx)(y.J,{htmlFor:"email",children:"Email"})]}),(0,t.jsx)(x.p,{id:"email",type:"email",placeholder:"Tu email",value:e?.email||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Tu email de inicio de sesi\xf3n."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-gray-500 mr-2"}),(0,t.jsx)(y.J,{htmlFor:"company",children:"Nombre de la Cuenta"})]}),(0,t.jsx)(x.p,{id:"company",placeholder:"Tu empresa",value:A?.name||"",disabled:!0,className:"bg-gray-50 dark:bg-gray-800"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"El nombre de tu cuenta o empresa."})]})]})}),(0,t.jsx)(i.wL,{children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.Button,{asChild:!0,variant:"outline",children:(0,t.jsxs)("a",{href:"https://docs.rayuela.ai/account/change-password",target:"_blank",rel:"noopener noreferrer",className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Cambiar Contrase\xf1a"]})}),(0,t.jsx)(n.Button,{variant:"outline",onClick:()=>{try{throw{status:429,error_code:"RESOURCE_LIMIT_EXCEEDED",message:"Has alcanzado el l\xedmite de recursos de tu plan"}}catch(e){(0,b.h)(e,"Error al guardar los cambios")}},className:"text-warning border-warning/30 hover:bg-warning-light hover:text-warning",children:"Simular Error"})]})})]})}),(0,t.jsx)(f.av,{value:"notifications",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Notification Preferences"}),(0,t.jsx)(i.BT,{children:"Configure how you receive notifications"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:[(0,t.jsx)(p,{className:"h-12 w-12 text-gray-300 dark:text-gray-600 mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"Configuraci\xf3n de notificaciones disponible pr\xf3ximamente"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Podr\xe1s configurar notificaciones por email, SMS y webhooks para eventos importantes como entrenamientos completados, l\xedmites de uso cercanos, etc."}),(0,t.jsxs)("div",{className:"mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:[(0,t.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Disponible en Q4 2023"]})]})})]})}),(0,t.jsx)(f.av,{value:"api",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"API Settings"}),(0,t.jsx)(i.BT,{children:"Configure API behavior and defaults"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:[(0,t.jsx)(h,{className:"h-12 w-12 text-gray-300 dark:text-gray-600 mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"Configuraci\xf3n de API disponible pr\xf3ximamente"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Podr\xe1s configurar comportamientos por defecto de la API, webhooks, l\xedmites personalizados, regiones preferidas, etc."}),(0,t.jsxs)("div",{className:"mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",children:[(0,t.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Disponible en Q1 2024"]})]})})]})})]})]})}a(62185)},64954:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\settings\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,r,a)=>{"use strict";a.d(r,{b:()=>o});var t=a(43210),s=a(3416),i=a(60687),n=t.forwardRef((e,r)=>(0,i.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var o=n},78200:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,a)=>{"use strict";a.d(r,{J:()=>n});var t=a(60687);a(43210);var s=a(78148),i=a(4780);function n({className:e,...r}){return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},81630:e=>{"use strict";e.exports=require("http")},82080:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,r,a)=>{"use strict";a.d(r,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>o});var t=a(60687),s=a(43210),i=a(64025),n=a(4780);let o=i.bL,l=s.forwardRef(({className:e,...r},a)=>(0,t.jsx)(i.B8,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...r}));l.displayName=i.B8.displayName;let d=s.forwardRef(({className:e,...r},a)=>(0,t.jsx)(i.l9,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));d.displayName=i.l9.displayName;let c=s.forwardRef(({className:e,...r},a)=>(0,t.jsx)(i.UC,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));c.displayName=i.UC.displayName},85778:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},94735:e=>{"use strict";e.exports=require("events")},96882:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[447,713,814,400,25,807,320],()=>a(36677));module.exports=t})();
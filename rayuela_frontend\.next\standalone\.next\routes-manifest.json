{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/robots.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}], "regex": "^/robots\\.txt(?:/)?$"}, {"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600"}], "regex": "^/sitemap\\.xml(?:/)?$"}], "dynamicRoutes": [{"page": "/verify-email/[token]", "regex": "^/verify\\-email/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtoken": "nxtPtoken"}, "namedRegex": "^/verify\\-email/(?<nxtPtoken>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/api-keys", "regex": "^/api\\-keys(?:/)?$", "routeKeys": {}, "namedRegex": "^/api\\-keys(?:/)?$"}, {"page": "/billing", "regex": "^/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/billing(?:/)?$"}, {"page": "/contact-sales", "regex": "^/contact\\-sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact\\-sales(?:/)?$"}, {"page": "/docs", "regex": "^/docs(?:/)?$", "routeKeys": {}, "namedRegex": "^/docs(?:/)?$"}, {"page": "/docs/quickstart/python", "regex": "^/docs/quickstart/python(?:/)?$", "routeKeys": {}, "namedRegex": "^/docs/quickstart/python(?:/)?$"}, {"page": "/features", "regex": "^/features(?:/)?$", "routeKeys": {}, "namedRegex": "^/features(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/legal/cookies", "regex": "^/legal/cookies(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal/cookies(?:/)?$"}, {"page": "/legal/dpa", "regex": "^/legal/dpa(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal/dpa(?:/)?$"}, {"page": "/legal/notice", "regex": "^/legal/notice(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal/notice(?:/)?$"}, {"page": "/legal/privacy", "regex": "^/legal/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal/privacy(?:/)?$"}, {"page": "/legal/terms", "regex": "^/legal/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal/terms(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/models", "regex": "^/models(?:/)?$", "routeKeys": {}, "namedRegex": "^/models(?:/)?$"}, {"page": "/pipeline", "regex": "^/pipeline(?:/)?$", "routeKeys": {}, "namedRegex": "^/pipeline(?:/)?$"}, {"page": "/pipeline/ingestion-jobs", "regex": "^/pipeline/ingestion\\-jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/pipeline/ingestion\\-jobs(?:/)?$"}, {"page": "/pipeline/training-jobs", "regex": "^/pipeline/training\\-jobs(?:/)?$", "routeKeys": {}, "namedRegex": "^/pipeline/training\\-jobs(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/recommendation-metrics", "regex": "^/recommendation\\-metrics(?:/)?$", "routeKeys": {}, "namedRegex": "^/recommendation\\-metrics(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/usage", "regex": "^/usage(?:/)?$", "routeKeys": {}, "namedRegex": "^/usage(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}
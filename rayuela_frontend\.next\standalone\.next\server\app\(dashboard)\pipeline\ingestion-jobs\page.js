(()=>{var e={};e.id=747,e.ids=[747],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7615:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var r=t(60687),a=t(43210),o=t(44493),i=t(29523),n=t(85726),d=t(6211),l=t(91821),c=t(47033),u=t(61611),m=t(16023),x=t(93613),h=t(80462),p=t(99270),j=t(13861),g=t(31158),f=t(13943),v=t(85650),b=t(41585),N=t(85814),_=t.n(N),y=t(89667),w=t(80013),S=t(15079),A=t(63503),C=t(81184),k=t(5336);let E=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var I=t(41862);function P({onIngestionStart:e,trigger:s}){let[t,o]=(0,a.useState)(!1),[n,d]=(0,a.useState)("batch"),[c,u]=(0,a.useState)(null),[h,p]=(0,a.useState)(!1),[j,g]=(0,a.useState)(null),[f,v]=(0,a.useState)(!1),b=async()=>{if(!c)return void g("Por favor selecciona un archivo");p(!0),g(null);try{let s,t=await N(c);try{s=c.name.toLowerCase().endsWith(".json")?JSON.parse(t):c.name.toLowerCase().endsWith(".csv")?await _(t,n):JSON.parse(t)}catch{throw Error("Error al parsear el archivo. Aseg\xfarate de que el formato sea v\xe1lido.")}let r={data_type:n,file_name:c.name,file_size:c.size,...s},a=await e(r);console.log("Ingestion started:",a),v(!0),setTimeout(()=>{o(!1),v(!1),u(null),d("batch")},2e3)}catch(e){g(e instanceof Error?e.message:"Error iniciando ingesta de datos")}finally{p(!1)}},N=e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>s(e.target?.result),r.onerror=()=>t(Error("Error reading file")),r.readAsText(e)}),_=async(e,s)=>{let t=e.trim().split("\n"),r=t[0].split(",").map(e=>e.trim()),a=[];for(let e=1;e<t.length;e++){let s=t[e].split(",").map(e=>e.trim()),o={};r.forEach((e,t)=>{o[e]=s[t]}),a.push(o)}switch(s){case"users":return{users:a};case"products":return{products:a};case"interactions":return{interactions:a};case"batch":return{users:a.filter(e=>e.external_id&&!e.product_id),products:a.filter(e=>e.external_id&&e.name&&!e.end_user_external_id),interactions:a.filter(e=>e.end_user_external_id&&e.product_external_id)};default:return{data:a}}};return(0,r.jsxs)(A.lG,{open:t,onOpenChange:o,children:[(0,r.jsx)(A.zM,{asChild:!0,children:s||(0,r.jsxs)(i.Button,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),(0,r.jsxs)(A.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(A.c7,{children:[(0,r.jsx)(A.L3,{children:"Nueva Ingesta de Datos"}),(0,r.jsx)(A.rr,{children:"Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones"})]}),f?(0,r.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,r.jsx)(k.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Ingesta iniciada!"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu archivo est\xe1 siendo procesado"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"dataType",children:"Tipo de datos"}),(0,r.jsxs)(S.l6,{value:n,onValueChange:e=>d(e),children:[(0,r.jsx)(S.bq,{children:(0,r.jsx)(S.yv,{placeholder:"Selecciona el tipo de datos"})}),(0,r.jsxs)(S.gC,{children:[(0,r.jsx)(S.eb,{value:"batch",children:"Lote completo (usuarios, productos, interacciones)"}),(0,r.jsx)(S.eb,{value:"users",children:"Solo usuarios"}),(0,r.jsx)(S.eb,{value:"products",children:"Solo productos"}),(0,r.jsx)(S.eb,{value:"interactions",children:"Solo interacciones"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"file",children:"Archivo"}),(0,r.jsx)(y.p,{id:"file",type:"file",accept:".csv,.json,.txt",onChange:e=>{let s=e.target.files?.[0];if(s){let e=s.name.toLowerCase().substr(s.name.lastIndexOf("."));if(!["text/csv","application/json","text/plain"].includes(s.type)&&![".csv",".json",".txt"].includes(e))return void g("Por favor selecciona un archivo CSV o JSON v\xe1lido");if(s.size>0xa00000)return void g("El archivo es demasiado grande. M\xe1ximo 10MB permitido");u(s),g(null)}},disabled:h}),c&&(0,r.jsxs)("div",{className:"mt-2 flex items-center text-sm text-muted-foreground",children:[(0,r.jsx)(E,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[c.name," (",(c.size/1024).toFixed(1)," KB)"]})]})]}),j&&(0,r.jsxs)(l.Fc,{variant:"destructive",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(l.TN,{children:j})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)("p",{children:"Formatos soportados: CSV, JSON"}),(0,r.jsx)("p",{children:"Tama\xf1o m\xe1ximo: 10MB"})]})]}),(0,r.jsxs)(A.Es,{children:[(0,r.jsx)(i.Button,{variant:"outline",onClick:()=>{h||(o(!1),u(null),g(null),v(!1),d("batch"))},disabled:h,children:"Cancelar"}),!f&&(0,r.jsx)(i.Button,{onClick:b,disabled:!c||h,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Subiendo..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Iniciar Ingesta"]})})]})]})]})}var J=t(13668);function L(){let{jobs:e,isLoading:s,error:t,startBatchIngestion:N}=function(){let[e,s]=(0,a.useState)([]),[t,r]=(0,a.useState)(!0),[o,i]=(0,a.useState)(null),n=async()=>{try{r(!0),i(null);try{let e=[].map(e=>{let s={id:e.job_id,status:e.status.toUpperCase(),created_at:e.created_at,started_at:e.started_at||void 0,completed_at:e.completed_at||void 0,error_message:e.error_message||void 0,task_id:e.task_id||void 0,records_processed:e.processed_count?{users:"number"==typeof e.processed_count.users?e.processed_count.users:void 0,products:"number"==typeof e.processed_count.products?e.processed_count.products:void 0,interactions:"number"==typeof e.processed_count.interactions?e.processed_count.interactions:void 0,total:"number"==typeof e.processed_count.total?e.processed_count.total:void 0}:void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),t=new Date(s.completed_at).getTime();s.duration=Math.round((t-e)/1e3)}return s});s(e);return}catch(e){console.warn("Listing endpoint not available, falling back to localStorage approach:",e)}let e=localStorage.getItem("ingestionJobIds"),t=e?JSON.parse(e):[],a=[];for(let e of t)try{let s=(await (0,C._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data,t={id:s.job_id,status:s.status.toUpperCase(),created_at:s.created_at,started_at:s.started_at||void 0,completed_at:s.completed_at||void 0,error_message:s.error_message||void 0,task_id:s.task_id||void 0,records_processed:s.processed_count?{users:"number"==typeof s.processed_count.users?s.processed_count.users:void 0,products:"number"==typeof s.processed_count.products?s.processed_count.products:void 0,interactions:"number"==typeof s.processed_count.interactions?s.processed_count.interactions:void 0,total:"number"==typeof s.processed_count.total?s.processed_count.total:void 0}:void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),s=new Date(t.completed_at).getTime();t.duration=Math.round((s-e)/1e3)}a.push(t)}catch(s){console.warn(`Error fetching job ${e}:`,s)}a.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()),s(a)}catch(e){i(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{r(!1)}},d=async e=>{try{let s=await (0,C._C)().batchDataIngestionApiV1IngestionBatchPost(e),t=s.data.job_id,r=localStorage.getItem("ingestionJobIds"),a=r?JSON.parse(r):[];a.unshift(t);let o=a.slice(0,50);return localStorage.setItem("ingestionJobIds",JSON.stringify(o)),await n(),s.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return{jobs:e,isLoading:t,error:o,fetchJobs:n,getJobStatus:async e=>{try{return(await (0,C._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},startBatchIngestion:d}}(),[k,E]=(0,a.useState)(""),[I,L]=(0,a.useState)("all"),[D,T]=(0,a.useState)(null),q=e.filter(e=>{let s="all"===I||e.status.toLowerCase()===I,t=""===k||e.id.toString().includes(k)||e.file_path&&e.file_path.toLowerCase().includes(k.toLowerCase());return s&&t}),z=e=>(0,J.z3)(e),F=()=>{E(""),L("all")},B=e=>"FAILED"===e.status,M=e=>{console.log("Retrying job:",e)},O=e=>{console.log("Downloading file:",e)};return s?(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,r.jsx)(n.E,{className:"h-8 w-64 mb-2"}),(0,r.jsx)(n.E,{className:"h-4 w-96"})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(n.E,{className:"h-6 w-48"}),(0,r.jsx)(n.E,{className:"h-4 w-32"})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(n.E,{className:"h-64 w-full"})})]})]}):(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,r.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)(_(),{href:"/pipeline",className:"text-muted-foreground hover:text-foreground",children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-3",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-green-500"}),"Historial de Ingesta de Datos"]})]}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Seguimiento completo de todos tus procesos de carga de datos"}),(0,r.jsxs)("div",{className:"flex gap-4 mt-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("span",{children:["Total: ",e.length]}),(0,r.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,r.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,r.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})]}),(0,r.jsx)(P,{onIngestionStart:N,trigger:(0,r.jsxs)(i.Button,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})})]})}),t&&(0,r.jsxs)(l.Fc,{variant:"destructive",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(l.XL,{children:"Error"}),(0,r.jsx)(l.TN,{children:t})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"border-b border-border/20 bg-muted/20",children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Filtros"]})}),(0,r.jsx)(o.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(y.p,{placeholder:"Buscar por ID o archivo...",value:k,onChange:e=>E(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)(w.J,{htmlFor:"statusFilter",className:"text-sm whitespace-nowrap",children:"Estado:"}),(0,r.jsxs)(S.l6,{value:I,onValueChange:e=>L(e),children:[(0,r.jsx)(S.bq,{className:"w-32",children:(0,r.jsx)(S.yv,{})}),(0,r.jsxs)(S.gC,{children:[(0,r.jsx)(S.eb,{value:"all",children:"Todos"}),(0,r.jsx)(S.eb,{value:"pending",children:"Pendiente"}),(0,r.jsx)(S.eb,{value:"processing",children:"Procesando"}),(0,r.jsx)(S.eb,{value:"completed",children:"Completado"}),(0,r.jsx)(S.eb,{value:"failed",children:"Fallido"})]})]})]}),(0,r.jsx)(i.Button,{variant:"outline",size:"sm",onClick:F,children:"Limpiar"})]})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{className:"border-b border-border/20 bg-muted/20",children:[(0,r.jsx)(o.ZB,{children:"Trabajos de Ingesta"}),(0,r.jsx)(o.BT,{children:"Lista completa de procesos de carga de datos con detalles y estad\xedsticas"})]}),(0,r.jsx)(o.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{className:"bg-muted/10",children:(0,r.jsxs)(d.Hj,{className:"border-b border-border/30",children:[(0,r.jsx)(d.nd,{className:"font-semibold",children:"Job ID"}),(0,r.jsx)(d.nd,{className:"font-semibold",children:"Estado"}),(0,r.jsx)(d.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,r.jsx)(d.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,r.jsx)(d.nd,{className:"font-semibold",children:"Registros Procesados"}),(0,r.jsx)(d.nd,{className:"font-semibold",children:"Archivo"}),(0,r.jsx)(d.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,r.jsx)(d.BF,{children:q.length>0?q.map((e,s)=>(0,r.jsxs)(d.Hj,{className:s%2==0?"bg-muted/20":"",children:[(0,r.jsxs)(d.nA,{className:"font-medium py-4",children:["#",e.id]}),(0,r.jsx)(d.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,J.cR)(e.status),(0,J.KC)(e.status)]})}),(0,r.jsx)(d.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:(0,v.GP)(new Date(e.created_at),"dd/MM/yyyy",{locale:b.es})}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,v.GP)(new Date(e.created_at),"HH:mm",{locale:b.es})})]})}),(0,r.jsx)(d.nA,{className:"py-4",children:e.duration?(0,r.jsx)("span",{className:"text-sm font-medium",children:(0,J.a3)(e.duration)}):"PROCESSING"===e.status?(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"En curso"}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(d.nA,{className:"py-4",children:e.records_processed?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium",children:["Total: ",e.records_processed.total?.toLocaleString()||"—"]}),(0,r.jsxs)("div",{className:"text-muted-foreground text-xs",children:[e.records_processed.users&&`${e.records_processed.users.toLocaleString()} usuarios`,e.records_processed.products&&`, ${e.records_processed.products.toLocaleString()} productos`,e.records_processed.interactions&&`, ${e.records_processed.interactions.toLocaleString()} interacciones`]})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(d.nA,{className:"py-4",children:e.file_path?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium truncate max-w-32",title:e.file_path,children:e.file_path.split("/").pop()}),e.file_size&&(0,r.jsx)("div",{className:"text-muted-foreground text-xs",children:z(e.file_size)})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(d.nA,{className:"text-right py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsxs)(A.lG,{children:[(0,r.jsx)(A.zM,{asChild:!0,children:(0,r.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>T(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(A.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(A.c7,{children:[(0,r.jsxs)(A.L3,{children:["Detalles del Job #",e.id]}),(0,r.jsx)(A.rr,{children:"Informaci\xf3n completa del trabajo de ingesta de datos"})]}),D&&(0,r.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Estado"}),(0,r.jsx)("div",{className:"mt-1",children:(0,J.KC)(D.status)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Duraci\xf3n"}),(0,r.jsx)("p",{className:"text-sm",children:D.duration?(0,J.a3)(D.duration):"En curso"})]})]}),D.file_path&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Archivo"}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-xs bg-muted p-2 rounded flex-1",children:D.file_path}),(0,r.jsx)(i.Button,{size:"sm",variant:"outline",onClick:()=>O(D.file_path),disabled:!0,children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]}),D.file_size&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Tama\xf1o: ",z(D.file_size)]})]}),D.records_processed&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Registros Procesados"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[D.records_processed.users&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Usuarios"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:D.records_processed.users.toLocaleString()})]}),D.records_processed.products&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Productos"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:D.records_processed.products.toLocaleString()})]}),D.records_processed.interactions&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Interacciones"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:D.records_processed.interactions.toLocaleString()})]}),D.records_processed.total&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Total"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:D.records_processed.total.toLocaleString()})]})]})]}),D.error_message&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,r.jsxs)(l.Fc,{variant:"destructive",className:"mt-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(l.TN,{className:"text-sm",children:D.error_message})]})]}),D.task_id&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{className:"text-sm font-medium",children:"Task ID"}),(0,r.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:D.task_id})]})]})]})]}),B(e)&&(0,r.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>M(e.id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})})]},e.id)):(0,r.jsx)(d.Hj,{children:(0,r.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No hay trabajos de ingesta a\xfan"}),(0,r.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando subas datos"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,r.jsx)(i.Button,{variant:"outline",size:"sm",onClick:F,children:"Limpiar filtros"})]})})})})})]})})})]}),(0,r.jsxs)(l.Fc,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(l.XL,{children:"Informaci\xf3n sobre ingesta de datos"}),(0,r.jsx)(l.TN,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,r.jsx)("p",{children:"Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar usuarios, productos e interacciones en el sistema."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Formatos soportados:"})," CSV, JSON"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Tipos de datos:"})," Usuarios, Productos, Interacciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Validaci\xf3n:"})," Se valida formato y campos obligatorios"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Procesamiento:"})," Los datos se procesan de forma as\xedncrona"]})]})]})})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51011:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var r=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let l={children:["",{children:["(dashboard)",{children:["pipeline",{children:["ingestion-jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53697)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/pipeline/ingestion-jobs/page",pathname:"/pipeline/ingestion-jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},53697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\pipeline\\\\ingestion-jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67428:(e,s,t)=>{Promise.resolve().then(t.bind(t,7615))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85988:(e,s,t)=>{Promise.resolve().then(t.bind(t,53697))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,713,814,423,400,576,920,807,320,387],()=>t(51011));module.exports=r})();
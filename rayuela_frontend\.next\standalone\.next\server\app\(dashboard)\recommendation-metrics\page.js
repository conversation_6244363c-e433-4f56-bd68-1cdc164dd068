(()=>{var e={};e.id=80,e.ids=[80],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3780:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\recommendation-metrics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx","default")},10510:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12121:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=r(65239),s=r(48088),n=r(88170),i=r.n(n),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(a,d);let l={children:["",{children:["(dashboard)",{children:["recommendation-metrics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3780)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\recommendation-metrics\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/recommendation-metrics/page",pathname:"/recommendation-metrics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12412:e=>{"use strict";e.exports=require("assert")},13338:(e,a,r)=>{Promise.resolve().then(r.bind(r,53699))},15079:(e,a,r)=>{"use strict";r.d(a,{bq:()=>u,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>m});var t=r(60687),s=r(43210),n=r(25957),i=r(78272),o=r(3589),d=r(13964),l=r(4780);let c=n.bL;n.YJ;let m=n.WT,u=s.forwardRef(({className:e,children:a,...r},s)=>(0,t.jsxs)(n.l9,{ref:s,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-xs transition-all hover:border-ring/50 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[a,(0,t.jsx)(n.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50 transition-transform group-data-[state=open]:rotate-180"})})]}));u.displayName=n.l9.displayName;let p=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.PP,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(o.A,{className:"h-4 w-4"})}));p.displayName=n.PP.displayName;let f=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.wn,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));f.displayName=n.wn.displayName;let x=s.forwardRef(({className:e,children:a,position:r="popper",...s},i)=>(0,t.jsx)(n.ZL,{children:(0,t.jsxs)(n.UC,{ref:i,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...s,children:[(0,t.jsx)(p,{}),(0,t.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(f,{})]})}));x.displayName=n.UC.displayName,s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.JU,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=n.JU.displayName;let h=s.forwardRef(({className:e,children:a,...r},s)=>(0,t.jsxs)(n.q7,{ref:s,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent/50 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(n.p4,{children:a})]}));h.displayName=n.q7.displayName,s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=n.wv.displayName},17295:(e,a,r)=>{"use strict";r.d(a,{A:()=>o});var t=r(60687);r(43210);var s=r(16189),n=r(44957),i=r(85726);let o=function({children:e}){let{user:a,isLoading:r}=(0,n.A)();return((0,s.useRouter)(),r)?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(i.E,{className:"h-8 w-[200px]"}),(0,t.jsx)(i.E,{className:"h-[400px] w-full"})]}):a&&a.is_admin?(0,t.jsx)(t.Fragment,{children:e}):null}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},21820:e=>{"use strict";e.exports=require("os")},25334:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32192:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},33873:e=>{"use strict";e.exports=require("path")},41312:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41550:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},45583:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},53699:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>ez});var t=r(60687),s=r(43210),n=r(5077),i=r(44957),o=r(44493),d=r(29523),l=r(15079),c=r(85763),m=r(52581),u=r(59327),p=r(76242),f=r(65668);function x({content:e,side:a="top",align:r="center",className:s="",iconSize:n=16,iconClassName:i=""}){return(0,t.jsx)(p.Bc,{children:(0,t.jsxs)(p.m_,{delayDuration:300,children:[(0,t.jsx)(p.k$,{asChild:!0,children:(0,t.jsx)("span",{className:`inline-flex cursor-help ${i}`,children:(0,t.jsx)(f.A,{size:n,className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"})})}),(0,t.jsx)(p.ZI,{side:a,align:r,className:`max-w-xs p-3 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md ${s}`,children:e})]})})}var h=r(78122),_=r(53411),E=r(62688);let C=(0,E.A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var g=r(96834),N=r(11273),R=r(9510),T=r(98599),v=r(70569),y=r(65551);r(51215);var I=r(8730),b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let r=(0,I.TL)(`Primitive.${a}`),n=s.forwardRef((e,s)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,t.jsx)(n?r:a,{...i,ref:s})});return n.displayName=`Primitive.${a}`,{...e,[a]:n}},{}),j=r(66156),A=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let r=(0,I.TL)(`Primitive.${a}`),n=s.forwardRef((e,s)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,t.jsx)(n?r:a,{...i,ref:s})});return n.displayName=`Primitive.${a}`,{...e,[a]:n}},{}),O=e=>{let{present:a,children:r}=e,t=function(e){var a,r;let[t,n]=s.useState(),i=s.useRef(null),o=s.useRef(e),d=s.useRef("none"),[l,c]=(a=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,a)=>r[e][a]??e,a));return s.useEffect(()=>{let e=M(i.current);d.current="mounted"===l?e:"none"},[l]),(0,j.N)(()=>{let a=i.current,r=o.current;if(r!==e){let t=d.current,s=M(a);e?c("MOUNT"):"none"===s||a?.display==="none"?c("UNMOUNT"):r&&t!==s?c("ANIMATION_OUT"):c("UNMOUNT"),o.current=e}},[e,c]),(0,j.N)(()=>{if(t){let e,a=t.ownerDocument.defaultView??window,r=r=>{let s=M(i.current).includes(r.animationName);if(r.target===t&&s&&(c("ANIMATION_END"),!o.current)){let r=t.style.animationFillMode;t.style.animationFillMode="forwards",e=a.setTimeout(()=>{"forwards"===t.style.animationFillMode&&(t.style.animationFillMode=r)})}},s=e=>{e.target===t&&(d.current=M(i.current))};return t.addEventListener("animationstart",s),t.addEventListener("animationcancel",r),t.addEventListener("animationend",r),()=>{a.clearTimeout(e),t.removeEventListener("animationstart",s),t.removeEventListener("animationcancel",r),t.removeEventListener("animationend",r)}}c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:s.useCallback(e=>{i.current=e?getComputedStyle(e):null,n(e)},[])}}(a),n="function"==typeof r?r({present:t.isPresent}):s.Children.only(r),i=(0,T.s)(t.ref,function(e){let a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=a&&"isReactWarning"in a&&a.isReactWarning;return r?e.ref:(r=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof r||t.isPresent?s.cloneElement(n,{ref:i}):null};function M(e){return e?.animationName||"none"}O.displayName="Presence";var w=r(96963),k="Collapsible",[D,L]=(0,N.A)(k),[P,S]=D(k),z=s.forwardRef((e,a)=>{let{__scopeCollapsible:r,open:n,defaultOpen:i,disabled:o,onOpenChange:d,...l}=e,[c,m]=(0,y.i)({prop:n,defaultProp:i??!1,onChange:d,caller:k});return(0,t.jsx)(P,{scope:r,disabled:o,contentId:(0,w.B)(),open:c,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),children:(0,t.jsx)(A.div,{"data-state":V(c),"data-disabled":o?"":void 0,...l,ref:a})})});z.displayName=k;var Y="CollapsibleTrigger",q=s.forwardRef((e,a)=>{let{__scopeCollapsible:r,...s}=e,n=S(Y,r);return(0,t.jsx)(A.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":V(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...s,ref:a,onClick:(0,v.m)(e.onClick,n.onOpenToggle)})});q.displayName=Y;var F="CollapsibleContent",G=s.forwardRef((e,a)=>{let{forceMount:r,...s}=e,n=S(F,e.__scopeCollapsible);return(0,t.jsx)(O,{present:r||n.open,children:({present:e})=>(0,t.jsx)(U,{...s,ref:a,present:e})})});G.displayName=F;var U=s.forwardRef((e,a)=>{let{__scopeCollapsible:r,present:n,children:i,...o}=e,d=S(F,r),[l,c]=s.useState(n),m=s.useRef(null),u=(0,T.s)(a,m),p=s.useRef(0),f=p.current,x=s.useRef(0),h=x.current,_=d.open||l,E=s.useRef(_),C=s.useRef(void 0);return s.useEffect(()=>{let e=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,j.N)(()=>{let e=m.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let a=e.getBoundingClientRect();p.current=a.height,x.current=a.width,E.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),c(n)}},[d.open,n]),(0,t.jsx)(A.div,{"data-state":V(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!_,...o,ref:u,style:{"--radix-collapsible-content-height":f?`${f}px`:void 0,"--radix-collapsible-content-width":h?`${h}px`:void 0,...e.style},children:_&&i})});function V(e){return e?"open":"closed"}var B=r(43),H="Accordion",Z=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[$,K,W]=(0,R.N)(H),[X,Q]=(0,N.A)(H,[W,L]),J=L(),ee=s.forwardRef((e,a)=>{let{type:r,...s}=e;return(0,t.jsx)($.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,t.jsx)(ei,{...s,ref:a}):(0,t.jsx)(en,{...s,ref:a})})});ee.displayName=H;var[ea,er]=X(H),[et,es]=X(H,{collapsible:!1}),en=s.forwardRef((e,a)=>{let{value:r,defaultValue:n,onValueChange:i=()=>{},collapsible:o=!1,...d}=e,[l,c]=(0,y.i)({prop:r,defaultProp:n??"",onChange:i,caller:H});return(0,t.jsx)(ea,{scope:e.__scopeAccordion,value:s.useMemo(()=>l?[l]:[],[l]),onItemOpen:c,onItemClose:s.useCallback(()=>o&&c(""),[o,c]),children:(0,t.jsx)(et,{scope:e.__scopeAccordion,collapsible:o,children:(0,t.jsx)(el,{...d,ref:a})})})}),ei=s.forwardRef((e,a)=>{let{value:r,defaultValue:n,onValueChange:i=()=>{},...o}=e,[d,l]=(0,y.i)({prop:r,defaultProp:n??[],onChange:i,caller:H}),c=s.useCallback(e=>l((a=[])=>[...a,e]),[l]),m=s.useCallback(e=>l((a=[])=>a.filter(a=>a!==e)),[l]);return(0,t.jsx)(ea,{scope:e.__scopeAccordion,value:d,onItemOpen:c,onItemClose:m,children:(0,t.jsx)(et,{scope:e.__scopeAccordion,collapsible:!0,children:(0,t.jsx)(el,{...o,ref:a})})})}),[eo,ed]=X(H),el=s.forwardRef((e,a)=>{let{__scopeAccordion:r,disabled:n,dir:i,orientation:o="vertical",...d}=e,l=s.useRef(null),c=(0,T.s)(l,a),m=K(r),u="ltr"===(0,B.jH)(i),p=(0,v.m)(e.onKeyDown,e=>{if(!Z.includes(e.key))return;let a=e.target,r=m().filter(e=>!e.ref.current?.disabled),t=r.findIndex(e=>e.ref.current===a),s=r.length;if(-1===t)return;e.preventDefault();let n=t,i=s-1,d=()=>{(n=t+1)>i&&(n=0)},l=()=>{(n=t-1)<0&&(n=i)};switch(e.key){case"Home":n=0;break;case"End":n=i;break;case"ArrowRight":"horizontal"===o&&(u?d():l());break;case"ArrowDown":"vertical"===o&&d();break;case"ArrowLeft":"horizontal"===o&&(u?l():d());break;case"ArrowUp":"vertical"===o&&l()}let c=n%s;r[c].ref.current?.focus()});return(0,t.jsx)(eo,{scope:r,disabled:n,direction:i,orientation:o,children:(0,t.jsx)($.Slot,{scope:r,children:(0,t.jsx)(b.div,{...d,"data-orientation":o,ref:c,onKeyDown:n?void 0:p})})})}),ec="AccordionItem",[em,eu]=X(ec),ep=s.forwardRef((e,a)=>{let{__scopeAccordion:r,value:s,...n}=e,i=ed(ec,r),o=er(ec,r),d=J(r),l=(0,w.B)(),c=s&&o.value.includes(s)||!1,m=i.disabled||e.disabled;return(0,t.jsx)(em,{scope:r,open:c,disabled:m,triggerId:l,children:(0,t.jsx)(z,{"data-orientation":i.orientation,"data-state":eg(c),...d,...n,ref:a,disabled:m,open:c,onOpenChange:e=>{e?o.onItemOpen(s):o.onItemClose(s)}})})});ep.displayName=ec;var ef="AccordionHeader",ex=s.forwardRef((e,a)=>{let{__scopeAccordion:r,...s}=e,n=ed(H,r),i=eu(ef,r);return(0,t.jsx)(b.h3,{"data-orientation":n.orientation,"data-state":eg(i.open),"data-disabled":i.disabled?"":void 0,...s,ref:a})});ex.displayName=ef;var eh="AccordionTrigger",e_=s.forwardRef((e,a)=>{let{__scopeAccordion:r,...s}=e,n=ed(H,r),i=eu(eh,r),o=es(eh,r),d=J(r);return(0,t.jsx)($.ItemSlot,{scope:r,children:(0,t.jsx)(q,{"aria-disabled":i.open&&!o.collapsible||void 0,"data-orientation":n.orientation,id:i.triggerId,...d,...s,ref:a})})});e_.displayName=eh;var eE="AccordionContent",eC=s.forwardRef((e,a)=>{let{__scopeAccordion:r,...s}=e,n=ed(H,r),i=eu(eE,r),o=J(r);return(0,t.jsx)(G,{role:"region","aria-labelledby":i.triggerId,"data-orientation":n.orientation,...o,...s,ref:a,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function eg(e){return e?"open":"closed"}eC.displayName=eE;var eN=r(78272),eR=r(4780);let eT=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(ep,{ref:r,className:(0,eR.cn)("border-b border-border/50 last:border-b-0",e),...a}));eT.displayName="AccordionItem";let ev=s.forwardRef(({className:e,children:a,...r},s)=>(0,t.jsx)(ex,{className:"flex",children:(0,t.jsxs)(e_,{ref:s,className:(0,eR.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:bg-muted/30 hover:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg -mx-2 px-2 [&[data-state=open]>svg]:rotate-180",e),...r,children:[a,(0,t.jsx)(eN.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200 text-muted-foreground"})]})}));ev.displayName="AccordionTrigger";let ey=s.forwardRef(({className:e,children:a,...r},s)=>(0,t.jsx)(eC,{ref:s,className:"overflow-hidden text-sm text-muted-foreground transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,t.jsx)("div",{className:(0,eR.cn)("pb-4 pt-0 px-2",e),children:a})}));ey.displayName="AccordionContent";let eI=(0,E.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]),eb=(0,E.A)("shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]]);var ej=r(45583);r(93613),r(61611),r(41312),r(25541);let eA=(0,E.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var eO=r(5336),eM=r(14952),ew=r(53520);let ek={COMPONENT_TITLE:"Recomendaciones Inteligentes",COMPONENT_DESCRIPTION:"Basadas en el an\xe1lisis de {count} \xe1reas de mejora potencial",LOADING_TEXT:"Analizando m\xe9tricas para generar recomendaciones...",OPTIMIZED_TITLE:"Sistema Optimizado",OPTIMIZED_DESCRIPTION:"No se han detectado \xe1reas de mejora significativas en este momento.",OPTIMIZED_MESSAGE:"Las m\xe9tricas actuales indican que su sistema de recomendaci\xf3n est\xe1 funcionando de manera \xf3ptima. Contin\xfae monitoreando las m\xe9tricas para mantener este rendimiento.",RECOMMENDATION_COUNT:"{count} {count, plural, one {recomendaci\xf3n} other {recomendaciones}}",ACTIONS_TITLE:"Acciones recomendadas:",CATEGORY_ACCURACY:"Precisi\xf3n y Relevancia",CATEGORY_DIVERSITY:"Diversidad y Descubrimiento",CATEGORY_CONFIDENCE:"Confianza y Fiabilidad",CATEGORY_PERFORMANCE:"Rendimiento y Eficiencia",METRIC_PRECISION:"Precisi\xf3n",METRIC_DIVERSITY:"Diversidad",METRIC_CATALOG_COVERAGE:"Cobertura del Cat\xe1logo",METRIC_NOVELTY:"Novedad",METRIC_NDCG:"NDCG",METRIC_SERENDIPITY:"Serendipia",METRIC_AVG_CONFIDENCE:"Confianza Promedio",METRIC_CONFIDENCE_CHANGE:"Cambio en confianza",METRIC_TARGET:"Meta: {value}{unit}",METRIC_MODEL_COLLABORATIVE:"colaborativo",METRIC_MODEL_CONTENT:"basado en contenido",METRIC_MODEL_HYBRID:"h\xedbrido",METRIC_MODEL_CONFIDENCE:"Confianza {model}",METRIC_FACTOR_USER_HISTORY:"tama\xf1o del historial de usuario",METRIC_FACTOR_ITEM_POPULARITY:"popularidad del \xedtem",METRIC_FACTOR_CATEGORY_STRENGTH:"fuerza de categor\xeda",METRIC_FACTOR_MODEL_TYPE:"tipo de modelo",METRIC_FACTOR:"Factor {factor}",METRIC_REC_PRECISION_TITLE:"Mejorar la precisi\xf3n de las recomendaciones",METRIC_REC_DIVERSITY_TITLE:"Aumentar la diversidad de recomendaciones",METRIC_REC_NOVELTY_TITLE:"Incrementar la novedad de las recomendaciones",METRIC_REC_RANKING_TITLE:"Mejorar la calidad del ranking de recomendaciones",METRIC_REC_SERENDIPITY_TITLE:"Aumentar la serendipia en las recomendaciones",METRIC_REC_CONFIDENCE_TITLE:"Aumentar la confianza en las recomendaciones",METRIC_REC_MODEL_TITLE:"Mejorar el rendimiento del modelo {model}",METRIC_REC_CATEGORIES_TITLE:"Mejorar recomendaciones en categor\xedas de baja confianza",METRIC_REC_FACTOR_TITLE:"Mejorar el factor de {factor}",METRIC_REC_TREND_TITLE:"Abordar tendencia negativa en confianza",METRIC_REC_PRECISION_DESC:"La precisi\xf3n actual est\xe1 por debajo del umbral recomendado del 50%. Considere ajustar los modelos para mejorar la relevancia de las recomendaciones.",METRIC_REC_DIVERSITY_DESC:"Las recomendaciones actuales muestran poca diversidad, lo que puede llevar a una experiencia mon\xf3tona para los usuarios.",METRIC_REC_NOVELTY_DESC:"El sistema tiende a recomendar \xedtems muy populares, limitando el descubrimiento de nuevos productos.",METRIC_REC_RANKING_DESC:"El NDCG actual indica que el orden de las recomendaciones podr\xeda no ser \xf3ptimo, afectando la experiencia del usuario.",METRIC_REC_SERENDIPITY_DESC:"Las recomendaciones actuales podr\xedan ser demasiado predecibles, limitando el descubrimiento de \xedtems inesperados pero relevantes.",METRIC_REC_CONFIDENCE_DESC:"El nivel de confianza promedio est\xe1 por debajo del umbral recomendado del 60%, lo que puede indicar incertidumbre en las predicciones.",METRIC_REC_MODEL_DESC:"El modelo {model} muestra un nivel de confianza bajo, lo que reduce la efectividad general del sistema.",METRIC_REC_CATEGORIES_DESC:'Algunas categor\xedas de productos muestran niveles de confianza particularmente bajos, especialmente "{category}".',METRIC_REC_FACTOR_DESC:"El factor de {factor} tiene una contribuci\xf3n baja a la confianza general, lo que indica un \xe1rea de mejora.",METRIC_REC_TREND_DESC:"La confianza promedio ha disminuido significativamente en los \xfaltimos d\xedas, lo que podr\xeda indicar un problema emergente.",METRIC_REC_PRECISION_ACTION_1:"Ajustar los par\xe1metros del modelo colaborativo para dar m\xe1s peso a interacciones recientes",METRIC_REC_PRECISION_ACTION_2:"Aumentar el tama\xf1o del conjunto de entrenamiento con m\xe1s datos de interacciones",METRIC_REC_PRECISION_ACTION_3:"Implementar t\xe9cnicas de filtrado para eliminar outliers en los datos de entrenamiento",METRIC_REC_DIVERSITY_ACTION_1:"Implementar un algoritmo de re-ranking para diversificar los resultados",METRIC_REC_DIVERSITY_ACTION_2:"Ajustar los par\xe1metros del modelo para reducir la concentraci\xf3n en \xedtems populares",METRIC_REC_DIVERSITY_ACTION_3:"Introducir un factor de aleatoriedad controlada en las recomendaciones finales",METRIC_REC_DIVERSITY_ACTION_4:"Considerar categor\xedas menos representadas en las recomendaciones",METRIC_REC_NOVELTY_ACTION_1:"Ajustar el algoritmo para dar m\xe1s peso a \xedtems menos populares",METRIC_REC_NOVELTY_ACTION_2:"Implementar un factor de penalizaci\xf3n para \xedtems extremadamente populares",METRIC_REC_NOVELTY_ACTION_3:'Crear un segmento espec\xedfico de "descubrimientos" con \xedtems de baja popularidad pero alta relevancia',METRIC_REC_NOVELTY_ACTION_4:"Considerar t\xe9cnicas de filtrado colaborativo basadas en vecindad para encontrar \xedtems nicho",METRIC_REC_RANKING_ACTION_1:"Implementar o mejorar algoritmos de Learning-to-Rank",METRIC_REC_RANKING_ACTION_2:"Ajustar los factores de relevancia en el c\xe1lculo del ranking",METRIC_REC_RANKING_ACTION_3:"Considerar se\xf1ales adicionales como recencia o tendencia para el ranking",METRIC_REC_RANKING_ACTION_4:"Experimentar con diferentes funciones de p\xe9rdida optimizadas para NDCG",METRIC_REC_SERENDIPITY_ACTION_1:"Implementar un componente de serendipia que ocasionalmente introduzca \xedtems inesperados",METRIC_REC_SERENDIPITY_ACTION_2:"Explorar conexiones no obvias entre preferencias de usuario y productos",METRIC_REC_SERENDIPITY_ACTION_3:"Considerar t\xe9cnicas de recomendaci\xf3n basadas en conocimiento para descubrir relaciones no evidentes",METRIC_REC_SERENDIPITY_ACTION_4:"Experimentar con modelos de grafos para encontrar conexiones de segundo o tercer grado",METRIC_REC_CONFIDENCE_ACTION_1:"Recopilar m\xe1s datos de interacciones para mejorar la base de predicciones",METRIC_REC_CONFIDENCE_ACTION_2:"Ajustar los umbrales de confianza para filtrar recomendaciones de baja calidad",METRIC_REC_CONFIDENCE_ACTION_3:"Implementar t\xe9cnicas de ensemble para combinar m\xfaltiples modelos",METRIC_REC_CONFIDENCE_ACTION_4:"Mejorar la calidad de los metadatos de productos para fortalecer el modelo basado en contenido",METRIC_REC_MODEL_COLLAB_ACTION_1:"Ajustar los par\xe1metros de similitud entre usuarios/\xedtems",METRIC_REC_MODEL_COLLAB_ACTION_2:"Implementar t\xe9cnicas de factorizaci\xf3n matricial m\xe1s avanzadas",METRIC_REC_MODEL_COLLAB_ACTION_3:"Aumentar el n\xfamero de vecinos considerados en el algoritmo KNN",METRIC_REC_MODEL_COLLAB_ACTION_4:"Reducir el umbral de filtrado para interacciones m\xednimas",METRIC_REC_MODEL_CONTENT_ACTION_1:"Mejorar la calidad y cantidad de atributos de los productos",METRIC_REC_MODEL_CONTENT_ACTION_2:"Implementar t\xe9cnicas de procesamiento de lenguaje natural m\xe1s avanzadas",METRIC_REC_MODEL_CONTENT_ACTION_3:"Ajustar los pesos de los diferentes atributos en el c\xe1lculo de similitud",METRIC_REC_MODEL_CONTENT_ACTION_4:"Considerar la incorporaci\xf3n de embeddings pre-entrenados para representar productos",METRIC_REC_MODEL_HYBRID_ACTION_1:"Ajustar los pesos relativos de los modelos colaborativo y basado en contenido",METRIC_REC_MODEL_HYBRID_ACTION_2:"Implementar un meta-modelo para seleccionar din\xe1micamente el mejor enfoque",METRIC_REC_MODEL_HYBRID_ACTION_3:"Considerar factores contextuales adicionales en la combinaci\xf3n de modelos",METRIC_REC_MODEL_HYBRID_ACTION_4:"Experimentar con diferentes estrategias de ensemble",METRIC_REC_CATEGORIES_ACTION_1:'Recopilar m\xe1s datos de interacciones para la categor\xeda "{category}"',METRIC_REC_CATEGORIES_ACTION_2:"Crear modelos espec\xedficos por categor\xeda para las categor\xedas problem\xe1ticas",METRIC_REC_CATEGORIES_ACTION_3:"Mejorar los metadatos de productos en estas categor\xedas",METRIC_REC_CATEGORIES_ACTION_4:"Considerar reglas de negocio espec\xedficas para complementar el algoritmo",METRIC_REC_FACTOR_USER_HISTORY_ACTION_1:"Implementar estrategias para aumentar la recopilaci\xf3n de interacciones de usuarios",METRIC_REC_FACTOR_USER_HISTORY_ACTION_2:"Mejorar el onboarding para capturar preferencias iniciales",METRIC_REC_FACTOR_USER_HISTORY_ACTION_3:"Considerar t\xe9cnicas de cold-start para usuarios con poco historial",METRIC_REC_FACTOR_USER_HISTORY_ACTION_4:"Implementar recomendaciones basadas en sesi\xf3n para usuarios nuevos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1:"Balancear mejor las recomendaciones entre \xedtems populares y de nicho",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2:"Implementar un sistema de boosting temporal para nuevos productos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3:"Crear segmentos de recomendaci\xf3n espec\xedficos para diferentes niveles de popularidad",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4:"Mejorar la estrategia de exploraci\xf3n vs. explotaci\xf3n",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1:"Mejorar la taxonom\xeda de categor\xedas para capturar mejor las preferencias",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2:"Implementar an\xe1lisis de afinidad de categor\xeda m\xe1s sofisticado",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3:"Considerar la jerarqu\xeda completa de categor\xedas en el c\xe1lculo de afinidad",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4:"Crear modelos espec\xedficos para categor\xedas principales",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1:"Experimentar con diferentes arquitecturas de modelos",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2:"Implementar un sistema de selecci\xf3n din\xe1mica de modelos basado en contexto",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3:"Considerar modelos m\xe1s avanzados como deep learning para recomendaciones",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4:"Mejorar la estrategia de ensemble para combinar modelos",METRIC_REC_TREND_ACTION_1:"Investigar cambios recientes en datos o modelos que podr\xedan haber afectado la confianza",METRIC_REC_TREND_ACTION_2:"Verificar la calidad de las interacciones recientes",METRIC_REC_TREND_ACTION_3:"Considerar un rollback a una versi\xf3n anterior del modelo si la tendencia persiste",METRIC_REC_TREND_ACTION_4:"Implementar monitoreo en tiempo real para detectar cambios abruptos en m\xe9tricas clave"};function eD(e,a){return e.replace(/{([^}]+)}/g,(e,r)=>{let t=r.match(/^([^,]+),\s*plural,\s*one\s*{([^}]+)}\s*other\s*{([^}]+)}$/);if(t){let e=t[1].trim(),r=t[2].trim(),s=t[3].trim();return 1===a[e]?r:s}return void 0!==a[r]?a[r]:e})}function eL({performanceData:e,confidenceData:a,isLoading:r=!1}){let[n,i]=(0,s.useState)([]),[d,l]=(0,s.useState)(void 0),c=n.reduce((e,a)=>(e[a.category]||(e[a.category]=[]),e[a.category].push(a),e),{}),m={accuracy:{name:ek.CATEGORY_ACCURACY,icon:(0,t.jsx)(ew.mm,{icon:eI,size:"md",context:"metric"})},diversity:{name:ek.CATEGORY_DIVERSITY,icon:(0,t.jsx)(ew.mm,{icon:eb,size:"md",context:"metric"})},confidence:{name:ek.CATEGORY_CONFIDENCE,icon:(0,t.jsx)(ew.mm,{icon:eO.A,size:"md",context:"metric"})},performance:{name:ek.CATEGORY_PERFORMANCE,icon:(0,t.jsx)(ew.mm,{icon:ej.A,size:"md",context:"metric"})}};return r?(0,t.jsxs)(o.Zp,{className:"w-full",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ew.mm,{icon:eA,size:"md",context:"warning"}),ek.COMPONENT_TITLE]}),(0,t.jsx)(o.BT,{children:ek.LOADING_TEXT})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"})]},e))})})]}):e&&a&&0!==n.length?(0,t.jsxs)(o.Zp,{className:"w-full",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ew.mm,{icon:eA,size:"md",context:"warning"}),ek.COMPONENT_TITLE]}),(0,t.jsx)(o.BT,{children:eD(ek.COMPONENT_DESCRIPTION,{count:Object.values(c).flat().length})})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)(ee,{type:"single",collapsible:!0,value:d,onValueChange:l,className:"space-y-4",children:Object.entries(c).map(([e,a])=>(0,t.jsxs)(eT,{value:e,className:"border rounded-lg overflow-hidden",children:[(0,t.jsx)(ev,{className:"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-left",children:[m[e]?.icon,(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:m[e]?.name||e}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:eD(ek.RECOMMENDATION_COUNT,{count:a.length})})]})]})}),(0,t.jsx)(ey,{className:"px-0",children:(0,t.jsx)("div",{className:"space-y-4 pt-2",children:a.map(e=>(0,t.jsx)("div",{className:"px-4 py-3 border-t border-gray-100 dark:border-gray-800",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"mt-1",children:e.icon}),(0,t.jsxs)("div",{className:"space-y-2 w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)(g.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",className:"capitalize",children:e.priority})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 my-3",children:e.metrics.map((e,a)=>(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:[(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"font-medium",children:[e.value.toFixed(1),e.unit]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:eD(ek.METRIC_TARGET,{value:e.target,unit:e.unit})})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1",children:(0,t.jsx)("div",{className:`h-1.5 rounded-full ${e.value>=e.target?"bg-green-500":e.value>=.8*e.target?"bg-amber-500":"bg-red-500"}`,style:{width:`${Math.min(100,e.value/e.target*100)}%`}})})]},a))}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"text-sm font-medium mb-2",children:ek.ACTIONS_TITLE}),(0,t.jsx)("ul",{className:"space-y-1",children:e.actions.map((e,a)=>(0,t.jsxs)("li",{className:"text-sm flex items-start gap-2",children:[(0,t.jsx)(ew.mm,{icon:eM.A,size:"sm",context:"muted",className:"mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:e})]},a))})]})]})]})},e.id))})})]},e))})})]}):(0,t.jsxs)(o.Zp,{className:"w-full",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ew.mm,{icon:eO.A,size:"md",context:"success"}),ek.OPTIMIZED_TITLE]}),(0,t.jsx)(o.BT,{children:ek.OPTIMIZED_DESCRIPTION})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("p",{className:"text-muted-foreground",children:ek.OPTIMIZED_MESSAGE})})]})}var eP=r(55695),eS=r(17295);function ez(){let{token:e,apiKey:a}=(0,i.A)(),[r,p]=(0,s.useState)(!1),[f,E]=(0,s.useState)("performance"),[g,N]=(0,s.useState)("all"),[R,T]=(0,s.useState)("all"),{data:v,error:y,isLoading:I,mutate:b}=(0,n.Ay)(e&&a?["recommendation-performance",g,R]:null,async([,e,a])=>await (0,eP.S)("all"!==e?parseInt(e):void 0,"all"!==a?a:void 0)),{data:j,error:A,isLoading:O,mutate:M}=(0,n.Ay)(e&&a?"confidence-metrics":null,async()=>await (0,eP.K)()),w=async()=>{p(!0);try{await Promise.all([b(),M()]),m.o.success("M\xe9tricas actualizadas")}catch(e){(0,u.h)(e,"Error al actualizar las m\xe9tricas")}finally{p(!1)}},k=[];if(v&&"object"==typeof v){let e=v.offline_metrics;e?.models&&Array.isArray(e.models)&&e.models.forEach(e=>{k.push({id:e.model_id?.toString()||e.id?.toString()||Math.random().toString(),name:`${e.model_type||e.type||"Model"} (${e.version||"v1"})`})})}return(0,t.jsx)(eS.A,{children:(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"M\xe9tricas de Recomendaci\xf3n"}),(0,t.jsx)(x,{content:(0,t.jsxs)("div",{className:"space-y-3 max-w-md",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Acerca de las M\xe9tricas de Recomendaci\xf3n"}),(0,t.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:"Esta p\xe1gina muestra m\xe9tricas detalladas sobre el rendimiento y la confianza de los modelos de recomendaci\xf3n."}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"M\xe9tricas de Rendimiento:"}),(0,t.jsxs)("ul",{className:"list-disc pl-4 text-gray-700 dark:text-gray-300",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Precisi\xf3n y Recall:"})," Miden la relevancia de las recomendaciones"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"NDCG y MAP:"})," Eval\xfaan la calidad del ranking de recomendaciones"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Diversidad y Novedad:"})," Miden la variedad y originalidad de las recomendaciones"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Serendipia:"})," Eval\xfaa la capacidad de sorprender positivamente al usuario"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"M\xe9tricas de Confianza:"}),(0,t.jsxs)("ul",{className:"list-disc pl-4 text-gray-700 dark:text-gray-300",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Distribuci\xf3n de Confianza:"})," Niveles de confianza por tipo de modelo"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Confianza por Categor\xeda:"})," Qu\xe9 categor\xedas generan recomendaciones m\xe1s confiables"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Factores de Confianza:"})," Qu\xe9 aspectos influyen m\xe1s en la confianza"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Tendencias:"})," Evoluci\xf3n de la confianza a lo largo del tiempo"]})]})]}),(0,t.jsx)("p",{className:"text-gray-700 dark:text-gray-300 italic",children:"Pase el cursor sobre cualquier m\xe9trica para obtener informaci\xf3n detallada sobre su significado y c\xe1lculo."})]}),iconSize:18,side:"right",align:"start",iconClassName:"mt-2"})]})}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"An\xe1lisis detallado del rendimiento y confianza de los modelos de recomendaci\xf3n"})]}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsxs)(d.Button,{variant:"outline",onClick:w,disabled:r||I||O,className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:`h-4 w-4 ${r?"animate-spin":""}`}),"Actualizar"]})})]}),v&&j&&(0,t.jsx)(eL,{performanceData:v,confidenceData:j,isLoading:I||O}),(0,t.jsx)(c.tU,{value:f,onValueChange:E,className:"w-full",children:(0,t.jsxs)(c.j7,{className:"grid grid-cols-2 w-full max-w-md",children:[(0,t.jsxs)(c.Xi,{value:"performance",className:"flex items-center gap-2",children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),"Rendimiento"]}),(0,t.jsxs)(c.Xi,{value:"confidence",className:"flex items-center gap-2",children:[(0,t.jsx)(C,{className:"h-4 w-4"}),"Confianza"]})]})}),"performance"===f&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsxs)("div",{className:"w-full md:w-auto",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Modelo"}),(0,t.jsxs)(l.l6,{value:g,onValueChange:N,children:[(0,t.jsx)(l.bq,{className:"w-full md:w-[200px]",children:(0,t.jsx)(l.yv,{placeholder:"Todos los modelos"})}),(0,t.jsxs)(l.gC,{children:[(0,t.jsx)(l.eb,{value:"all",children:"Todos los modelos"}),k.map(e=>(0,t.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,t.jsxs)("div",{className:"w-full md:w-auto",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Tipo de M\xe9trica"}),(0,t.jsxs)(l.l6,{value:R,onValueChange:T,children:[(0,t.jsx)(l.bq,{className:"w-full md:w-[200px]",children:(0,t.jsx)(l.yv,{placeholder:"Todas las m\xe9tricas"})}),(0,t.jsxs)(l.gC,{children:[(0,t.jsx)(l.eb,{value:"all",children:"Todas las m\xe9tricas"}),[{id:"precision",name:"Precisi\xf3n"},{id:"recall",name:"Recall"},{id:"ndcg",name:"NDCG"},{id:"map",name:"MAP"},{id:"catalog_coverage",name:"Cobertura del Cat\xe1logo"},{id:"diversity",name:"Diversidad"},{id:"novelty",name:"Novedad"},{id:"serendipity",name:"Serendipia"}].map(e=>(0,t.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})]}),v&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Precisi\xf3n"}),(0,t.jsx)(x,{content:(0,t.jsx)("div",{children:"Porcentaje de recomendaciones relevantes del total de recomendaciones mostradas"}),iconSize:14})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof v&&v){let e=v.offline_metrics,a=e?.precision;return a?`${(100*a).toFixed(1)}%`:"--"}return"--"})()})})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"NDCG"}),(0,t.jsx)(x,{content:(0,t.jsx)("div",{children:"Normalized Discounted Cumulative Gain - eval\xfaa la calidad del ranking"}),iconSize:14})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof v&&v){let e=v.offline_metrics,a=e?.ndcg;return a?`${(100*a).toFixed(1)}%`:"--"}return"--"})()})})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Diversidad"}),(0,t.jsx)(x,{content:(0,t.jsx)("div",{children:"Variedad en las recomendaciones mostradas"}),iconSize:14})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof v&&v){let e=v.offline_metrics,a=e?.diversity;return a?`${(100*a).toFixed(1)}%`:"--"}return"--"})()})})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Cobertura"}),(0,t.jsx)(x,{content:(0,t.jsx)("div",{children:"Porcentaje del cat\xe1logo que se est\xe1 recomendando"}),iconSize:14})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof v&&v){let e=v.offline_metrics,a=e?.catalog_coverage;return a?`${(100*a).toFixed(1)}%`:"--"}return"--"})()})})]})]}),I&&(0,t.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),y&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de rendimiento"})})]}),"confidence"===f&&(0,t.jsxs)("div",{className:"space-y-6",children:[j&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{children:"Distribuci\xf3n de Confianza"})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:"object"==typeof j&&j?Object.entries(j.confidence_distribution||{}).map(([e,a])=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"capitalize",children:e}),(0,t.jsx)("span",{className:"font-medium",children:a.avg?`${(100*a.avg).toFixed(1)}%`:"N/A"})]},e)):(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Datos de confianza no disponibles"})})})})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{children:"Confianza por Categor\xeda"})}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:"object"==typeof j&&j?Object.entries(j.category_confidence||{}).slice(0,5).map(([e,a])=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"capitalize",children:e}),(0,t.jsx)("span",{className:"font-medium",children:"number"==typeof a?`${(100*a).toFixed(1)}%`:"N/A"})]},e)):(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Datos por categor\xeda no disponibles"})})})})]})]}),O&&(0,t.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),A&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de confianza"})})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55695:(e,a,r)=>{"use strict";r.d(a,{K:()=>o,S:()=>i});var t=r(81184),s=r(62185);let n=(0,t._C)();async function i(e,a){try{let r={};return e&&(r.model_id=e),a&&(r.metric_type=a),await n.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(r)}catch(t){let e=t instanceof Error?t.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",a=t.status||500,r=t.body;throw new s.hD(e,a,r)}}async function o(){try{return await n.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(t){let e=t instanceof Error?t.message:"Error al obtener m\xe9tricas de confianza",a=t.status||500,r=t.body;throw new s.hD(e,a,r)}}},56748:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58559:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58887:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59327:(e,a,r)=>{"use strict";r.d(a,{h:()=>m});var t=r(52581),s=r(51060);class n extends Error{constructor(e,a,r,t){super(e),this.status=a,this.errorCode=r,this.details=t,this.name="ApiError"}static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}}let i=s.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});i.interceptors.request.use(e=>(e.token&&(e.headers.Authorization=`Bearer ${e.token}`),e.apiKey&&!e.url?.endsWith("/auth/token")&&(e.headers["X-API-Key"]=e.apiKey),delete e.token,delete e.apiKey,e)),i.interceptors.response.use(e=>e,e=>{if(e.response){let a=e.response.data;throw n.fromResponse(a)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var o=r(85814),d=r.n(o),l=r(43210),c=r.n(l);function m(e,a="Ha ocurrido un error"){return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c().createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",c().createElement(d(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(c().createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",c().createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(c().createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",c().createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(c().createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",c().createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(c().createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",c().createElement(d(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(c().createElement("div",{},"Error de validacion: "+e.message+". ",c().createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(c().createElement("div",{},"Datos insuficientes para generar recomendaciones. ",c().createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||a),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||a):void(t.o.error(a),console.groupEnd())}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76242:(e,a,r)=>{"use strict";r.d(a,{Bc:()=>i,ZI:()=>l,k$:()=>d,m_:()=>o});var t=r(60687);r(43210);var s=r(46442),n=r(4780);function i({delayDuration:e=0,...a}){return(0,t.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function o({...e}){return(0,t.jsx)(i,{children:(0,t.jsx)(s.bL,{"data-slot":"tooltip",...e})})}function d({...e}){return(0,t.jsx)(s.l9,{"data-slot":"tooltip-trigger",...e})}function l({className:e,sideOffset:a=0,children:r,...i}){return(0,t.jsx)(s.ZL,{children:(0,t.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...i,children:[r,(0,t.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78200:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82080:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,a,r)=>{"use strict";r.d(a,{Xi:()=>l,av:()=>c,j7:()=>d,tU:()=>o});var t=r(60687),s=r(43210),n=r(64025),i=r(4780);let o=n.bL,d=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.B8,{ref:r,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...a}));d.displayName=n.B8.displayName;let l=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.l9,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));l.displayName=n.l9.displayName;let c=s.forwardRef(({className:e,...a},r)=>(0,t.jsx)(n.UC,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=n.UC.displayName},85778:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89778:(e,a,r)=>{Promise.resolve().then(r.bind(r,3780))},94735:e=>{"use strict";e.exports=require("events")},96834:(e,a,r)=>{"use strict";r.d(a,{E:()=>o});var t=r(60687);r(43210);var s=r(24224),n=r(4780);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,...r}){return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:a}),e),...r})}}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[447,713,814,400,576,920,77,25,340,807,320],()=>r(12121));module.exports=t})();
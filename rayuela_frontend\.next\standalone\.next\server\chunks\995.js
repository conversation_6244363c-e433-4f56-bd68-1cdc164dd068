"use strict";exports.id=995,exports.ids=[995],exports.modules={36571:(e,t,r)=>{r.d(t,{T4:()=>l,As:()=>a.A,A$:()=>s,Xn:()=>d,TB:()=>c});var a=r(44957),n=r(5077),i=r(62185);function s(){let{token:e,apiKey:t}=(0,a.A)(),{data:r,error:s,isLoading:o,mutate:l}=(0,n.Ay)(e&&t?["plans",e,t]:null,async()=>await (0,i.T9)(),{refreshInterval:3e5,revalidateOnFocus:!1});return{plans:r||{},error:s,isLoading:o,refresh:l,getPlanLimits:e=>{if(!r)return null;let t=r[e];return t?.limits||null},getPlanById:e=>r&&r[e]||null,getAllPlans:()=>r?Object.values(r):[],getPlanName:e=>{if(!r)return e;let t=r[e];return t?.name||e}}}var o=r(81184);function l(e={}){let{token:t,apiKey:r}=(0,a.A)(),{data:s,error:c,isLoading:u,isValidating:d,mutate:g}=(0,n.Ay)(t&&r?["account-info",t,r]:null,async()=>await (0,i.Dm)(),{revalidateOnFocus:e.revalidateOnFocus??!1,refreshInterval:e.refreshInterval,dedupingInterval:e.dedupingInterval??6e4,errorRetryCount:e.errorRetryCount??3}),p=async e=>{if(!t||!r)throw Error("No token or API key available");try{let t=(await (0,o._C)().patchCurrentAccountApiV1AccountsCurrentPatch({onboardingChecklistStatus:e})).data;return await g(t,{revalidate:!1}),t}catch(r){console.error("Error updating checklist status:",r);let t=s?{...s,onboardingChecklistStatus:e}:void 0;throw await g(t,{revalidate:!1}),r}};return{accountData:s,error:c,isLoading:u,isValidating:d,refresh:g,lastUpdated:null,getCreationDate:()=>s&&s.createdAt?new Date(s.createdAt):null,isActive:()=>!!s&&s.isActive,getSubscriptionPlan:()=>s&&s.subscription?s.subscription.plan:null,isSubscriptionActive:()=>!!s&&!!s.subscription&&s.subscription.isActive,getSubscriptionExpiryDate:()=>s&&s.subscription&&s.subscription.expiresAt?new Date(s.subscription.expiresAt):null,getChecklistStatus:()=>s&&s.onboardingChecklistStatus?s.onboardingChecklistStatus:{},updateChecklistStatus:p}}function c(){let{token:e,apiKey:t}=(0,a.A)(),{data:r,error:s,isLoading:o,mutate:l}=(0,n.Ay)(e&&t?["usage-summary",e,t]:null,async()=>await (0,i.S3)(),{refreshInterval:3e4,revalidateOnFocus:!0}),c=()=>r&&r.apiCalls?.percentage||0,u=()=>r&&r.storage?.percentage||0;return{usageData:r,error:s,isLoading:o,mutate:l,getApiCallsUsed:()=>r?.apiCalls?.used||0,getApiCallsLimit:()=>r?.apiCalls?.limit||0,getApiCallsRemaining:()=>{let e=r?.apiCalls?.used||0;return Math.max(0,(r?.apiCalls?.limit||0)-e)},getStorageUsed:()=>r?.storage?.usedBytes||0,getStorageLimit:()=>r?.storage?.limitBytes||0,getStorageRemaining:()=>{let e=r?.storage?.usedBytes||0;return Math.max(0,(r?.storage?.limitBytes||0)-e)},hasUsageActivity:()=>(r?.apiCalls?.used||0)>0,getApiCallsPercentage:c,getStoragePercentage:u,canTrainNow:()=>!!r&&(r.training?.canTrainNow||!1),getNextTrainingDate:()=>r&&r.training?.nextAvailable?new Date(r.training.nextAvailable):null,getLastStorageMeasurement:()=>r&&r.storage?.lastMeasured||null,getNextApiCallsReset:()=>r&&r.apiCalls?.resetDate?new Date(r.apiCalls.resetDate):null,isApiCallsLimitReached:()=>!!r&&c()>=100,isStorageLimitReached:()=>!!r&&u()>=100,getStorageUsedFormatted:()=>{if(!r)return"0 B";let e=r.storage?.usedBytes||0;if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]},getStorageLimitFormatted:()=>{if(!r)return"0 B";let e=r.storage?.limitBytes||0,t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB","TB"][t]},getApiCallsUsedFormatted:()=>r?(r.apiCalls?.used||0).toLocaleString():"0",getApiCallsLimitFormatted:()=>r?(r.apiCalls?.limit||0).toLocaleString():"0",getAvailableModels:()=>r&&r.planLimits?.availableModels||[],getMaxRequestsPerMinute:()=>r&&r.planLimits?.maxRequestsPerMinute||0}}function u(e){return!!e&&"object"==typeof e&&"string"==typeof e.date&&"number"==typeof e.api_calls&&"number"==typeof e.storage}function d(e,t){let{token:r,apiKey:s}=(0,a.A)(),o=e?.toISOString().split("T")[0],l=t?.toISOString().split("T")[0],{data:c,error:d,isLoading:g,mutate:p}=(0,n.Ay)(r&&s&&o&&l?["usage-history",r,s,o,l]:null,async()=>await (0,i.M2)(),{refreshInterval:6e4,revalidateOnFocus:!0,onError:e=>{e instanceof i.hD?console.error("Error fetching usage history:",e.message,e.body):console.error("Unexpected error:",e)}});return{data:c,error:d,isLoading:g,mutate:p,getTotalApiCalls:()=>c?c.reduce((e,t)=>u(t)?e+t.api_calls:e,0):0,getPeakUsageDay:()=>{if(!c||0===c.length)return null;let e=c.filter(u);if(0===e.length)return null;let t=e.reduce((e,t)=>t.api_calls>e.api_calls?t:e,e[0]);return{date:new Date(t.date),apiCalls:t.api_calls}},getLatestStorageUsage:()=>{if(!c||0===c.length)return 0;let e=c.filter(u);return 0===e.length?0:e.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())[0].storage},getChartData:()=>c?c.filter(u).sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()).map(e=>({date:new Date(e.date),apiCalls:e.api_calls,storage:e.storage})):[],getGrowthRate:e=>{if(!c||c.length<2)return 0;let t=c.filter(u);if(t.length<2)return 0;let r=t.sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),a="apiCalls"===e?r[0].api_calls:r[0].storage,n="apiCalls"===e?r[r.length-1].api_calls:r[r.length-1].storage;return 0===a?100*(n>0):(n-a)/a*100}}}r(56184),r(55695),r(43210)},43125:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},55695:(e,t,r)=>{r.d(t,{K:()=>o,S:()=>s});var a=r(81184),n=r(62185);let i=(0,a._C)();async function s(e,t){try{let r={};return e&&(r.model_id=e),t&&(r.metric_type=t),await i.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(r)}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}async function o(){try{return await i.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de confianza",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}},56184:(e,t,r)=>{r.d(t,{Q:()=>s});var a=r(5077),n=r(62185),i=r(43210);function s(e={}){let[t,r]=(0,i.useState)(!1),[o,l]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!1),[d,g]=(0,i.useState)(!1),[p,h]=(0,i.useState)(null),{data:f,error:y,isLoading:m,isValidating:b,mutate:v}=(0,a.Ay)("api-keys",async()=>await (0,n.PX)(),{revalidateOnFocus:e.revalidateOnFocus??!0,refreshInterval:e.refreshInterval,dedupingInterval:e.dedupingInterval??6e4,errorRetryCount:e.errorRetryCount??3,onError:e=>{console.error("Error fetching API keys:",e)}}),w=f?.api_keys&&f.api_keys.length>0?f.api_keys.find(e=>e.is_active)||f.api_keys[0]:null,A=async e=>{r(!0),h(null);try{let t={name:e.name||"",permissions:[]},r=await (0,n.Iq)(t);return await v(),r}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al crear API Key",500);throw h(e),e}finally{r(!1)}},x=async(e,t)=>{l(!0),h(null);try{let r={name:t.name||void 0,permissions:[]},a=await (0,n.XW)(e.toString(),r);return await v(),a}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al actualizar API Key",500);throw h(e),e}finally{l(!1)}},k=async e=>{u(!0),h(null);try{return await (0,n.mA)(e),await v(),!0}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al revocar API Key",500);throw h(e),e}finally{u(!1)}},C=async()=>{g(!0),h(null);try{let e=await (0,n.Iq)({name:`API Key ${new Date().toLocaleDateString("es-ES")}`});return await v(),e}catch(e){return h(e instanceof n.hD?e:new n.hD("Error al regenerar API Key",500)),null}finally{g(!1)}};return{data:f??null,primaryKey:w??null,error:y??null,isLoading:m,isValidating:b,mutate:v,dataUpdatedAt:0,createApiKey:A,updateApiKey:x,revokeApiKey:k,regenerateApiKey:C,isCreating:t,isUpdating:o,isRevoking:c,isRegenerating:d,operationError:p,getFormattedApiKey:e=>{let t=e||w;return t?.prefix&&t?.last_chars?`${t.prefix}••••••••${t.last_chars}`:null}}}},94652:(e,t,r)=>{r.d(t,{q:()=>d});var a=r(60687),n=r(43210),i=r(29523),s=r(44957),o=r(62185),l=r(52581),c=r(43125),u=r(25334);function d({children:e,className:t,variant:r="outline",...d}){let{token:g,apiKey:p}=(0,s.A)(),[h,f]=(0,n.useState)(!1),y=async()=>{if(!g||!p)return void l.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");f(!0);try{let e=await (0,o.oE)();if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n del Portal de Facturaci\xf3n:",e),l.o.error(e.message||"Error al acceder al portal de facturaci\xf3n"),f(!1)}};return(0,a.jsx)(i.Button,{onClick:y,disabled:h,className:t,variant:r,...d,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Redirigiendo..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),e||"Gestionar Facturaci\xf3n"]})})}},96834:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(60687);r(43210);var n=r(24224),i=r(4780);let s=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{className:(0,i.cn)(s({variant:t}),e),...r})}},96882:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97930:(e,t,r)=>{r.d(t,{s:()=>d});var a=r(60687),n=r(43210),i=r(29523),s=r(44957),o=r(62185),l=r(52581),c=r(43125),u=r(85778);function d({priceId:e,planName:t,actionType:r="subscribe",children:d,className:g,variant:p="default",...h}){let{token:f,apiKey:y}=(0,s.A)(),[m,b]=(0,n.useState)(!1),v=async()=>{if(!f||!y)return void l.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");b(!0);try{if("contact"===r){window.location.href="/contact-sales";return}let t=await (0,o.fw)(e);if(t.url)window.location.href=t.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n de checkout:",e),l.o.error(e.message||`Error al procesar la suscripci\xf3n al plan ${t}`),b(!1)}};return(0,a.jsx)(i.Button,{onClick:v,disabled:m,className:g,variant:p,...h,children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Procesando..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),d||("upgrade"===r?`Actualizar a ${t}`:"downgrade"===r?`Cambiar a ${t}`:"contact"===r?"Contactar con Ventas":`Suscribirse a ${t}`)]})})}}};
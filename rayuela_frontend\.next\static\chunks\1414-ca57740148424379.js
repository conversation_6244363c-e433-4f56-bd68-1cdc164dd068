"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1414],{6176:(e,t,n)=>{n.d(t,{UC:()=>X,B8:()=>J,bL:()=>q,l9:()=>Q});var r=n(2115),o=n(5185),a=n(6081),i=n(7328),u=n(6101),l=n(1285);n(7650);var s=n(9708),d=n(5155),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,s.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),f=n(9033),m=n(5845),v=n(4315),p="rovingFocusGroup.onEntryFocus",w={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[y,g,h]=(0,i.N)(b),[N,T]=(0,a.A)(b,[h]),[I,R]=N(b),A=r.forwardRef((e,t)=>(0,d.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(x,{...e,ref:t})})}));A.displayName=b;var x=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:s,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:h,onEntryFocus:N,preventScrollOnEntryFocus:T=!1,...R}=e,A=r.useRef(null),x=(0,u.s)(t,A),E=(0,v.jH)(l),[D,F]=(0,m.i)({prop:s,defaultProp:null!=y?y:null,onChange:h,caller:b}),[M,j]=r.useState(!1),O=(0,f.c)(N),L=g(n),P=r.useRef(!1),[S,U]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,d.jsx)(I,{scope:n,orientation:a,dir:E,loop:i,currentTabStopId:D,onItemFocus:r.useCallback(e=>F(e),[F]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>U(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>U(e=>e-1),[]),children:(0,d.jsx)(c.div,{tabIndex:M||0===S?-1:0,"data-orientation":a,...R,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,w);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),T)}}P.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),E="RovingFocusGroupItem",D=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,children:s,...f}=e,m=(0,l.B)(),v=u||m,p=R(E,n),w=p.currentTabStopId===v,b=g(n),{onFocusableItemAdd:h,onFocusableItemRemove:N}=p;return r.useEffect(()=>{if(a)return h(),()=>N()},[a,h,N]),(0,d.jsx)(y.ItemSlot,{scope:n,id:v,focusable:a,active:i,children:(0,d.jsx)(c.span,{tabIndex:w?0:-1,"data-orientation":p.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?p.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return F[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>C(n))}}),children:"function"==typeof s?s({isCurrentTabStop:w}):s})})});D.displayName=E;var F={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var M=n(8905),j=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,s.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),O="Tabs",[L,P]=(0,a.A)(O,[T]),S=T(),[U,_]=L(O),k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:s="automatic",...c}=e,f=(0,v.jH)(u),[p,w]=(0,m.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:O});return(0,d.jsx)(U,{scope:n,baseId:(0,l.B)(),value:p,onValueChange:w,orientation:i,dir:f,activationMode:s,children:(0,d.jsx)(j.div,{dir:f,"data-orientation":i,...c,ref:t})})});k.displayName=O;var K="TabsList",B=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=_(K,n),i=S(n);return(0,d.jsx)(A,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,d.jsx)(j.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});B.displayName=K;var G="TabsTrigger",V=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...i}=e,u=_(G,n),l=S(n),s=H(u.baseId,r),c=z(u.baseId,r),f=r===u.value;return(0,d.jsx)(D,{asChild:!0,...l,focusable:!a,active:f,children:(0,d.jsx)(j.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":c,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:s,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||a||!e||u.onValueChange(r)})})})});V.displayName=G;var W="TabsContent",$=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...u}=e,l=_(W,n),s=H(l.baseId,o),c=z(l.baseId,o),f=o===l.value,m=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(M.C,{present:a||f,children:n=>{let{present:r}=n;return(0,d.jsx)(j.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":s,hidden:!r,id:c,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})}})});function H(e,t){return"".concat(e,"-trigger-").concat(t)}function z(e,t){return"".concat(e,"-content-").concat(t)}$.displayName=W;var q=k,J=B,Q=V,X=$},8905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(6101),a=n(2712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),s=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=d.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1833],{6126:(e,r,a)=>{a.d(r,{E:()=>i});var t=a(5155);a(2115);var n=a(2085),o=a(9434);let s=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:a,...n}=e;return(0,t.jsx)("div",{className:(0,o.cn)(s({variant:a}),r),...n})}},6695:(e,r,a)=>{a.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>s,wL:()=>c});var t=a(5155);a(2115);var n=a(9434);function o(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-sm","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",r),...a})}function s(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function i(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("text-subheading rayuela-accent",r),...a})}function d(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-caption",r),...a})}function l(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...a})}function c(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",r),...a})}},7588:(e,r,a)=>{a.d(r,{h:()=>c});var t=a(6671),n=a(3464);class o extends Error{static isApiError(e){return e instanceof o}static fromResponse(e){return new o(e.message,e.status_code,e.error_code,e.details)}constructor(e,r,a,t){super(e),this.status=r,this.errorCode=a,this.details=t,this.name="ApiError"}}let s=n.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{var r;return e.token&&(e.headers.Authorization="Bearer ".concat(e.token)),!e.apiKey||(null==(r=e.url)?void 0:r.endsWith("/auth/token"))||(e.headers["X-API-Key"]=e.apiKey),delete e.token,delete e.apiKey,e}),s.interceptors.response.use(e=>e,e=>{if(e.response){let r=e.response.data;throw o.fromResponse(r)}if(e.request)throw new o("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new o(e.message,0,"REQUEST_ERROR",null)});var i=a(6874),d=a.n(i),l=a(2115);function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof o)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(l.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",l.createElement(d(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(l.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",l.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(l.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",l.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(l.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",l.createElement(d(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(l.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",l.createElement(d(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(l.createElement("div",{},"Error de validacion: "+e.message+". ",l.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(l.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",l.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||r),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||r):void(t.o.error(r),console.groupEnd())}},8534:(e,r,a)=>{a.d(r,{mm:()=>i,vK:()=>l});var t=a(5155);a(2115);var n=a(9434);let o={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},s={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function i(e){let{icon:r,size:a="md",context:i="neutral",className:d,"aria-label":l,"aria-hidden":c=!l,...u}=e;return(0,t.jsx)(r,{className:(0,n.cn)(o[a],s[i],"shrink-0",d),"aria-label":l,"aria-hidden":c,...u})}let d={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function l(e){let{icon:r,children:a,size:o="sm",context:s="neutral",iconPosition:l="left",spacing:c="normal",className:u}=e,m=(0,t.jsx)(i,{icon:r,size:o,context:s,"aria-hidden":!0});return(0,t.jsxs)("span",{className:(0,n.cn)("inline-flex items-center",d[c],u),children:["left"===l&&m,a,"right"===l&&m]})}},8856:(e,r,a)=>{a.d(r,{E:()=>o});var t=a(5155),n=a(9434);function o(e){let{className:r,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",r),...a})}},9409:(e,r,a)=>{a.d(r,{bq:()=>m,eb:()=>h,gC:()=>p,l6:()=>c,yv:()=>u});var t=a(5155),n=a(2115),o=a(4140),s=a(6474),i=a(7863),d=a(5196),l=a(9434);let c=o.bL;o.YJ;let u=o.WT,m=n.forwardRef((e,r)=>{let{className:a,children:n,...i}=e;return(0,t.jsxs)(o.l9,{ref:r,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-xs transition-all hover:border-ring/50 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[n,(0,t.jsx)(o.In,{asChild:!0,children:(0,t.jsx)(s.A,{className:"h-4 w-4 opacity-50 transition-transform group-data-[state=open]:rotate-180"})})]})});m.displayName=o.l9.displayName;let f=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(o.PP,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});f.displayName=o.PP.displayName;let g=n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(o.wn,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,t.jsx)(s.A,{className:"h-4 w-4"})})});g.displayName=o.wn.displayName;let p=n.forwardRef((e,r)=>{let{className:a,children:n,position:s="popper",...i}=e;return(0,t.jsx)(o.ZL,{children:(0,t.jsxs)(o.UC,{ref:r,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-lg border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:s,...i,children:[(0,t.jsx)(f,{}),(0,t.jsx)(o.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,t.jsx)(g,{})]})})});p.displayName=o.UC.displayName,n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(o.JU,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...n})}).displayName=o.JU.displayName;let h=n.forwardRef((e,r)=>{let{className:a,children:n,...s}=e;return(0,t.jsxs)(o.q7,{ref:r,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-md py-1.5 pl-8 pr-2 text-sm outline-none transition-colors hover:bg-accent/50 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(o.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(o.p4,{children:n})]})});h.displayName=o.q7.displayName,n.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(o.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",a),...n})}).displayName=o.wv.displayName}}]);
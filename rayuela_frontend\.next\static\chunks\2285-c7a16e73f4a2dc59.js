"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2285],{646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3109:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3815:(e,t,n)=>{n.d(t,{i3:()=>eo,UC:()=>er,ZL:()=>en,Kq:()=>Q,bL:()=>ee,l9:()=>et});var r,o=n(2115),l=n(5185),i=n(6101),a=n(6081),s=n(7650),u=n(9708),c=n(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?n:t,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),p=n(9033),v=n(1595),f="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,a;let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:u,onPointerDownOutside:y,onFocusOutside:x,onInteractOutside:g,onDismiss:w,...E}=e,C=o.useContext(h),[k,T]=o.useState(null),L=null!=(a=null==k?void 0:k.ownerDocument)?a:null==(n=globalThis)?void 0:n.document,[,P]=o.useState({}),R=(0,i.s)(t,e=>T(e)),D=Array.from(C.layers),[M]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),j=D.indexOf(M),A=k?D.indexOf(k):-1,O=C.layersWithOutsidePointerEventsDisabled.size>0,_=A>=j,N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));_&&!n&&(null==y||y(e),null==g||g(e),e.defaultPrevented||null==w||w())},L),B=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==x||x(e),null==g||g(e),e.defaultPrevented||null==w||w())},L);return(0,v.U)(e=>{A===C.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},L),o.useEffect(()=>{if(k)return s&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(k)),C.layers.add(k),m(),()=>{s&&1===C.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[k,L,s,C]),o.useEffect(()=>()=>{k&&(C.layers.delete(k),C.layersWithOutsidePointerEventsDisabled.delete(k),m())},[k,C]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,c.jsx)(d.div,{...E,ref:R,style:{pointerEvents:O?_?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,B.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,B.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,N.onPointerDownCapture)})});function m(){let e=new CustomEvent(f);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:o}=r,l=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&l.addEventListener(e,t,{once:!0}),o)l&&s.flushSync(()=>l.dispatchEvent(i));else l.dispatchEvent(i)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(h),r=o.useRef(null),l=(0,i.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(d.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var x=n(1285),g=n(5773),w=n(2712),E=o.forwardRef((e,t)=>{var n,r;let{container:l,...i}=e,[a,u]=o.useState(!1);(0,w.N)(()=>u(!0),[]);let p=l||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return p?s.createPortal((0,c.jsx)(d.div,{...i,ref:t}),p):null});E.displayName="Portal";var C=n(8905),k=n(5845),T=n(3601),[L,P]=(0,a.A)("Tooltip",[g.Bk]),R=(0,g.Bk)(),D="TooltipProvider",M="tooltip.open",[j,A]=L(D),O=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:l=!1,children:i}=e,a=o.useRef(!0),s=o.useRef(!1),u=o.useRef(0);return o.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,c.jsx)(j,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,r)},[r]),isPointerInTransitRef:s,onPointerInTransitChange:o.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};O.displayName=D;var _="Tooltip",[N,B]=L(_),S=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:s}=e,u=A(_,e.__scopeTooltip),d=R(t),[p,v]=o.useState(null),f=(0,x.B)(),h=o.useRef(0),y=null!=a?a:u.disableHoverableContent,m=null!=s?s:u.delayDuration,b=o.useRef(!1),[w,E]=(0,k.i)({prop:r,defaultProp:null!=l&&l,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(M))):u.onClose(),null==i||i(e)},caller:_}),C=o.useMemo(()=>w?b.current?"delayed-open":"instant-open":"closed",[w]),T=o.useCallback(()=>{window.clearTimeout(h.current),h.current=0,b.current=!1,E(!0)},[E]),L=o.useCallback(()=>{window.clearTimeout(h.current),h.current=0,E(!1)},[E]),P=o.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{b.current=!0,E(!0),h.current=0},m)},[m,E]);return o.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),(0,c.jsx)(g.bL,{...d,children:(0,c.jsx)(N,{scope:t,contentId:f,open:w,stateAttribute:C,trigger:p,onTriggerChange:v,onTriggerEnter:o.useCallback(()=>{u.isOpenDelayedRef.current?P():T()},[u.isOpenDelayedRef,P,T]),onTriggerLeave:o.useCallback(()=>{y?L():(window.clearTimeout(h.current),h.current=0)},[L,y]),onOpen:T,onClose:L,disableHoverableContent:y,children:n})})};S.displayName=_;var z="TooltipTrigger",F=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,a=B(z,n),s=A(z,n),u=R(n),p=o.useRef(null),v=(0,i.s)(t,p,a.onTriggerChange),f=o.useRef(!1),h=o.useRef(!1),y=o.useCallback(()=>f.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,c.jsx)(g.Mz,{asChild:!0,...u,children:(0,c.jsx)(d.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...r,ref:v,onPointerMove:(0,l.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,l.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,l.m)(e.onPointerDown,()=>{a.open&&a.onClose(),f.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,l.m)(e.onFocus,()=>{f.current||a.onOpen()}),onBlur:(0,l.m)(e.onBlur,a.onClose),onClick:(0,l.m)(e.onClick,a.onClose)})})});F.displayName=z;var I="TooltipPortal",[W,H]=L(I,{forceMount:void 0}),V=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,l=B(I,t);return(0,c.jsx)(W,{scope:t,forceMount:n,children:(0,c.jsx)(C.C,{present:n||l.open,children:(0,c.jsx)(E,{asChild:!0,container:o,children:r})})})};V.displayName=I;var q="TooltipContent",U=o.forwardRef((e,t)=>{let n=H(q,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...l}=e,i=B(q,e.__scopeTooltip);return(0,c.jsx)(C.C,{present:r||i.open,children:i.disableHoverableContent?(0,c.jsx)(Z,{side:o,...l,ref:t}):(0,c.jsx)(X,{side:o,...l,ref:t})})}),X=o.forwardRef((e,t)=>{let n=B(q,e.__scopeTooltip),r=A(q,e.__scopeTooltip),l=o.useRef(null),a=(0,i.s)(t,l),[s,u]=o.useState(null),{trigger:d,onClose:p}=n,v=l.current,{onPointerInTransitChange:f}=r,h=o.useCallback(()=>{u(null),f(!1)},[f]),y=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(n,r,o,l)){case l:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return o.useEffect(()=>()=>h(),[h]),o.useEffect(()=>{if(d&&v){let e=e=>y(e,v),t=e=>y(e,d);return d.addEventListener("pointerleave",e),v.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),v.removeEventListener("pointerleave",t)}}},[d,v,y,h]),o.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==d?void 0:d.contains(t))||(null==v?void 0:v.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,s);r?h():o&&(h(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[d,v,s,p,h]),(0,c.jsx)(Z,{...e,ref:a})}),[Y,$]=L(_,{isInside:!1}),K=(0,u.Dc)("TooltipContent"),Z=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:a,...s}=e,u=B(q,n),d=R(n),{onClose:p}=u;return o.useEffect(()=>(document.addEventListener(M,p),()=>document.removeEventListener(M,p)),[p]),o.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(u.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,p]),(0,c.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,c.jsxs)(g.UC,{"data-state":u.stateAttribute,...d,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,c.jsx)(K,{children:r}),(0,c.jsx)(Y,{scope:n,isInside:!0,children:(0,c.jsx)(T.bL,{id:u.contentId,role:"tooltip",children:l||r})})]})})});U.displayName=q;var G="TooltipArrow",J=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=R(n);return $(G,n).isInside?null:(0,c.jsx)(g.i3,{...o,...r,ref:t})});J.displayName=G;var Q=O,ee=S,et=F,en=V,er=U,eo=J},3904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3843],{3601:(e,t,n)=>{n.d(t,{bL:()=>u,Qg:()=>a});var r=n(2115);n(7650);var i=n(9708),o=n(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?n:t,{...l,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),f=r.forwardRef((e,t)=>(0,o.jsx)(l.span,{...e,ref:t,style:{...a,...e.style}}));f.displayName="VisuallyHidden";var u=f},5339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5773:(e,t,n)=>{n.d(t,{Mz:()=>eK,i3:()=>e0,UC:()=>eZ,bL:()=>eJ,Bk:()=>eN});var r=n(2115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>s[e])}function x(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function v(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function R(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),u=p(t),c="y"===l,s=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(u){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:s}=R(u,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:x}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(u=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:c,y:s}=R(u,d,f)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:s="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=v(h),g=a[p?"floating"===s?"reference":"floating":s],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:f})),w="floating"===s?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),R=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return i.some(t=>e[t]>=0)}async function E(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),u=["left","top"].includes(l)?-1:1,c=o&&f?-1:1,s=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof s?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function S(){return"undefined"!=typeof window}function C(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!S()&&(e instanceof Node||e instanceof O(e).Node)}function j(e){return!!S()&&(e instanceof Element||e instanceof O(e).Element)}function D(e){return!!S()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function N(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function M(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=B(),n=j(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(C(e))}function z(e){return O(e).getComputedStyle(e)}function $(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===C(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||k(e);return N(t)?t.host:t}function I(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=_(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&F(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=O(i);if(o){let e=X(l);return t.concat(l,l.visualViewport||[],F(i)?i:[],e&&n?I(e):[])}return t.concat(i,I(i,[],n))}function X(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Y(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=D(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function q(e){return j(e)?e:e.contextElement}function Q(e){let t=q(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=Y(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let U=u(0);function G(e){let t=O(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function J(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=q(e),a=u(1);t&&(r?j(r)&&(a=Q(r)):a=Q(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===O(l))&&i)?G(l):u(0),c=(o.left+f.x)/a.x,s=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=O(l),t=r&&j(r)?O(r):r,n=e,i=X(n);for(;i&&r&&t!==n;){let e=Q(i),t=i.getBoundingClientRect(),r=z(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,p*=e.y,c+=o,s+=l,i=X(n=O(i))}}return b({width:d,height:p,x:c,y:s})}function K(e,t){let n=$(e).scrollLeft;return t?t.left+n:J(k(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=k(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=B();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=$(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),f=-n.scrollTop;return"rtl"===z(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(e));else if(j(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=D(e)?Q(e):u(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=G(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!D(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=O(e);if(M(e))return n;if(!D(e)){let t=_(e);for(;t&&!V(t);){if(j(t)&&!et(t))return t;t=_(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(C(r))&&et(r);)r=en(r,t);return r&&V(r)&&et(r)&&!W(r)?n:r||function(e){let t=_(e);for(;D(t)&&!V(t);){if(W(t))return t;if(M(t))break;t=_(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),i=k(t),o="fixed"===n,l=J(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=u(0);if(r||!r&&!o)if(("body"!==C(t)||F(i))&&(a=$(t)),r){let e=J(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=K(i));let c=!i||r||o?u(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=k(r),a=!!t&&M(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=D(r);if((d||!d&&!o)&&(("body"!==C(r)||F(l))&&(f=$(r)),D(r))){let e=J(r);c=Q(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let p=!l||d||o?u(0):Z(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-f.scrollTop*c.y+s.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?M(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=I(e,[],!1).filter(e=>j(e)&&"body"!==C(e)),i=null,o="fixed"===z(e).position,l=o?_(e):e;for(;j(l)&&!V(l);){let t=z(l),n=W(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||F(l)&&!n&&function e(t,n){let r=_(t);return!(r===n||!j(r)||V(r))&&("fixed"===z(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=_(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=Y(e);return{width:t,height:n}},getScale:Q,isElement:j,isRTL:function(e){return"rtl"===z(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:u,middlewareData:c}=t,{element:s,padding:p=0}=d(e,t)||{};if(null==s)return{};let w=v(p),x={x:n,y:r},b=m(y(i)),R=g(b),A=await f.getDimensions(s),L="y"===b,T=L?"clientHeight":"clientWidth",P=a.reference[R]+a.reference[b]-x[b]-a.floating[R],E=x[b]-a.reference[b],S=await (null==f.getOffsetParent?void 0:f.getOffsetParent(s)),C=S?S[T]:0;C&&await (null==f.isElement?void 0:f.isElement(S))||(C=u.floating[T]||a.floating[R]);let O=C/2-A[R]/2-1,k=o(w[L?"top":"left"],O),H=o(w[L?"bottom":"right"],O),j=C-A[R]-H,D=C/2-A[R]/2+(P/2-E/2),N=l(k,o(D,j)),F=!c.arrow&&null!=h(i)&&D!==N&&a.reference[R]/2-(D<k?k:H)-A[R]/2<0,M=F?D<k?D-k:D-j:0;return{[b]:x[b]+M,data:{[b]:N,centerOffset:D-N-M,...F&&{alignmentOffset:M}},reset:F}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return A(e,t,{...i,platform:o})};var eu=n(7650),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function es(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!es(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!es(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await E(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),s={x:n,y:r},h=await L(t,c),g=y(p(i)),w=m(g),x=s[w],v=s[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}let b=u.fn({...t,[w]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},s=y(i),h=m(s),g=c[h],w=c[s],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+v.mainAxis,n=o.reference[h]+o.reference[e]-v.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,R;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[s]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[s])||0)+(t?0:v.crossAxis),r=o.reference[s]+o.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[s])||0)-(t?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[s]:w}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:u,initialPlacement:c,platform:s,elements:v}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:E=!0,...S}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let C=p(a),O=y(c),k=p(c)===c,H=await (null==s.isRTL?void 0:s.isRTL(v.floating)),j=A||(k||!E?[x(c)]:function(e){let t=x(e);return[w(e),t,w(t)]}(c)),D="none"!==P;!A&&D&&j.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(c,E,P,H));let N=[c,...j],F=await L(t,S),M=[],W=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&M.push(F[C]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=x(l)),[l,x(l)]}(a,u,H);M.push(F[e[0]],F[e[1]])}if(W=[...W,{placement:a,overflows:M}],!M.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=N[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(o=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=W.filter(e=>{if(D){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:u,platform:c,elements:s}=t,{apply:m=()=>{},...g}=d(e,t),w=await L(t,g),x=p(f),v=h(f),b="y"===y(f),{width:R,height:A}=u.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let T=A-w.top-w.bottom,P=R-w.left-w.right,E=o(A-w[i],T),S=o(R-w[a],P),C=!t.middlewareData.shift,O=E,k=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=P),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=T),C&&!v){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?k=R-2*(0!==e||0!==t?e+t:l(w.left,w.right)):O=A-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:k,availableHeight:O});let H=await c.getDimensions(s.floating);return R!==H.width||A!==H.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=T(await L(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=T(await L(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}),eR=(e,t)=>({...em(e),options:[e,t]});var eA=n(9708),eL=n(5155),eT=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,eA.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eL.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),eP=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eL.jsx)(eT.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eL.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eP.displayName="Arrow";var eE=n(6101),eS=n(6081),eC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,eA.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,eL.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),eO=n(9033),ek=n(2712),eH=n(1275),ej="Popper",[eD,eN]=(0,eS.A)(ej),[eF,eM]=eD(ej),eW=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eL.jsx)(eF,{scope:t,anchor:i,onAnchorChange:o,children:n})};eW.displayName=ej;var eB="PopperAnchor",eV=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eM(eB,n),a=r.useRef(null),f=(0,eE.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eL.jsx)(eC.div,{...o,ref:f})});eV.displayName=eB;var ez="PopperContent",[e$,e_]=eD(ez),eI=r.forwardRef((e,t)=>{var n,i,a,u,c,s,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:R=0,sticky:A="partial",hideWhenDetached:L=!1,updatePositionStrategy:T="optimized",onPlaced:P,...E}=e,S=eM(ez,h),[C,O]=r.useState(null),H=(0,eE.s)(t,e=>O(e)),[j,D]=r.useState(null),N=(0,eH.X)(j),F=null!=(d=null==N?void 0:N.width)?d:0,M=null!=(p=null==N?void 0:N.height)?p:0,W="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},B=Array.isArray(b)?b:[b],V=B.length>0,z={padding:W,boundary:B.filter(eQ),altBoundary:V},{refs:$,floatingStyles:_,placement:X,isPositioned:Y,middlewareData:Q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:u,open:c}=e,[s,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);es(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),v=r.useCallback(e=>{e!==L.current&&(L.current=e,w(e))},[]),b=l||m,R=a||y,A=r.useRef(null),L=r.useRef(null),T=r.useRef(s),P=null!=u,E=eh(u),S=eh(o),C=eh(c),O=r.useCallback(()=>{if(!A.current||!L.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),ef(A.current,L.current,e).then(e=>{let t={...e,isPositioned:!1!==C.current};k.current&&!es(T.current,t)&&(T.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,S,C]);ec(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let k=r.useRef(!1);ec(()=>(k.current=!0,()=>{k.current=!1}),[]),ec(()=>{if(b&&(A.current=b),R&&(L.current=R),b&&R){if(E.current)return E.current(b,R,O);O()}},[b,R,O,E,P]);let H=r.useMemo(()=>({reference:A,floating:L,setReference:x,setFloating:v}),[x,v]),j=r.useMemo(()=>({reference:b,floating:R}),[b,R]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,s.x),r=ep(j.floating,s.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,j.floating,s.x,s.y]);return r.useMemo(()=>({...s,update:O,refs:H,elements:j,floatingStyles:D}),[s,O,H,j,D])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=a||u?[...p?I(p):[],...I(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&s?function(e,t){let n,r=null,i=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,s))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!v)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?J(e):null;return d&&function t(){let r=J(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===T})},elements:{reference:S.anchor},middleware:[eg({mainAxis:g+M,alignmentAxis:w}),v&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?ew():void 0,...z}),v&&ex({...z}),ev({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&eR({element:j,padding:x}),eU({arrowWidth:F,arrowHeight:M}),L&&eb({strategy:"referenceHidden",...z})]}),[U,G]=eG(X),K=(0,eO.c)(P);(0,ek.N)(()=>{Y&&(null==K||K())},[Y,K]);let Z=null==(n=Q.arrow)?void 0:n.x,ee=null==(i=Q.arrow)?void 0:i.y,et=(null==(a=Q.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,ek.N)(()=>{C&&er(window.getComputedStyle(C).zIndex)},[C]),(0,eL.jsx)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:Y?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=Q.transformOrigin)?void 0:u.x,null==(c=Q.transformOrigin)?void 0:c.y].join(" "),...(null==(s=Q.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eL.jsx)(e$,{scope:h,placedSide:U,onArrowChange:D,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eL.jsx)(eC.div,{"data-side":U,"data-align":G,...E,ref:H,style:{...E.style,animation:Y?void 0:"none"}})})})});eI.displayName=ez;var eX="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eq=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e_(eX,n),o=eY[i.placedSide];return(0,eL.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eL.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eQ(e){return null!==e}eq.displayName=eX;var eU=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,s=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=eG(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+s/2,y=(null!=(l=null==(i=u.arrow)?void 0:i.y)?l:0)+d/2,w="",x="";return"bottom"===p?(w=c?m:"".concat(g,"px"),x="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),x="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),x=c?m:"".concat(y,"px")),{data:{x:w,y:x}}}});function eG(e){let[t,n="center"]=e.split("-");return[t,n]}var eJ=eW,eK=eV,eZ=eI,e0=eq}}]);
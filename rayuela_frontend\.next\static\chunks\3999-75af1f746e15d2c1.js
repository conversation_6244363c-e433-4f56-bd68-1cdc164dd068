"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3999],{2523:(e,a,t)=>{t.d(a,{p:()=>o});var r=t(5155);t(2115);var s=t(9434);function o(e){let{className:a,type:t,...o}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-lg border bg-transparent px-3 py-1 text-base shadow-xs transition-all outline-none hover:border-ring/50 file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...o})}},3999:(e,a,t)=>{t.d(a,{A:()=>u,AuthProvider:()=>d});var r=t(5155),s=t(2115),o=t(5695),i=t(5731),n=t(6671),l=t(8868);let c=(0,s.createContext)(void 0),d=e=>{let{children:a}=e,[t,d]=(0,s.useState)(null),[u,m]=(0,s.useState)(null),[p,g]=(0,s.useState)(null),[h,f]=(0,s.useState)(!0),[x,y]=(0,s.useState)(!1),[v,b]=(0,s.useState)(null),j=(0,o.useRouter)(),A=(0,o.usePathname)(),[w,N]=(0,s.useState)(null),k=(0,s.useCallback)(()=>{localStorage.removeItem("rayuela-token"),localStorage.removeItem("rayuela-apiKey"),d(null),m(null),g(null)},[]),P=(0,s.useCallback)(async(e,a)=>{f(!0);try{let t=await (0,i.jp)();if(!t.is_active)throw Error("User account is inactive.");return d(t),m(e),a&&g(a),console.log("User data fetched successfully:",t),!0}catch(e){return console.error("Token validation/fetch user data failed:",e),k(),(null==A?void 0:A.startsWith("/dashboard"))&&(n.o.error("Tu sesi\xf3n ha expirado o es inv\xe1lida. Por favor, inicia sesi\xf3n de nuevo."),j.push("/login")),!1}finally{f(!1)}},[j,A,k]);(0,s.useEffect)(()=>{console.log("AuthProvider Mounted. Checking localStorage...");let e=localStorage.getItem("rayuela-token"),a=localStorage.getItem("rayuela-apiKey");e&&a?(console.log("Found token and apiKey in localStorage. Validating..."),P(e,a)):(console.log("No token or apiKey found in localStorage."),f(!1))},[P]);let I=(0,s.useCallback)(async()=>{if(!u)return n.o.error("No hay sesi\xf3n activa. Por favor, inicia sesi\xf3n de nuevo."),!1;try{return await (0,i.Hl)(),n.o.success("Email de verificaci\xf3n enviado. Por favor, revisa tu bandeja de entrada."),!0}catch(e){return console.error("Error al solicitar email de verificaci\xf3n:",e),e instanceof i.hD?n.o.error(e.message||"Error al solicitar email de verificaci\xf3n."):n.o.error("Error inesperado al solicitar email de verificaci\xf3n"),!1}},[u]),C=(0,s.useCallback)(async(e,a)=>{f(!0),N(null);try{let t=await (0,i.Lx)({email:e,password:a});if(t.access_token){localStorage.setItem("rayuela-token",t.access_token),m(t.access_token);let e=localStorage.getItem("rayuela-apiKey");if(e&&g(e),await P(t.access_token,e))return n.o.success("Login exitoso!"),j.push("/dashboard"),!0;return!1}throw Error("No se recibi\xf3 token de acceso.")}catch(r){if(console.error("Login processing failed:",r),r&&"object"==typeof r&&"error_code"in r&&"EMAIL_NOT_VERIFIED"===r.error_code)return N({email:e,password:a,message:r.message||"Por favor, verifica tu email para continuar."}),f(!1),!1;let t=r&&"object"==typeof r&&"message"in r?r.message:"Error al iniciar sesi\xf3n. Verifica tus credenciales.";return n.o.error(t),k(),f(!1),!1}},[P,j,k]),E=(0,s.useCallback)(async(e,a,t)=>{f(!0);try{let r=await (0,i.DY)(e,a,t);if(r.access_token){let e=r.api_key,a=r.access_token;return localStorage.setItem("rayuela-token",a),localStorage.setItem("rayuela-apiKey",e),m(a),g(e),b(e),y(!0),await P(a,e),{success:!0,apiKey:e}}throw Error("No se recibi\xf3 token de acceso")}catch(e){return console.error("Register processing failed:",e),e instanceof i.hD?n.o.error(e.message||"Error al registrarse"):n.o.error("Error inesperado al registrarse"),k(),{success:!1,error:e}}finally{f(!1)}},[P,k]),K=(0,s.useCallback)(async()=>{y(!1),b(null);let e=n.o.loading("Configurando tu cuenta...");f(!0);try{if(!u||!p)throw new i.hD("Token o API Key no disponibles",401,"AUTH_REQUIRED");let a=!1,t=0,r=null;for(;!a&&t<3;)try{await P(u,p),a=!0}catch(e){r=e,++t<3&&await new Promise(e=>setTimeout(e,500))}if(!a&&r)throw r;n.o.dismiss(e),n.o.success("\xa1Cuenta configurada correctamente!"),j.push("/dashboard")}catch(a){n.o.dismiss(e),a instanceof i.hD?n.o.error(a.message||"Error al inicializar la cuenta"):n.o.error("Error inesperado al configurar la cuenta")}finally{f(!1)}},[u,p,P,j]),S=(0,s.useCallback)(async()=>{try{u&&await (0,i.ri)(),k(),j.push("/login"),n.o.success("Sesi\xf3n cerrada correctamente")}catch(e){console.error("Logout error:",e),k(),j.push("/login")}},[u,k,j]);return(0,r.jsxs)(c.Provider,{value:{user:t,token:u,apiKey:p,setApiKey:g,login:C,register:E,logout:S,isLoading:h,emailVerificationError:w,requestNewVerificationEmail:I},children:[a,x&&v&&(0,r.jsx)(l.A,{apiKey:v,onClose:K})]})},u=()=>{let e=(0,s.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4165:(e,a,t)=>{t.d(a,{Cf:()=>u,Es:()=>p,L3:()=>g,c7:()=>m,lG:()=>n,rr:()=>h,zM:()=>l});var r=t(5155);t(2115);var s=t(9458),o=t(4416),i=t(9434);function n(e){let{...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function u(e){let{className:a,children:t,...n}=e;return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...n,children:[t,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function p(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function g(e){let{className:a,...t}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function h(e){let{className:a,...t}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}},5365:(e,a,t)=>{t.d(a,{Fc:()=>l,TN:()=>d,XL:()=>c});var r=t(5155),s=t(2115),o=t(2085),i=t(9434);let n=(0,o.F)("relative w-full rounded-lg border p-4 transition-all hover:shadow-sm [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground border-border",destructive:"border-destructive/50 text-destructive bg-destructive/5 hover:bg-destructive/10 [&>svg]:text-destructive",success:"border-success/50 text-success bg-success-light hover:bg-success/10 [&>svg]:text-success",warning:"border-warning/50 text-warning bg-warning-light hover:bg-warning/10 [&>svg]:text-warning",info:"border-info/50 text-info bg-info-light hover:bg-info/10 [&>svg]:text-info"}},defaultVariants:{variant:"default"}}),l=s.forwardRef((e,a)=>{let{className:t,variant:s,...o}=e;return(0,r.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(n({variant:s}),t),...o})});l.displayName="Alert";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h5",{ref:a,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})});c.displayName="AlertTitle";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...s})});d.displayName="AlertDescription"},5731:(e,a,t)=>{t.d(a,{A$:()=>u,DY:()=>l,Dm:()=>p,Hl:()=>d,Iq:()=>v,Lx:()=>n,M2:()=>f,PX:()=>y,S3:()=>x,T9:()=>h,XW:()=>b,_:()=>g,fw:()=>A,hD:()=>s,jp:()=>m,mA:()=>j,oE:()=>w,ri:()=>c});var r=t(2656);class s extends Error{constructor(e,a=500,t){super(e),this.name="ApiError",this.status=a,this.body=t}}function o(e,a){if(e&&"object"==typeof e&&"response"in e){var t,r;let o=(null==(t=e.response)?void 0:t.status)||500;throw new s(e.message||a,o,null==(r=e.response)?void 0:r.data)}if(e instanceof Error)throw new s(e.message,500);throw new s(a,500)}let i=(0,r._C)(),n=async e=>{try{return await i.loginApiV1AuthTokenPost(e)}catch(e){o(e,"Login failed")}},l=async(e,a,t)=>{try{return await i.registerApiV1AuthRegisterPost({accountName:e,email:a,password:t})}catch(e){o(e,"Registration failed")}},c=async()=>i.logoutApiV1AuthLogoutPost(),d=async()=>i.sendVerificationEmailApiV1AuthSendVerificationEmailPost(),u=async e=>i.verifyEmailApiV1AuthVerifyEmailGet({token:e}),m=async()=>{try{return await i.getCurrentUserInfoApiV1SystemUsersMeGet()}catch(e){o(e,"Failed to get current user")}},p=async()=>{try{return await i.getAccountInfoApiV1AccountsCurrentGet()}catch(e){o(e,"Failed to get current account")}},g=p,h=async()=>{try{return await i.getAvailablePlansApiV1PlansGet()}catch(e){o(e,"Failed to get plans")}},f=async(e,a)=>{let t={};return e&&(t.start_date=e),a&&(t.end_date=a),i.getUsageHistoryApiV1UsageHistoryGet(t)},x=async()=>{try{return await i.getUsageSummaryApiV1UsageSummaryGet()}catch(e){o(e,"Failed to get usage summary")}},y=async()=>{try{return await i.listApiKeysApiV1ApiKeysGet()}catch(e){o(e,"Failed to get API keys")}},v=async e=>{try{let a={name:e.name};return await i.createApiKeyApiV1ApiKeysPost(a)}catch(e){o(e,"Failed to create API key")}},b=async(e,a)=>{try{let t={name:a.name};return await i.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(e),t)}catch(e){o(e,"Failed to update API key")}},j=async e=>i.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(e),A=async e=>i.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost({price_id:e}),w=async()=>i.createPortalSessionApiV1BillingCreatePortalSessionPost({})},8868:(e,a,t)=>{t.d(a,{A:()=>b});var r=t(5155),s=t(2115),o=t(4165),i=t(285),n=t(2523),l=t(5365),c=t(1243),d=t(5196),u=t(4357),m=t(1684),p=t(1788),g=t(381),h=t(6671),f=t(4927),x=t(9434);let y=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(f.bL,{ref:a,className:(0,x.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,r.jsx)(f.C1,{className:(0,x.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})})});y.displayName=f.bL.displayName;var v=t(5695);let b=e=>{let{apiKey:a,onClose:t}=e,[f,x]=(0,s.useState)(!1),[b,j]=(0,s.useState)(!1),[A,w]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[P,I]=(0,s.useState)(!1),C=(0,v.useRouter)(),E='curl -X GET "'.concat("http://localhost:8001",'/health/auth" \\\n  -H "X-API-Key: ').concat(a,'"');(0,s.useEffect)(()=>{let e=e=>{if(!A)return e.preventDefault(),e.returnValue="",""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[A]);let K=()=>{navigator.clipboard.writeText(a).then(()=>{x(!0),k(!0),h.o.success("\xa1API Key copiada al portapapeles!"),setTimeout(()=>x(!1),2e3)}).catch(e=>{h.o.error("Error al copiar la API Key."),console.error("Error al copiar al portapapeles:",e)})},S=(e,a)=>{navigator.clipboard.writeText(e).then(()=>{j(!0),h.o.success("\xa1C\xf3digo ".concat(a," copiado al portapapeles!")),setTimeout(()=>j(!1),2e3)}).catch(e=>{h.o.error("Error al copiar el c\xf3digo."),console.error("Error al copiar al portapapeles:",e)})},D=()=>{try{let e=new Blob(["API Key de Rayuela\n","----------------\n","Fecha: ".concat(new Date().toLocaleString(),"\n"),"API Key: ".concat(a,"\n\n"),"IMPORTANTE: Guarda este archivo en un lugar seguro. Esta clave no se mostrar\xe1 completa nuevamente."],{type:"text/plain"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="rayuela-api-key.txt",document.body.appendChild(r),r.click(),setTimeout(()=>{document.body.removeChild(r),URL.revokeObjectURL(t)},0),k(!0),h.o.success("API Key descargada como archivo de texto")}catch(e){h.o.error("Error al descargar la API Key"),console.error("Error al descargar:",e)}},L=()=>{if(!N)return void I(!0);t()};return(0,r.jsx)(o.lG,{open:!0,onOpenChange:e=>!e&&L(),children:(0,r.jsxs)(o.Cf,{className:"sm:max-w-[650px] max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)(o.c7,{children:[(0,r.jsxs)(o.L3,{className:"text-xl font-bold flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD11"})," Tu primera API Key est\xe1 lista"]}),(0,r.jsxs)(o.rr,{className:"py-2",children:[(0,r.jsx)("p",{className:"mb-3",children:"\xa1Bienvenido a Rayuela! Tu primera API Key se ha generado autom\xe1ticamente. \xdasala para autenticar todas tus solicitudes a la API."}),(0,r.jsxs)("p",{className:"mb-3 text-sm text-muted-foreground",children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"Tip:"})," Puedes crear y gestionar m\xfaltiples API Keys para diferentes entornos o equipos desde la secci\xf3n 'API Keys' de tu dashboard."]}),(0,r.jsxs)(l.Fc,{variant:"warning",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)(l.XL,{children:"⚠️ Solo se muestra una vez"}),(0,r.jsx)(l.TN,{children:"Copia y guarda tu API Key ahora. No podr\xe1s verla completa nuevamente."})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-4 mt-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Tu primera API Key:"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.p,{id:"apiKey",readOnly:!0,value:a,className:"flex-1 font-mono text-sm bg-muted border-2"}),(0,r.jsxs)(i.Button,{type:"button",size:"sm",onClick:K,className:"min-w-[90px]",children:[f?(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}):(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),f?"\xa1Copiado!":"Copiar"]})]})]}),(0,r.jsxs)("div",{className:"bg-success-light border border-success/20 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-success"}),(0,r.jsx)("h4",{className:"font-semibold text-success-foreground",children:"\uD83D\uDE80 Prueba tu API Key ahora"})]}),(0,r.jsx)("p",{className:"text-sm text-success-foreground mb-3",children:"Ejecuta este comando para verificar que tu API Key funciona:"}),(0,r.jsxs)("div",{className:"bg-card border rounded-md p-3 relative",children:[(0,r.jsx)("pre",{className:"text-info text-xs overflow-x-auto",children:(0,r.jsx)("code",{children:E})}),(0,r.jsx)(i.Button,{type:"button",size:"sm",variant:"ghost",onClick:()=>S(E,"cURL"),className:"absolute top-2 right-2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground",children:b?(0,r.jsx)(d.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:"text-xs text-success-foreground mt-2",children:"Deber\xedas recibir una respuesta con status 200 y un mensaje de bienvenida."})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between gap-4 pt-4",children:[(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)(i.Button,{type:"button",onClick:D,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Descargar como archivo"]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(y,{id:"confirmSaved",checked:A,onCheckedChange:e=>w(e)}),(0,r.jsx)("label",{htmlFor:"confirmSaved",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"He guardado mi API Key de forma segura"})]})]}),(0,r.jsxs)(l.Fc,{variant:"info",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)(l.XL,{children:"Informaci\xf3n importante"}),(0,r.jsx)(l.TN,{children:(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm mt-2",children:[(0,r.jsxs)("li",{children:["Usa esta API Key en el header ",(0,r.jsx)("code",{className:"text-code-inline",children:"X-API-Key"})," de tus solicitudes"]}),(0,r.jsx)("li",{children:"No compartas tu API Key p\xfablicamente"}),(0,r.jsx)("li",{children:"Puedes crear m\xfaltiples API Keys desde tu panel de control"}),(0,r.jsx)("li",{children:"Si pierdes tu API Key, puedes crear una nueva y revocar la anterior"})]})})]})]}),(0,r.jsxs)(o.Es,{className:"flex flex-col gap-2 sm:flex-row sm:gap-3",children:[(0,r.jsxs)(i.Button,{onClick:()=>{t(),C.push("/api-keys")},variant:"outline",disabled:!N,className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),"Ir a Gesti\xf3n de API Keys"]}),(0,r.jsx)(i.Button,{onClick:t,disabled:!N,className:"flex-1",children:N?"Continuar al Dashboard":"Primero copia o descarga tu API Key"})]}),P&&(0,r.jsxs)(l.Fc,{variant:"warning",className:"mt-4",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)(l.XL,{children:"⚠️ Advertencia"}),(0,r.jsxs)(l.TN,{children:[(0,r.jsx)("p",{className:"text-sm mb-3",children:"A\xfan no has copiado o descargado tu API Key. Esta es la \xfanica vez que podr\xe1s verla completa."}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.Button,{onClick:K,size:"sm",variant:"outline",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Copiar API Key"]}),(0,r.jsxs)(i.Button,{onClick:D,size:"sm",variant:"outline",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Descargar"]}),(0,r.jsx)(i.Button,{onClick:()=>I(!1),size:"sm",variant:"ghost",children:"Cancelar"})]})]})]})]})})}}}]);
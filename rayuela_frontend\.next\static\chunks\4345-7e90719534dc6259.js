"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{381:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},646:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1243:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1539:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},3314:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},3904:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4213:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4472:(e,t,a)=>{a.d(t,{C1:()=>g,bL:()=>w});var r=a(2115),l=a(6081);a(7650);var n=a(9708),o=a(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?a:t,{...n,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),d="Progress",[u,c]=(0,l.A)(d),[p,s]=u(d),y=r.forwardRef((e,t)=>{var a,r,l,n;let{__scopeProgress:d,value:u=null,max:c,getValueLabel:s=k,...y}=e;(c||0===c)&&!x(c)&&console.error((a="".concat(c),r="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=x(c)?c:100;null===u||A(u,h)||console.error((l="".concat(u),n="Progress","Invalid prop `value` of value `".concat(l,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let v=A(u,h)?u:null,w=f(v)?s(v,h):void 0;return(0,o.jsx)(p,{scope:d,value:v,max:h,children:(0,o.jsx)(i.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":f(v)?v:void 0,"aria-valuetext":w,role:"progressbar","data-state":m(v,h),"data-value":null!=v?v:void 0,"data-max":h,...y,ref:t})})});y.displayName=d;var h="ProgressIndicator",v=r.forwardRef((e,t)=>{var a;let{__scopeProgress:r,...l}=e,n=s(h,r);return(0,o.jsx)(i.div,{"data-state":m(n.value,n.max),"data-value":null!=(a=n.value)?a:void 0,"data-max":n.max,...l,ref:t})});function k(e,t){return"".concat(Math.round(e/t*100),"%")}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function x(e){return f(e)&&!isNaN(e)&&e>0}function A(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}v.displayName=h;var w=y,g=v},4861:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5487:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},5690:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6081:(e,t,a)=>{a.d(t,{A:()=>o,q:()=>n});var r=a(2115),l=a(5155);function n(e,t){let a=r.createContext(t),n=e=>{let{children:t,...n}=e,o=r.useMemo(()=>n,Object.values(n));return(0,l.jsx)(a.Provider,{value:o,children:t})};return n.displayName=e+"Provider",[n,function(l){let n=r.useContext(a);if(n)return n;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let a=[],n=()=>{let t=a.map(e=>r.createContext(e));return function(a){let l=a?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return n.scopeName=e,[function(t,n){let o=r.createContext(n),i=a.length;a=[...a,n];let d=t=>{let{scope:a,children:n,...d}=t,u=a?.[e]?.[i]||o,c=r.useMemo(()=>d,Object.values(d));return(0,l.jsx)(u.Provider,{value:c,children:n})};return d.displayName=t+"Provider",[d,function(a,l){let d=l?.[e]?.[i]||o,u=r.useContext(d);if(u)return u;if(void 0!==n)return n;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=a.reduce((t,{useScope:a,scopeName:r})=>{let l=a(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return a.scopeName=t.scopeName,a}(n,...t)]}},9376:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9676:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},9869:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9946:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:u="",children:c,iconNode:p,...s}=e;return(0,r.createElement)("svg",{ref:t,...d,width:l,height:l,stroke:a,strokeWidth:o?24*Number(n)/Number(l):n,className:i("lucide",u),...s},[...p.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),c=(e,t)=>{let a=(0,r.forwardRef)((a,n)=>{let{className:d,...c}=a;return(0,r.createElement)(u,{ref:n,iconNode:t,className:i("lucide-".concat(l(o(e))),"lucide-".concat(e),d),...c})});return a.displayName=o(e),a}}}]);
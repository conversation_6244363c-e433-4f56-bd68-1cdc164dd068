"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5674],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1285:(e,t,n)=>{n.d(t,{B:()=>l});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function l(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},1595:(e,t,n)=>{n.d(t,{U:()=>i});var r=n(2115),o=n(9033);function i(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3540:(e,t,n)=>{n.d(t,{sG:()=>c,hO:()=>s});var r=n(2115),o=n(7650),i=n(6101),a=n(5155),u=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,u,l=(a=n,(u=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(u=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,u=r.Children.toArray(o),c=u.find(l);if(c){let e=c.props.children,o=u.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var u=("function"==typeof SuppressedError&&SuppressedError,n(2115)),l="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?u.useLayoutEffect:u.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},h=u.forwardRef(function(e,t){var n,r,o,l,c=u.useRef(null),p=u.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=p[0],g=p[1],y=e.forwardProps,b=e.children,E=e.className,w=e.removeScrollBar,C=e.enabled,N=e.shards,R=e.sideCar,x=e.noIsolation,S=e.inert,O=e.allowPinchZoom,A=e.as,D=e.gapMode,P=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,u.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,d(function(){var e=f.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(l,n)},[n]),l),T=i(i({},P),h);return u.createElement(u.Fragment,null,C&&u.createElement(R,{sideCar:v,removeScrollBar:w,shards:N,noIsolation:x,inert:S,setCallbacks:g,allowPinchZoom:!!O,lockRef:c,gapMode:D}),y?u.cloneElement(u.Children.only(b),i(i({},T),{ref:M})):u.createElement(void 0===A?"div":A,i({},T,{className:E,ref:M}),b))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:c,zeroRight:l};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return u.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,n){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},E=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},C=function(e){return parseInt(e||"",10)||0},N=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[C(n),C(r),C(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=N(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},x=E(),S="data-scroll-locked",O=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},D=function(){u.useEffect(function(){return document.body.setAttribute(S,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;D();var i=u.useMemo(function(){return R(o)},[o]);return u.createElement(x,{styles:O(i,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){M=!1}var j=!!M&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=a*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,p=0;do{var v=W(e,l),m=v[0],h=v[1]-v[2]-a*m;(m||h)&&I(e,l)&&(f+=h,p+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-u>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},$=0,z=[];let G=(r=function(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState($++)[0],i=u.useState(E)[0],a=u.useRef(e);u.useEffect(function(){a.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=u.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),u=n.current,l="deltaX"in e?e.deltaX:u[0]-i[0],c="deltaY"in e?e.deltaY:u[1]-i[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=L(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?l:c,!0)},[]),c=u.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?B(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=u.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=u.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=u.useCallback(function(t){s(t.type,B(t),t.target,l(t,e.lockRef.current))},[]),p=u.useCallback(function(t){s(t.type,F(t),t.target,l(t,e.lockRef.current))},[]);u.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var v=e.removeScrollBar,m=e.inert;return u.createElement(u.Fragment,null,m?u.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?u.createElement(P,{gapMode:e.gapMode}):null)},v.useMedium(r),g);var V=u.forwardRef(function(e,t){return u.createElement(h,i({},e,{ref:t,sideCar:G}))});V.classNames=h.classNames;let K=V},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,u,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),u=o.useRef(t);return a(()=>{u.current=t},[t]),o.useEffect(()=>{i.current!==n&&(u.current?.(n),i.current=n)},[n,i]),[n,r,u]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else u(t)},[c,e,u,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),u=n.length;n=[...n,i];let l=t=>{let{scope:n,children:i,...l}=t,c=n?.[e]?.[u]||a,s=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:s,children:i})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||a,c=r.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},u=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};c.forEach(v);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,u=(o.get(e)||0)+1,l=(s.get(e)||0)+1;o.set(e,u),s.set(e,l),d.push(e),1===u&&a&&i.set(e,!0),1===l&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9458:(e,t,n)=>{n.d(t,{bm:()=>eR,UC:()=>ew,VY:()=>eN,hJ:()=>eE,ZL:()=>eb,bL:()=>eg,hE:()=>eC,l9:()=>ey,G$:()=>ep,Hs:()=>B});var r,o=n(2115),i=n(5185),a=n(6101),u=n(6081),l=n(1285),c=n(5845),s=n(3540),d=n(9033),f=n(1595),p=n(5155),v="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=o.forwardRef((e,t)=>{var n,u;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:c,onPointerDownOutside:h,onFocusOutside:b,onInteractOutside:E,onDismiss:w,...C}=e,N=o.useContext(m),[R,x]=o.useState(null),S=null!=(u=null==R?void 0:R.ownerDocument)?u:null==(n=globalThis)?void 0:n.document,[,O]=o.useState({}),A=(0,a.s)(t,e=>x(e)),D=Array.from(N.layers),[P]=[...N.layersWithOutsidePointerEventsDisabled].slice(-1),M=D.indexOf(P),T=R?D.indexOf(R):-1,j=N.layersWithOutsidePointerEventsDisabled.size>0,k=T>=M,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,d.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){y("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...N.branches].some(e=>e.contains(t));k&&!n&&(null==h||h(e),null==E||E(e),e.defaultPrevented||null==w||w())},S),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,d.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&y("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...N.branches].some(e=>e.contains(t))&&(null==b||b(e),null==E||E(e),e.defaultPrevented||null==w||w())},S);return(0,f.U)(e=>{T===N.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},S),o.useEffect(()=>{if(R)return l&&(0===N.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),N.layersWithOutsidePointerEventsDisabled.add(R)),N.layers.add(R),g(),()=>{l&&1===N.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[R,S,l,N]),o.useEffect(()=>()=>{R&&(N.layers.delete(R),N.layersWithOutsidePointerEventsDisabled.delete(R),g())},[R,N]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,p.jsx)(s.sG.div,{...C,ref:A,style:{pointerEvents:j?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function g(){let e=new CustomEvent(v);document.dispatchEvent(e)}function y(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,s.hO)(i,a):i.dispatchEvent(a)}h.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(m),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(s.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var b="focusScope.autoFocusOnMount",E="focusScope.autoFocusOnUnmount",w={bubbles:!1,cancelable:!0},C=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:u,...l}=e,[c,f]=o.useState(null),v=(0,d.c)(i),m=(0,d.c)(u),h=o.useRef(null),g=(0,a.s)(t,e=>f(e)),y=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(y.paused||!c)return;let t=e.target;c.contains(t)?h.current=t:x(h.current,{select:!0})},t=function(e){if(y.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||x(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&x(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,y.paused]),o.useEffect(()=>{if(c){S.add(y);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(b,w);c.addEventListener(b,v),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(x(r,{select:t}),document.activeElement!==n)return}(N(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&x(c))}return()=>{c.removeEventListener(b,v),setTimeout(()=>{let t=new CustomEvent(E,w);c.addEventListener(E,m),c.dispatchEvent(t),t.defaultPrevented||x(null!=e?e:document.body,{select:!0}),c.removeEventListener(E,m),S.remove(y)},0)}}},[c,v,m,y]);let C=o.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=N(e);return[R(t,e),R(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&x(i,{select:!0})):(e.preventDefault(),n&&x(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,p.jsx)(s.sG.div,{tabIndex:-1,...l,ref:g,onKeyDown:C})});function N(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function R(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function x(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}C.displayName="FocusScope";var S=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=O(e,t)).unshift(t)},remove(t){var n;null==(n=(e=O(e,t))[0])||n.resume()}}}();function O(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var A=n(7650),D=n(2712),P=o.forwardRef((e,t)=>{var n,r;let{container:i,...a}=e,[u,l]=o.useState(!1);(0,D.N)(()=>l(!0),[]);let c=i||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?A.createPortal((0,p.jsx)(s.sG.div,{...a,ref:t}),c):null});P.displayName="Portal";var M=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),a=o.useRef(null),u=o.useRef(e),l=o.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=T(a.current);l.current="mounted"===c?e:"none"},[c]),(0,D.N)(()=>{let t=a.current,n=u.current;if(n!==e){let r=l.current,o=T(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),(0,D.N)(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=T(a.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=T(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),u=(0,a.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:u}):null};function T(e){return(null==e?void 0:e.animationName)||"none"}M.displayName="Presence";var j=n(2293),k=n(3795),L=n(8168),I=Symbol("radix.slottable");function W(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===I}var _="Dialog",[F,B]=(0,u.A)(_),[U,$]=F(_),z=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,s=o.useRef(null),d=o.useRef(null),[f,v]=(0,c.i)({prop:r,defaultProp:null!=i&&i,onChange:a,caller:_});return(0,p.jsx)(U,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),modal:u,children:n})};z.displayName=_;var G="DialogTrigger",V=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=$(G,n),u=(0,a.s)(t,o.triggerRef);return(0,p.jsx)(s.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ed(o.open),...r,ref:u,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});V.displayName=G;var K="DialogPortal",[q,X]=F(K,{forceMount:void 0}),Y=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,a=$(K,t);return(0,p.jsx)(q,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,p.jsx)(M,{present:n||a.open,children:(0,p.jsx)(P,{asChild:!0,container:i,children:e})}))})};Y.displayName=K;var Z="DialogOverlay",H=o.forwardRef((e,t)=>{let n=X(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=$(Z,e.__scopeDialog);return i.modal?(0,p.jsx)(M,{present:r||i.open,children:(0,p.jsx)(Q,{...o,ref:t})}):null});H.displayName=Z;var J=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){var i;let e,u,l=(i=n,(u=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(u=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(c.ref=t?(0,a.t)(t,l):l),o.cloneElement(n,c)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,a=o.Children.toArray(r),u=a.find(W);if(u){let e=u.props.children,r=a.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,p.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,p.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}("DialogOverlay.RemoveScroll"),Q=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=$(Z,n);return(0,p.jsx)(k.A,{as:J,allowPinchZoom:!0,shards:[o.contentRef],children:(0,p.jsx)(s.sG.div,{"data-state":ed(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ee="DialogContent",et=o.forwardRef((e,t)=>{let n=X(ee,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=$(ee,e.__scopeDialog);return(0,p.jsx)(M,{present:r||i.open,children:i.modal?(0,p.jsx)(en,{...o,ref:t}):(0,p.jsx)(er,{...o,ref:t})})});et.displayName=ee;var en=o.forwardRef((e,t)=>{let n=$(ee,e.__scopeDialog),r=o.useRef(null),u=(0,a.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,L.Eq)(e)},[]),(0,p.jsx)(eo,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),er=o.forwardRef((e,t)=>{let n=$(ee,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,p.jsx)(eo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,a;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let u=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eo=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:u,...l}=e,c=$(ee,n),s=o.useRef(null),d=(0,a.s)(t,s);return(0,j.Oh)(),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(C,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:u,children:(0,p.jsx)(h,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":ed(c.open),...l,ref:d,onDismiss:()=>c.onOpenChange(!1)})}),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(em,{titleId:c.titleId}),(0,p.jsx)(eh,{contentRef:s,descriptionId:c.descriptionId})]})]})}),ei="DialogTitle",ea=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=$(ei,n);return(0,p.jsx)(s.sG.h2,{id:o.titleId,...r,ref:t})});ea.displayName=ei;var eu="DialogDescription",el=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=$(eu,n);return(0,p.jsx)(s.sG.p,{id:o.descriptionId,...r,ref:t})});el.displayName=eu;var ec="DialogClose",es=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=$(ec,n);return(0,p.jsx)(s.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function ed(e){return e?"open":"closed"}es.displayName=ec;var ef="DialogTitleWarning",[ep,ev]=(0,u.q)(ef,{contentName:ee,titleName:ei,docsSlug:"dialog"}),em=e=>{let{titleId:t}=e,n=ev(ef),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},eh=e=>{let{contentRef:t,descriptionId:n}=e,r=ev("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},eg=z,ey=V,eb=Y,eE=H,ew=et,eC=ea,eN=el,eR=es}}]);
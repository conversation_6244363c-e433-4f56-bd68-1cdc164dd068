"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5813],{4140:(e,t,n)=>{n.d(t,{UC:()=>eJ,YJ:()=>e$,In:()=>eY,q7:()=>eQ,VF:()=>e1,p4:()=>e0,JU:()=>eG,ZL:()=>eZ,bL:()=>eU,wn:()=>e6,PP:()=>e5,wv:()=>e3,l9:()=>ez,WT:()=>eq,LM:()=>eX});var r,l=n(2115),o=n(7650);function a(e,[t,n]){return Math.min(n,Math.max(t,e))}var i=n(5185),s=n(7328),u=n(6101),d=n(6081),c=n(4315),f=n(9708),p=n(5155),v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,f.TL)(`Primitive.${t}`),r=l.forwardRef((e,r)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(l?n:t,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),m=n(9033),h=n(1595),w="dismissableLayer.update",y=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=l.forwardRef((e,t)=>{var n,o;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:f,onDismiss:g,...E}=e,S=l.useContext(y),[C,T]=l.useState(null),P=null!=(o=null==C?void 0:C.ownerDocument)?o:null==(n=globalThis)?void 0:n.document,[,L]=l.useState({}),N=(0,u.s)(t,e=>T(e)),j=Array.from(S.layers),[k]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),R=j.indexOf(k),D=C?j.indexOf(C):-1,I=S.layersWithOutsidePointerEventsDisabled.size>0,M=D>=R,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,m.c)(e),o=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));M&&!n&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==g||g())},P),F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,m.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...S.branches].some(e=>e.contains(t))&&(null==c||c(e),null==f||f(e),e.defaultPrevented||null==g||g())},P);return(0,h.U)(e=>{D===S.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},P),l.useEffect(()=>{if(C)return a&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(C)),S.layers.add(C),x(),()=>{a&&1===S.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[C,P,a,S]),l.useEffect(()=>()=>{C&&(S.layers.delete(C),S.layersWithOutsidePointerEventsDisabled.delete(C),x())},[C,S]),l.useEffect(()=>{let e=()=>L({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,p.jsx)(v.div,{...E,ref:N,style:{pointerEvents:I?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function x(){let e=new CustomEvent(w);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:l}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&a.addEventListener(e,t,{once:!0}),l)a&&o.flushSync(()=>a.dispatchEvent(i));else a.dispatchEvent(i)}g.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(y),r=l.useRef(null),o=(0,u.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(v.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var E=n(2293),S="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},P=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[s,d]=l.useState(null),c=(0,m.c)(o),f=(0,m.c)(a),h=l.useRef(null),w=(0,u.s)(t,e=>d(e)),y=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(y.paused||!s)return;let t=e.target;s.contains(t)?h.current=t:j(h.current,{select:!0})},t=function(e){if(y.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||j(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&j(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,y.paused]),l.useEffect(()=>{if(s){k.add(y);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(S,T);s.addEventListener(S,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(j(r,{select:t}),document.activeElement!==n)return}(L(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&j(s))}return()=>{s.removeEventListener(S,c),setTimeout(()=>{let t=new CustomEvent(C,T);s.addEventListener(C,f),s.dispatchEvent(t),t.defaultPrevented||j(null!=e?e:document.body,{select:!0}),s.removeEventListener(C,f),k.remove(y)},0)}}},[s,c,f,y]);let g=l.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,l=document.activeElement;if(t&&l){let t=e.currentTarget,[r,o]=function(e){let t=L(e);return[N(t,e),N(t.reverse(),e)]}(t);r&&o?e.shiftKey||l!==o?e.shiftKey&&l===r&&(e.preventDefault(),n&&j(o,{select:!0})):(e.preventDefault(),n&&j(r,{select:!0})):l===t&&e.preventDefault()}},[n,r,y.paused]);return(0,p.jsx)(v.div,{tabIndex:-1,...i,ref:w,onKeyDown:g})});function L(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function N(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function j(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}P.displayName="FocusScope";var k=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=R(e,t)).unshift(t)},remove(t){var n;null==(n=(e=R(e,t))[0])||n.resume()}}}();function R(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var D=n(1285),I=n(5773),M=n(2712),A=l.forwardRef((e,t)=>{var n,r;let{container:a,...i}=e,[s,u]=l.useState(!1);(0,M.N)(()=>u(!0),[]);let d=a||s&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return d?o.createPortal((0,p.jsx)(v.div,{...i,ref:t}),d):null});A.displayName="Portal";var F=n(5845),O=n(5503),B=n(3601),_=n(8168),H=n(3795),K=[" ","Enter","ArrowUp","ArrowDown"],W=[" ","Enter"],V="Select",[U,z,q]=(0,s.N)(V),[Y,Z]=(0,d.A)(V,[q,I.Bk]),J=(0,I.Bk)(),[X,$]=Y(V),[G,Q]=Y(V),ee=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:v,disabled:m,required:h,form:w}=e,y=J(t),[g,x]=l.useState(null),[b,E]=l.useState(null),[S,C]=l.useState(!1),T=(0,c.jH)(d),[P,L]=(0,F.i)({prop:r,defaultProp:null!=o&&o,onChange:a,caller:V}),[N,j]=(0,F.i)({prop:i,defaultProp:s,onChange:u,caller:V}),k=l.useRef(null),R=!g||w||!!g.closest("form"),[M,A]=l.useState(new Set),O=Array.from(M).map(e=>e.props.value).join(";");return(0,p.jsx)(I.bL,{...y,children:(0,p.jsxs)(X,{required:h,scope:t,trigger:g,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,D.B)(),value:N,onValueChange:j,open:P,onOpenChange:L,dir:T,triggerPointerDownPosRef:k,disabled:m,children:[(0,p.jsx)(U.Provider,{scope:t,children:(0,p.jsx)(G,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{A(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{A(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),R?(0,p.jsxs)(eH,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:v,value:N,onChange:e=>j(e.target.value),disabled:m,form:w,children:[void 0===N?(0,p.jsx)("option",{value:""}):null,Array.from(M)]},O):null]})})};ee.displayName=V;var et="SelectTrigger",en=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,a=J(n),s=$(et,n),d=s.disabled||r,c=(0,u.s)(t,s.onTriggerChange),f=z(n),m=l.useRef("touch"),[h,w,y]=eW(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eV(t,e,n);void 0!==r&&s.onValueChange(r.value)}),g=e=>{d||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,p.jsx)(I.Mz,{asChild:!0,...a,children:(0,p.jsx)(v.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eK(s.value)?"":void 0,...o,ref:c,onClick:(0,i.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&g(e)}),onPointerDown:(0,i.m)(o.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,i.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||w(e.key),(!t||" "!==e.key)&&K.includes(e.key)&&(g(),e.preventDefault())})})})});en.displayName=et;var er="SelectValue",el=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:a="",...i}=e,s=$(er,n),{onValueNodeHasChildrenChange:d}=s,c=void 0!==o,f=(0,u.s)(t,s.onValueNodeChange);return(0,M.N)(()=>{d(c)},[d,c]),(0,p.jsx)(v.span,{...i,ref:f,style:{pointerEvents:"none"},children:eK(s.value)?(0,p.jsx)(p.Fragment,{children:a}):o})});el.displayName=er;var eo=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,p.jsx)(v.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});eo.displayName="SelectIcon";var ea=e=>(0,p.jsx)(A,{asChild:!0,...e});ea.displayName="SelectPortal";var ei="SelectContent",es=l.forwardRef((e,t)=>{let n=$(ei,e.__scopeSelect),[r,a]=l.useState();return((0,M.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,p.jsx)(ef,{...e,ref:t}):r?o.createPortal((0,p.jsx)(eu,{scope:e.__scopeSelect,children:(0,p.jsx)(U.Slot,{scope:e.__scopeSelect,children:(0,p.jsx)("div",{children:e.children})})}),r):null});es.displayName=ei;var[eu,ed]=Y(ei),ec=(0,f.TL)("SelectContent.RemoveScroll"),ef=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:s,side:d,sideOffset:c,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:w,sticky:y,hideWhenDetached:x,avoidCollisions:b,...S}=e,C=$(ei,n),[T,L]=l.useState(null),[N,j]=l.useState(null),k=(0,u.s)(t,e=>L(e)),[R,D]=l.useState(null),[I,M]=l.useState(null),A=z(n),[F,O]=l.useState(!1),B=l.useRef(!1);l.useEffect(()=>{if(T)return(0,_.Eq)(T)},[T]),(0,E.Oh)();let K=l.useCallback(e=>{let[t,...n]=A().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),null==n||n.focus(),document.activeElement!==l))return},[A,N]),W=l.useCallback(()=>K([R,T]),[K,R,T]);l.useEffect(()=>{F&&W()},[F,W]);let{onOpenChange:V,triggerPointerDownPosRef:U}=C;l.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var n,r,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(n=U.current)?void 0:n.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(r=U.current)?void 0:r.y)?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,V,U]),l.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[q,Y]=eW(e=>{let t=A().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eV(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Z=l.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==C.value&&C.value===t||r)&&(D(e),r&&(B.current=!0))},[C.value]),J=l.useCallback(()=>null==T?void 0:T.focus(),[T]),X=l.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==C.value&&C.value===t||r)&&M(e)},[C.value]),G="popper"===r?ev:ep,Q=G===ev?{side:d,sideOffset:c,align:f,alignOffset:v,arrowPadding:m,collisionBoundary:h,collisionPadding:w,sticky:y,hideWhenDetached:x,avoidCollisions:b}:{};return(0,p.jsx)(eu,{scope:n,content:T,viewport:N,onViewportChange:j,itemRefCallback:Z,selectedItem:R,onItemLeave:J,itemTextRefCallback:X,focusSelectedItem:W,selectedItemText:I,position:r,isPositioned:F,searchRef:q,children:(0,p.jsx)(H.A,{as:ec,allowPinchZoom:!0,children:(0,p.jsx)(P,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(o,e=>{var t;null==(t=C.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,p.jsx)(g,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,p.jsx)(G,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...Q,onPlaced:()=>O(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,i.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ef.displayName="SelectContentImpl";var ep=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=$(ei,n),s=ed(ei,n),[d,c]=l.useState(null),[f,m]=l.useState(null),h=(0,u.s)(t,e=>m(e)),w=z(n),y=l.useRef(!1),g=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=s,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&f&&x&&b&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),l=E.getBoundingClientRect();if("rtl"!==i.dir){let r=l.left-t.left,o=n.left-r,i=e.left-o,s=e.width+i,u=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let r=t.right-l.right,o=window.innerWidth-n.right-r,i=window.innerWidth-e.right-o,s=e.width+i,u=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let o=w(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(f),p=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),m=parseInt(c.borderBottomWidth,10),h=p+v+u+parseInt(c.paddingBottom,10)+m,g=Math.min(5*b.offsetHeight,h),S=window.getComputedStyle(x),C=parseInt(S.paddingTop,10),T=parseInt(S.paddingBottom,10),P=e.top+e.height/2-10,L=b.offsetHeight/2,N=p+v+(b.offsetTop+L);if(N<=P){let e=o.length>0&&b===o[o.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-P,L+(e?T:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+m);d.style.height=N+t+"px"}else{let e=o.length>0&&b===o[0].ref.current;d.style.top="0px";let t=Math.max(P,p+x.offsetTop+(e?C:0)+L);d.style.height=t+(h-N)+"px",x.scrollTop=N-P+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=g+"px",d.style.maxHeight=s+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[w,i.trigger,i.valueNode,d,f,x,b,E,i.dir,r]);(0,M.N)(()=>C(),[C]);let[T,P]=l.useState();(0,M.N)(()=>{f&&P(window.getComputedStyle(f).zIndex)},[f]);let L=l.useCallback(e=>{e&&!0===g.current&&(C(),null==S||S(),g.current=!1)},[C,S]);return(0,p.jsx)(em,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:y,onScrollButtonChange:L,children:(0,p.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,p.jsx)(v.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});ep.displayName="SelectItemAlignedPosition";var ev=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,a=J(n);return(0,p.jsx)(I.UC,{...a,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ev.displayName="SelectPopperPosition";var[em,eh]=Y(ei,{}),ew="SelectViewport",ey=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,a=ed(ew,n),s=eh(ew,n),d=(0,u.s)(t,a.onViewportChange),c=l.useRef(0);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,p.jsx)(U.Slot,{scope:n,children:(0,p.jsx)(v.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,i.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ey.displayName=ew;var eg="SelectGroup",[ex,eb]=Y(eg),eE=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,D.B)();return(0,p.jsx)(ex,{scope:n,id:l,children:(0,p.jsx)(v.div,{role:"group","aria-labelledby":l,...r,ref:t})})});eE.displayName=eg;var eS="SelectLabel",eC=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=eb(eS,n);return(0,p.jsx)(v.div,{id:l.id,...r,ref:t})});eC.displayName=eS;var eT="SelectItem",[eP,eL]=Y(eT),eN=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:a,...s}=e,d=$(eT,n),c=ed(eT,n),f=d.value===r,[m,h]=l.useState(null!=a?a:""),[w,y]=l.useState(!1),g=(0,u.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,o)}),x=(0,D.B)(),b=l.useRef("touch"),E=()=>{o||(d.onValueChange(r),d.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,p.jsx)(eP,{scope:n,value:r,disabled:o,textId:x,isSelected:f,onItemTextChange:l.useCallback(e=>{h(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,p.jsx)(U.ItemSlot,{scope:n,value:r,disabled:o,textValue:m,children:(0,p.jsx)(v.div,{role:"option","aria-labelledby":x,"data-highlighted":w?"":void 0,"aria-selected":f&&w,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:g,onFocus:(0,i.m)(s.onFocus,()=>y(!0)),onBlur:(0,i.m)(s.onBlur,()=>y(!1)),onClick:(0,i.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,i.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,i.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,i.m)(s.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,i.m)(s.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(W.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eN.displayName=eT;var ej="SelectItemText",ek=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:a,...i}=e,s=$(ej,n),d=ed(ej,n),c=eL(ej,n),f=Q(ej,n),[m,h]=l.useState(null),w=(0,u.s)(t,e=>h(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),y=null==m?void 0:m.textContent,g=l.useMemo(()=>(0,p.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=f;return(0,M.N)(()=>(x(g),()=>b(g)),[x,b,g]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(v.span,{id:c.textId,...i,ref:w}),c.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(i.children,s.valueNode):null]})});ek.displayName=ej;var eR="SelectItemIndicator",eD=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return eL(eR,n).isSelected?(0,p.jsx)(v.span,{"aria-hidden":!0,...r,ref:t}):null});eD.displayName=eR;var eI="SelectScrollUpButton",eM=l.forwardRef((e,t)=>{let n=ed(eI,e.__scopeSelect),r=eh(eI,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,u.s)(t,r.onScrollButtonChange);return(0,M.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(eO,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eM.displayName=eI;var eA="SelectScrollDownButton",eF=l.forwardRef((e,t)=>{let n=ed(eA,e.__scopeSelect),r=eh(eA,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,u.s)(t,r.onScrollButtonChange);return(0,M.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(eO,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eF.displayName=eA;var eO=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,a=ed("SelectScrollButton",n),s=l.useRef(null),u=z(n),d=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,M.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,p.jsx)(v.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,i.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,i.m)(o.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,i.m)(o.onPointerLeave,()=>{d()})})}),eB=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,p.jsx)(v.div,{"aria-hidden":!0,...r,ref:t})});eB.displayName="SelectSeparator";var e_="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=J(n),o=$(e_,n),a=ed(e_,n);return o.open&&"popper"===a.position?(0,p.jsx)(I.i3,{...l,...r,ref:t}):null}).displayName=e_;var eH=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,a=l.useRef(null),i=(0,u.s)(t,a),s=(0,O.Z)(r);return l.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,p.jsx)(v.select,{...o,style:{...B.Qg,...o.style},ref:i,defaultValue:r})});function eK(e){return""===e||void 0===e}function eW(e){let t=(0,m.c)(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let l=n.current+e;t(l),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),a=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,a]}function eV(e,t,n){var r,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,i=(r=e,l=Math.max(a,0),r.map((e,t)=>r[(l+t)%r.length]));1===o.length&&(i=i.filter(e=>e!==n));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==n?s:void 0}eH.displayName="SelectBubbleInput";var eU=ee,ez=en,eq=el,eY=eo,eZ=ea,eJ=es,eX=ey,e$=eE,eG=eC,eQ=eN,e0=ek,e1=eD,e5=eM,e6=eF,e3=eB},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);
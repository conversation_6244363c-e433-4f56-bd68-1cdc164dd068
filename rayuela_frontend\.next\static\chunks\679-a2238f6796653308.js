"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[679],{714:(e,t,n)=>{n.d(t,{f:()=>i});var r=n(7239),o=n(9447);function i(e,t,n){let i=(0,o.a)(e,null==n?void 0:n.in);return isNaN(t)?(0,r.w)((null==n?void 0:n.in)||e,NaN):(t&&i.setDate(i.getDate()+t),i)}},1228:(e,t,n)=>{n.d(t,{h:()=>e3});var r,o,i,a,s,l={};n.r(l),n.d(l,{Button:()=>Q,CaptionLabel:()=>G,Chevron:()=>J,Day:()=>ee,DayButton:()=>et,Dropdown:()=>en,DropdownNav:()=>er,Footer:()=>eo,Month:()=>ei,MonthCaption:()=>ea,MonthGrid:()=>es,Months:()=>el,MonthsDropdown:()=>ec,Nav:()=>ef,NextMonthButton:()=>eh,Option:()=>ep,PreviousMonthButton:()=>em,Root:()=>ey,Select:()=>eb,Week:()=>eg,WeekNumber:()=>ew,WeekNumberHeader:()=>eM,Weekday:()=>ev,Weekdays:()=>ex,Weeks:()=>ek,YearsDropdown:()=>eC});var d={};n.r(d),n.d(d,{formatCaption:()=>eD,formatDay:()=>eS,formatMonthCaption:()=>eO,formatMonthDropdown:()=>eT,formatWeekNumber:()=>eE,formatWeekNumberHeader:()=>eN,formatWeekdayName:()=>eP,formatYearCaption:()=>eW,formatYearDropdown:()=>e_});var u={};n.r(u),n.d(u,{labelCaption:()=>eI,labelDay:()=>eY,labelDayButton:()=>eR,labelGrid:()=>eA,labelGridcell:()=>ej,labelMonthDropdown:()=>eF,labelNav:()=>eL,labelNext:()=>eH,labelPrevious:()=>eB,labelWeekNumber:()=>ez,labelWeekNumberHeader:()=>eX,labelWeekday:()=>e$,labelYearDropdown:()=>eZ});var c=n(2115);Symbol.for("constructDateFrom");let f={},h={};function p(e,t){try{let n=(f[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(n in h)return h[n];return y(n,n.split(":"))}catch{if(e in h)return h[e];let t=e?.match(m);if(t)return y(e,t.slice(1));return NaN}}let m=/([+-]\d\d):?(\d\d)?/;function y(e,t){let n=+t[0],r=+(t[1]||0);return h[e]=n>0?60*n+r:60*n-r}class b extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(p(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),x(this,NaN),v(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new b(...t,e):new b(Date.now(),e)}withTimeZone(e){return new b(+this,e)}getTimezoneOffset(){return-p(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),v(this),+this}[Symbol.for("constructDateFrom")](e){return new b(+new Date(e),this.timeZone)}}let g=/^(get|set)(?!UTC)/;function v(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function x(e){let t=p(e.timeZone,e),n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);let r=-new Date(+e).getTimezoneOffset(),o=r- -new Date(+n).getTimezoneOffset(),i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let a=r-t;a&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+a);let s=p(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-a;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-p(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!g.test(e))return;let t=e.replace(g,"$1UTC");b.prototype[t]&&(e.startsWith("get")?b.prototype[e]=function(){return this.internal[t]()}:(b.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),x(e),+this},b.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),v(this),+this}))});class w extends b{static tz(e,...t){return t.length?new w(...t,e):new w(Date.now(),e)}toISOString(){let[e,t,n]=this.tzComponents(),r=`${e}${t}:${n}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,n,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${n} ${t} ${r}`}toTimeString(){var e,t;let n=this.internal.toUTCString().split(" ")[4],[r,o,i]=this.tzComponents();return`${n} GMT${r}${o}${i} (${e=this.timeZone,t=this,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),n=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,n]}withTimeZone(e){return new w(+this,e)}[Symbol.for("constructDateFrom")](e){return new w(+new Date(e),this.timeZone)}}!function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(r||(r={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(o||(o={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(i||(i={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(a||(a={}));var M=n(3039),k=n(714),C=n(7239),D=n(9447);function O(e,t,n){let r=(0,D.a)(e,null==n?void 0:n.in);if(isNaN(t))return(0,C.w)((null==n?void 0:n.in)||e,NaN);if(!t)return r;let o=r.getDate(),i=(0,C.w)((null==n?void 0:n.in)||e,r.getTime());return(i.setMonth(r.getMonth()+t+1,0),o>=i.getDate())?i:(r.setFullYear(i.getFullYear(),i.getMonth(),o),r)}var S=n(8637),T=n(1183),E=n(5490);function N(e,t){var n,r,o,i,a,s,l,d;let u=(0,E.q)(),c=null!=(d=null!=(l=null!=(s=null!=(a=null==t?void 0:t.weekStartsOn)?a:null==t||null==(r=t.locale)||null==(n=r.options)?void 0:n.weekStartsOn)?s:u.weekStartsOn)?l:null==(i=u.locale)||null==(o=i.options)?void 0:o.weekStartsOn)?d:0,f=(0,D.a)(e,null==t?void 0:t.in),h=f.getDay();return f.setDate(f.getDate()+((h<c?-7:0)+6-(h-c))),f.setHours(23,59,59,999),f}var P=n(3008),_=n(7519),W=n(1391),A=n(7716),I=n(9026),j=n(9092),R=n(540),Y=n(4423),L=n(7386);function F(e,t){let n=t.startOfMonth(e),r=n.getDay();return 1===r?n:0===r?t.addDays(n,-6):t.addDays(n,-1*(r-1))}class H{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?w.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,n)=>this.overrides?.newDate?this.overrides.newDate(e,t,n):this.options.timeZone?new w(e,t,n,this.options.timeZone):new Date(e,t,n),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):(0,k.f)(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):O(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):(0,k.f)(e,7*t,void 0),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):O(e,12*t,void 0),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,S.m)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t,n){let[r,o]=(0,T.x)(void 0,e,t);return 12*(r.getFullYear()-o.getFullYear())+(r.getMonth()-o.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){var n;let{start:r,end:o}=function(e,t){let[n,r]=(0,T.x)(e,t.start,t.end);return{start:n,end:r}}(void 0,e),i=+r>+o,a=i?+r:+o,s=i?o:r;s.setHours(0,0,0,0),s.setDate(1);let l=(n=void 0,1);if(!l)return[];l<0&&(l=-l,i=!i);let d=[];for(;+s<=a;)d.push((0,C.w)(r,s)),s.setMonth(s.getMonth()+l);return i?d.reverse():d}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let n=F(e,t),r=function(e,t){let n=t.startOfMonth(e),r=n.getDay()>0?n.getDay():7,o=t.addDays(e,-r+1),i=t.addDays(o,34);return t.getMonth(e)===t.getMonth(i)?5:4}(e,t);return t.addDays(n,7*r-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):N(e,{...void 0,weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):function(e,t){let n=(0,D.a)(e,void 0),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):N(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e,t){let n=(0,D.a)(e,void 0),r=n.getFullYear();return n.setFullYear(r+1,0,0),n.setHours(23,59,59,999),n}(e),this.format=(e,t,n)=>{let r=this.overrides?.format?this.overrides.format(e,t,this.options):(0,P.GP)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(r):r},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,_.s)(e),this.getMonth=(e,t)=>this.overrides?.getMonth?this.overrides.getMonth(e,this.options):function(e,t){return(0,D.a)(e,null==t?void 0:t.in).getMonth()}(e,this.options),this.getYear=(e,t)=>this.overrides?.getYear?this.overrides.getYear(e,this.options):function(e,t){return(0,D.a)(e,null==t?void 0:t.in).getFullYear()}(e,this.options),this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,W.N)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):(0,A.d)(e,t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):+(0,D.a)(e)<+(0,D.a)(t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,I.$)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):function(e,t,n){let[r,o]=(0,T.x)(void 0,e,t);return+(0,j.o)(r)==+(0,j.o)(o)}(e,t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):function(e,t,n){let[r,o]=(0,T.x)(void 0,e,t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t,n){let[r,o]=(0,T.x)(void 0,e,t);return r.getFullYear()===o.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e,t){let n,r;return e.forEach(e=>{r||"object"!=typeof e||(r=C.w.bind(null,e));let t=(0,D.a)(e,r);(!n||n<t||isNaN(+t))&&(n=t)}),(0,C.w)(r,n||NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e,t){let n,r;return e.forEach(e=>{r||"object"!=typeof e||(r=C.w.bind(null,e));let t=(0,D.a)(e,r);(!n||n>t||isNaN(+t))&&(n=t)}),(0,C.w)(r,n||NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):function(e,t,n){let r=(0,D.a)(e,void 0),o=r.getFullYear(),i=r.getDate(),a=(0,C.w)(e,0);a.setFullYear(o,t,15),a.setHours(0,0,0,0);let s=function(e,t){let n=(0,D.a)(e,void 0),r=n.getFullYear(),o=n.getMonth(),i=(0,C.w)(n,0);return i.setFullYear(r,o+1,0),i.setHours(0,0,0,0),i.getDate()}(a);return r.setMonth(t,Math.min(i,s)),r}(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t,n){let r=(0,D.a)(e,void 0);return isNaN(+r)?(0,C.w)(e,NaN):(r.setFullYear(t),r)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):F(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,j.o)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,R.b)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):function(e,t){let n=(0,D.a)(e,void 0);return n.setDate(1),n.setHours(0,0,0,0),n}(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,Y.k)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,L.D)(e),this.options={locale:M.c,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),n={};for(let e=0;e<10;e++)n[e.toString()]=t.format(e);return n}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let B=new H;function $(e,t,n=!1,r=B){let{from:o,to:i}=e,{differenceInCalendarDays:a,isSameDay:s}=r;return o&&i?(0>a(i,o)&&([o,i]=[i,o]),a(t,o)>=+!!n&&a(i,t)>=+!!n):!n&&i?s(i,t):!n&&!!o&&s(o,t)}function z(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function X(e){return!!(e&&"object"==typeof e&&"from"in e)}function Z(e){return!!(e&&"object"==typeof e&&"after"in e)}function U(e){return!!(e&&"object"==typeof e&&"before"in e)}function q(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function V(e,t){return Array.isArray(e)&&e.every(t.isDate)}function K(e,t,n=B){let r=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:i,isAfter:a}=n;return r.some(t=>{if("boolean"==typeof t)return t;if(n.isDate(t))return o(e,t);if(V(t,n))return t.includes(e);if(X(t))return $(t,e,!1,n);if(q(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(z(t)){let n=i(t.before,e),r=i(t.after,e),o=n>0,s=r<0;return a(t.before,t.after)?s&&o:o||s}return Z(t)?i(e,t.after)>0:U(t)?i(t.before,e)>0:"function"==typeof t&&t(e)})}function Q(e){return c.createElement("button",{...e})}function G(e){return c.createElement("span",{...e})}function J(e){let{size:t=24,orientation:n="left",className:r}=e;return c.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},"up"===n&&c.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===n&&c.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===n&&c.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===n&&c.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function ee(e){let{day:t,modifiers:n,...r}=e;return c.createElement("td",{...r})}function et(e){let{day:t,modifiers:n,...r}=e,o=c.useRef(null);return c.useEffect(()=>{n.focused&&o.current?.focus()},[n.focused]),c.createElement("button",{ref:o,...r})}function en(e){let{options:t,className:n,components:o,classNames:i,...a}=e,s=[i[r.Dropdown],n].join(" "),l=t?.find(({value:e})=>e===a.value);return c.createElement("span",{"data-disabled":a.disabled,className:i[r.DropdownRoot]},c.createElement(o.Select,{className:s,...a},t?.map(({value:e,label:t,disabled:n})=>c.createElement(o.Option,{key:e,value:e,disabled:n},t))),c.createElement("span",{className:i[r.CaptionLabel],"aria-hidden":!0},l?.label,c.createElement(o.Chevron,{orientation:"down",size:18,className:i[r.Chevron]})))}function er(e){return c.createElement("div",{...e})}function eo(e){return c.createElement("div",{...e})}function ei(e){let{calendarMonth:t,displayIndex:n,...r}=e;return c.createElement("div",{...r},e.children)}function ea(e){let{calendarMonth:t,displayIndex:n,...r}=e;return c.createElement("div",{...r})}function es(e){return c.createElement("table",{...e})}function el(e){return c.createElement("div",{...e})}let ed=(0,c.createContext)(void 0);function eu(){let e=(0,c.useContext)(ed);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function ec(e){let{components:t}=eu();return c.createElement(t.Dropdown,{...e})}function ef(e){let{onPreviousClick:t,onNextClick:n,previousMonth:o,nextMonth:i,...a}=e,{components:s,classNames:l,labels:{labelPrevious:d,labelNext:u}}=eu(),f=(0,c.useCallback)(e=>{i&&n?.(e)},[i,n]),h=(0,c.useCallback)(e=>{o&&t?.(e)},[o,t]);return c.createElement("nav",{...a},c.createElement(s.PreviousMonthButton,{type:"button",className:l[r.PreviousMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":d(o),onClick:h},c.createElement(s.Chevron,{disabled:!o||void 0,className:l[r.Chevron],orientation:"left"})),c.createElement(s.NextMonthButton,{type:"button",className:l[r.NextMonthButton],tabIndex:i?void 0:-1,"aria-disabled":!i||void 0,"aria-label":u(i),onClick:f},c.createElement(s.Chevron,{disabled:!i||void 0,orientation:"right",className:l[r.Chevron]})))}function eh(e){let{components:t}=eu();return c.createElement(t.Button,{...e})}function ep(e){return c.createElement("option",{...e})}function em(e){let{components:t}=eu();return c.createElement(t.Button,{...e})}function ey(e){let{rootRef:t,...n}=e;return c.createElement("div",{...n,ref:t})}function eb(e){return c.createElement("select",{...e})}function eg(e){let{week:t,...n}=e;return c.createElement("tr",{...n})}function ev(e){return c.createElement("th",{...e})}function ex(e){return c.createElement("thead",{"aria-hidden":!0},c.createElement("tr",{...e}))}function ew(e){let{week:t,...n}=e;return c.createElement("th",{...n})}function eM(e){return c.createElement("th",{...e})}function ek(e){return c.createElement("tbody",{...e})}function eC(e){let{components:t}=eu();return c.createElement(t.Dropdown,{...e})}function eD(e,t,n){return(n??new H(t)).format(e,"LLLL y")}let eO=eD;function eS(e,t,n){return(n??new H(t)).format(e,"d")}function eT(e,t=B){return t.format(e,"LLLL")}function eE(e,t=B){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function eN(){return""}function eP(e,t,n){return(n??new H(t)).format(e,"cccccc")}function e_(e,t=B){return t.format(e,"yyyy")}let eW=e_;function eA(e,t,n){return(n??new H(t)).format(e,"LLLL y")}let eI=eA;function ej(e,t,n,r){let o=(r??new H(n)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function eR(e,t,n,r){let o=(r??new H(n)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let eY=eR;function eL(){return""}function eF(e){return"Choose the Month"}function eH(e){return"Go to the Next Month"}function eB(e){return"Go to the Previous Month"}function e$(e,t,n){return(n??new H(t)).format(e,"cccc")}function ez(e,t){return`Week ${e}`}function eX(e){return"Week Number"}function eZ(e){return"Choose the Year"}let eU=e=>e instanceof HTMLElement?e:null,eq=e=>[...e.querySelectorAll("[data-animated-month]")??[]],eV=e=>eU(e.querySelector("[data-animated-month]")),eK=e=>eU(e.querySelector("[data-animated-caption]")),eQ=e=>eU(e.querySelector("[data-animated-weeks]")),eG=e=>eU(e.querySelector("[data-animated-nav]")),eJ=e=>eU(e.querySelector("[data-animated-weekdays]"));function e0(e,t){let{month:n,defaultMonth:r,today:o=t.today(),numberOfMonths:i=1,endMonth:a,startMonth:s}=e,l=n||r||o,{differenceInCalendarMonths:d,addMonths:u,startOfMonth:c}=t;return a&&0>d(a,l)&&(l=u(a,-1*(i-1))),s&&0>d(l,s)&&(l=s),c(l)}class e1{constructor(e,t,n=B){this.date=e,this.displayMonth=t,this.outside=!!(t&&!n.isSameMonth(e,t)),this.dateLib=n}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class e2{constructor(e,t){this.days=t,this.weekNumber=e}}class e5{constructor(e,t){this.date=e,this.weeks=t}}function e8(e,t){let[n,r]=(0,c.useState)(e);return[void 0===t?n:t,r]}function e4(e){return!e[o.disabled]&&!e[o.hidden]&&!e[o.outside]}function e9(e,t,n=B){return $(e,t.from,!1,n)||$(e,t.to,!1,n)||$(t,e.from,!1,n)||$(t,e.to,!1,n)}function e3(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new w(t.today,t.timeZone)),t.month&&(t.month=new w(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new w(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new w(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new w(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new w(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new w(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new w(t.selected.from,t.timeZone):void 0,to:t.selected.to?new w(t.selected.to,t.timeZone):void 0}));let{components:n,formatters:f,labels:h,dateLib:p,locale:m,classNames:y}=(0,c.useMemo)(()=>{var e,n;let s={...M.c,...t.locale};return{dateLib:new H({locale:s,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...l,...e}),formatters:(n=t.formatters,n?.formatMonthCaption&&!n.formatCaption&&(n.formatCaption=n.formatMonthCaption),n?.formatYearCaption&&!n.formatYearDropdown&&(n.formatYearDropdown=n.formatYearCaption),{...d,...n}),labels:{...u,...t.labels},locale:s,classNames:{...function(){let e={};for(let t in r)e[r[t]]=`rdp-${r[t]}`;for(let t in o)e[o[t]]=`rdp-${o[t]}`;for(let t in i)e[i[t]]=`rdp-${i[t]}`;for(let t in a)e[a[t]]=`rdp-${a[t]}`;return e}(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:b,mode:g,navLayout:v,numberOfMonths:x=1,onDayBlur:k,onDayClick:C,onDayFocus:D,onDayKeyDown:O,onDayMouseEnter:S,onDayMouseLeave:T,onNextClick:E,onPrevClick:N,showWeekNumber:P,styles:_}=t,{formatCaption:W,formatDay:A,formatMonthDropdown:I,formatWeekNumber:j,formatWeekNumberHeader:R,formatWeekdayName:Y,formatYearDropdown:L}=f,F=function(e,t){let[n,r]=function(e,t){let{startMonth:n,endMonth:r}=e,{startOfYear:o,startOfDay:i,startOfMonth:a,endOfMonth:s,addYears:l,endOfYear:d,newDate:u,today:c}=t,{fromYear:f,toYear:h,fromMonth:p,toMonth:m}=e;!n&&p&&(n=p),!n&&f&&(n=t.newDate(f,0,1)),!r&&m&&(r=m),!r&&h&&(r=u(h,11,31));let y="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return n?n=a(n):f?n=u(f,0,1):!n&&y&&(n=o(l(e.today??c(),-100))),r?r=s(r):h?r=u(h,11,31):!r&&y&&(r=d(e.today??c())),[n?i(n):n,r?i(r):r]}(e,t),{startOfMonth:o,endOfMonth:i}=t,a=e0(e,t),[s,l]=e8(a,e.month?a:void 0);(0,c.useEffect)(()=>{l(e0(e,t))},[e.timeZone]);let d=function(e,t,n,r){let{numberOfMonths:o=1}=n,i=[];for(let n=0;n<o;n++){let o=r.addMonths(e,n);if(t&&o>t)break;i.push(o)}return i}(s,r,e,t),u=function(e,t,n,r){let o=e[0],i=e[e.length-1],{ISOWeek:a,fixedWeeks:s,broadcastCalendar:l}=n??{},{addDays:d,differenceInCalendarDays:u,differenceInCalendarMonths:c,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:p,endOfWeek:m,isAfter:y,startOfBroadcastWeek:b,startOfISOWeek:g,startOfWeek:v}=r,x=l?b(o,r):a?g(o):v(o),w=u(l?f(i):a?h(p(i)):m(p(i)),x),M=c(i,o)+1,k=[];for(let e=0;e<=w;e++){let n=d(x,e);if(t&&y(n,t))break;k.push(n)}let C=(l?35:42)*M;if(s&&k.length<C){let e=C-k.length;for(let t=0;t<e;t++){let e=d(k[k.length-1],1);k.push(e)}}return k}(d,e.endMonth?i(e.endMonth):void 0,e,t),f=function(e,t,n,r){let{addDays:o,endOfBroadcastWeek:i,endOfISOWeek:a,endOfMonth:s,endOfWeek:l,getISOWeek:d,getWeek:u,startOfBroadcastWeek:c,startOfISOWeek:f,startOfWeek:h}=r,p=e.reduce((e,p)=>{let m=n.broadcastCalendar?c(p,r):n.ISOWeek?f(p):h(p),y=n.broadcastCalendar?i(p):n.ISOWeek?a(s(p)):l(s(p)),b=t.filter(e=>e>=m&&e<=y),g=n.broadcastCalendar?35:42;if(n.fixedWeeks&&b.length<g){let e=t.filter(e=>{let t=g-b.length;return e>y&&e<=o(y,t)});b.push(...e)}let v=b.reduce((e,t)=>{let o=n.ISOWeek?d(t):u(t),i=e.find(e=>e.weekNumber===o),a=new e1(t,p,r);return i?i.days.push(a):e.push(new e2(o,[a])),e},[]),x=new e5(p,v);return e.push(x),e},[]);return n.reverseMonths?p.reverse():p}(d,u,e,t),h=f.reduce((e,t)=>[...e,...t.weeks],[]),p=function(e){let t=[];return e.reduce((e,n)=>[...e,...n.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(f),m=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i}=n,{startOfMonth:a,addMonths:s,differenceInCalendarMonths:l}=r,d=a(e);if(!t||!(0>=l(d,t)))return s(d,-(o?i??1:1))}(s,n,e,t),y=function(e,t,n,r){if(n.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:i=1}=n,{startOfMonth:a,addMonths:s,differenceInCalendarMonths:l}=r,d=a(e);if(!t||!(l(t,e)<i))return s(d,o?i:1)}(s,r,e,t),{disableNavigation:b,onMonthChange:g}=e,v=e=>h.some(t=>t.days.some(t=>t.isEqualTo(e))),x=e=>{if(b)return;let t=o(e);n&&t<o(n)&&(t=o(n)),r&&t>o(r)&&(t=o(r)),l(t),g?.(t)};return{months:f,weeks:h,days:p,navStart:n,navEnd:r,previousMonth:m,nextMonth:y,goToMonth:x,goToDay:e=>{v(e)||x(e.date)}}}(t,p),{days:Q,months:G,navStart:J,navEnd:ee,previousMonth:et,nextMonth:en,goToMonth:er}=F,eo=function(e,t,n){let{disabled:r,hidden:i,modifiers:a,showOutsideDays:s,broadcastCalendar:l,today:d}=t,{isSameDay:u,isSameMonth:c,startOfMonth:f,isBefore:h,endOfMonth:p,isAfter:m}=n,y=t.startMonth&&f(t.startMonth),b=t.endMonth&&p(t.endMonth),g={[o.focused]:[],[o.outside]:[],[o.disabled]:[],[o.hidden]:[],[o.today]:[]},v={};for(let t of e){let{date:e,displayMonth:o}=t,f=!!(o&&!c(e,o)),p=!!(y&&h(e,y)),x=!!(b&&m(e,b)),w=!!(r&&K(e,r,n)),M=!!(i&&K(e,i,n))||p||x||!l&&!s&&f||l&&!1===s&&f,k=u(e,d??n.today());f&&g.outside.push(t),w&&g.disabled.push(t),M&&g.hidden.push(t),k&&g.today.push(t),a&&Object.keys(a).forEach(r=>{let o=a?.[r];o&&K(e,o,n)&&(v[r]?v[r].push(t):v[r]=[t])})}return e=>{let t={[o.focused]:!1,[o.disabled]:!1,[o.hidden]:!1,[o.outside]:!1,[o.today]:!1},n={};for(let n in g){let r=g[n];t[n]=r.some(t=>t===e)}for(let t in v)n[t]=v[t].some(t=>t===e);return{...t,...n}}}(Q,t,p),{isSelected:ei,select:ea,selected:es}=function(e,t){let n=function(e,t){let{selected:n,required:r,onSelect:o}=e,[i,a]=e8(n,o?n:void 0),s=o?n:i,{isSameDay:l}=t;return{selected:s,select:(e,t,n)=>{let i=e;return!r&&s&&s&&l(e,s)&&(i=void 0),o||a(i),o?.(i,e,t,n),i},isSelected:e=>!!s&&l(s,e)}}(e,t),r=function(e,t){let{selected:n,required:r,onSelect:o}=e,[i,a]=e8(n,o?n:void 0),s=o?n:i,{isSameDay:l}=t,d=e=>s?.some(t=>l(t,e))??!1,{min:u,max:c}=e;return{selected:s,select:(e,t,n)=>{let i=[...s??[]];if(d(e)){if(s?.length===u||r&&s?.length===1)return;i=s?.filter(t=>!l(t,e))}else i=s?.length===c?[e]:[...i,e];return o||a(i),o?.(i,e,t,n),i},isSelected:d}}(e,t),o=function(e,t){let{disabled:n,excludeDisabled:r,selected:o,required:i,onSelect:a}=e,[s,l]=e8(o,a?o:void 0),d=a?o:s;return{selected:d,select:(o,s,u)=>{let{min:c,max:f}=e,h=o?function(e,t,n=0,r=0,o=!1,i=B){let a,{from:s,to:l}=t||{},{isSameDay:d,isAfter:u,isBefore:c}=i;if(s||l){if(s&&!l)a=d(s,e)?o?{from:s,to:void 0}:void 0:c(e,s)?{from:e,to:s}:{from:s,to:e};else if(s&&l)if(d(s,e)&&d(l,e))a=o?{from:s,to:l}:void 0;else if(d(s,e))a={from:s,to:n>0?void 0:e};else if(d(l,e))a={from:e,to:n>0?void 0:e};else if(c(e,s))a={from:e,to:l};else if(u(e,s))a={from:s,to:e};else if(u(e,l))a={from:s,to:e};else throw Error("Invalid range")}else a={from:e,to:n>0?void 0:e};if(a?.from&&a?.to){let t=i.differenceInCalendarDays(a.to,a.from);r>0&&t>r?a={from:e,to:void 0}:n>1&&t<n&&(a={from:e,to:void 0})}return a}(o,d,c,f,i,t):void 0;return r&&n&&h?.from&&h.to&&function(e,t,n=B){let r=Array.isArray(t)?t:[t];if(r.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:n.isDate(t)?$(e,t,!1,n):V(t,n)?t.some(t=>$(e,t,!1,n)):X(t)?!!t.from&&!!t.to&&e9(e,{from:t.from,to:t.to},n):q(t)?function(e,t,n=B){let r=Array.isArray(t)?t:[t],o=e.from,i=Math.min(n.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=i;e++){if(r.includes(o.getDay()))return!0;o=n.addDays(o,1)}return!1}(e,t.dayOfWeek,n):z(t)?n.isAfter(t.before,t.after)?e9(e,{from:n.addDays(t.after,1),to:n.addDays(t.before,-1)},n):K(e.from,t,n)||K(e.to,t,n):!!(Z(t)||U(t))&&(K(e.from,t,n)||K(e.to,t,n))))return!0;let o=r.filter(e=>"function"==typeof e);if(o.length){let t=e.from,r=n.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(o.some(e=>e(t)))return!0;t=n.addDays(t,1)}}return!1}({from:h.from,to:h.to},n,t)&&(h.from=o,h.to=void 0),a||l(h),a?.(h,o,s,u),h},isSelected:e=>d&&$(d,e,!1,t)}}(e,t);switch(e.mode){case"single":return n;case"multiple":return r;case"range":return o;default:return}}(t,p)??{},{blur:el,focused:eu,isFocusTarget:ec,moveFocus:ef,setFocused:eh}=function(e,t,n,r,i){let{autoFocus:a}=e,[l,d]=(0,c.useState)(),u=function(e,t,n,r){let i,a=-1;for(let l of e){let e=t(l);e4(e)&&(e[o.focused]&&a<s.FocusedModifier?(i=l,a=s.FocusedModifier):r?.isEqualTo(l)&&a<s.LastFocused?(i=l,a=s.LastFocused):n(l.date)&&a<s.Selected?(i=l,a=s.Selected):e[o.today]&&a<s.Today&&(i=l,a=s.Today))}return i||(i=e.find(e=>e4(t(e)))),i}(t.days,n,r||(()=>!1),l),[f,h]=(0,c.useState)(a?u:void 0);return{isFocusTarget:e=>!!u?.isEqualTo(e),setFocused:h,focused:f,blur:()=>{d(f),h(void 0)},moveFocus:(n,r)=>{if(!f)return;let o=function e(t,n,r,o,i,a,s,l=0){if(l>365)return;let d=function(e,t,n,r,o,i,a){let{ISOWeek:s,broadcastCalendar:l}=i,{addDays:d,addMonths:u,addWeeks:c,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:p,endOfWeek:m,max:y,min:b,startOfBroadcastWeek:g,startOfISOWeek:v,startOfWeek:x}=a,w=({day:d,week:c,month:u,year:f,startOfWeek:e=>l?g(e,a):s?v(e):x(e),endOfWeek:e=>l?h(e):s?p(e):m(e)})[e](n,"after"===t?1:-1);return"before"===t&&r?w=y([r,w]):"after"===t&&o&&(w=b([o,w])),w}(t,n,r.date,o,i,a,s),u=!!(a.disabled&&K(d,a.disabled,s)),c=!!(a.hidden&&K(d,a.hidden,s)),f=new e1(d,d,s);return u||c?e(t,n,f,o,i,a,s,l+1):f}(n,r,f,t.navStart,t.navEnd,e,i);o&&(t.goToDay(o),h(o))}}}(t,F,eo,ei??(()=>!1),p),{labelDayButton:ep,labelGridcell:em,labelGrid:ey,labelMonthDropdown:eb,labelNav:eg,labelPrevious:ev,labelNext:ex,labelWeekday:ew,labelWeekNumber:eM,labelWeekNumberHeader:ek,labelYearDropdown:eC}=h,eD=(0,c.useMemo)(()=>(function(e,t,n){let r=e.today(),o=t?e.startOfISOWeek(r):e.startOfWeek(r),i=[];for(let t=0;t<7;t++){let n=e.addDays(o,t);i.push(n)}return i})(p,t.ISOWeek),[p,t.ISOWeek]),eO=void 0!==g||void 0!==C,eS=(0,c.useCallback)(()=>{et&&(er(et),N?.(et))},[et,er,N]),eT=(0,c.useCallback)(()=>{en&&(er(en),E?.(en))},[er,en,E]),eE=(0,c.useCallback)((e,t)=>n=>{n.preventDefault(),n.stopPropagation(),eh(e),ea?.(e.date,t,n),C?.(e.date,t,n)},[ea,C,eh]),eN=(0,c.useCallback)((e,t)=>n=>{eh(e),D?.(e.date,t,n)},[D,eh]),eP=(0,c.useCallback)((e,t)=>n=>{el(),k?.(e.date,t,n)},[el,k]),e_=(0,c.useCallback)((e,n)=>r=>{let o={ArrowLeft:["day","rtl"===t.dir?"after":"before"],ArrowRight:["day","rtl"===t.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[r.key]){r.preventDefault(),r.stopPropagation();let[e,t]=o[r.key];ef(e,t)}O?.(e.date,n,r)},[ef,O,t.dir]),eW=(0,c.useCallback)((e,t)=>n=>{S?.(e.date,t,n)},[S]),eA=(0,c.useCallback)((e,t)=>n=>{T?.(e.date,t,n)},[T]),eI=(0,c.useCallback)(e=>t=>{let n=Number(t.target.value);er(p.setMonth(p.startOfMonth(e),n))},[p,er]),ej=(0,c.useCallback)(e=>t=>{let n=Number(t.target.value);er(p.setYear(p.startOfMonth(e),n))},[p,er]),{className:eR,style:eY}=(0,c.useMemo)(()=>({className:[y[r.Root],t.className].filter(Boolean).join(" "),style:{..._?.[r.Root],...t.style}}),[y,t.className,t.style,_]),eL=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,n])=>{e.startsWith("data-")&&(t[e]=n)}),t}(t),eF=(0,c.useRef)(null);!function(e,t,{classNames:n,months:r,focused:o,dateLib:i}){let s=(0,c.useRef)(null),l=(0,c.useRef)(r),d=(0,c.useRef)(!1);(0,c.useLayoutEffect)(()=>{let u=l.current;if(l.current=r,!t||!e.current||!(e.current instanceof HTMLElement)||0===r.length||0===u.length||r.length!==u.length)return;let c=i.isSameMonth(r[0].date,u[0].date),f=i.isAfter(r[0].date,u[0].date),h=f?n[a.caption_after_enter]:n[a.caption_before_enter],p=f?n[a.weeks_after_enter]:n[a.weeks_before_enter],m=s.current,y=e.current.cloneNode(!0);if(y instanceof HTMLElement?(eq(y).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=eV(e);t&&e.contains(t)&&e.removeChild(t);let n=eK(e);n&&n.classList.remove(h);let r=eQ(e);r&&r.classList.remove(p)}),s.current=y):s.current=null,d.current||c||o)return;let b=m instanceof HTMLElement?eq(m):[],g=eq(e.current);if(g&&g.every(e=>e instanceof HTMLElement)&&b&&b.every(e=>e instanceof HTMLElement)){d.current=!0;let t=[];e.current.style.isolation="isolate";let r=eG(e.current);r&&(r.style.zIndex="1"),g.forEach((o,i)=>{let s=b[i];if(!s)return;o.style.position="relative",o.style.overflow="hidden";let l=eK(o);l&&l.classList.add(h);let u=eQ(o);u&&u.classList.add(p);let c=()=>{d.current=!1,e.current&&(e.current.style.isolation=""),r&&(r.style.zIndex=""),l&&l.classList.remove(h),u&&u.classList.remove(p),o.style.position="",o.style.overflow="",o.contains(s)&&o.removeChild(s)};t.push(c),s.style.pointerEvents="none",s.style.position="absolute",s.style.overflow="hidden",s.setAttribute("aria-hidden","true");let m=eJ(s);m&&(m.style.opacity="0");let y=eK(s);y&&(y.classList.add(f?n[a.caption_before_exit]:n[a.caption_after_exit]),y.addEventListener("animationend",c));let g=eQ(s);g&&g.classList.add(f?n[a.weeks_before_exit]:n[a.weeks_after_exit]),o.insertBefore(s,o.firstChild)})}})}(eF,!!t.animate,{classNames:y,months:G,focused:eu,dateLib:p});let eH={dayPickerProps:t,selected:es,select:ea,isSelected:ei,months:G,nextMonth:en,previousMonth:et,goToMonth:er,getModifiers:eo,components:n,classNames:y,styles:_,labels:h,formatters:f};return c.createElement(ed.Provider,{value:eH},c.createElement(n.Root,{rootRef:t.animate?eF:void 0,className:eR,style:eY,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...eL},c.createElement(n.Months,{className:y[r.Months],style:_?.[r.Months]},!t.hideNavigation&&!v&&c.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[r.Nav],style:_?.[r.Nav],"aria-label":eg(),onPreviousClick:eS,onNextClick:eT,previousMonth:et,nextMonth:en}),G.map((e,a)=>{let s=function(e,t,n,r,o){let{startOfMonth:i,startOfYear:a,endOfYear:s,eachMonthOfInterval:l,getMonth:d}=o;return l({start:a(e),end:s(e)}).map(e=>{let a=r.formatMonthDropdown(e,o);return{value:d(e),label:a,disabled:t&&e<i(t)||n&&e>i(n)||!1}})}(e.date,J,ee,f,p),l=function(e,t,n,r){if(!e||!t)return;let{startOfYear:o,endOfYear:i,addYears:a,getYear:s,isBefore:l,isSameYear:d}=r,u=o(e),c=i(t),f=[],h=u;for(;l(h,c)||d(h,c);)f.push(h),h=a(h,1);return f.map(e=>{let t=n.formatYearDropdown(e,r);return{value:s(e),label:t,disabled:!1}})}(J,ee,f,p);return c.createElement(n.Month,{"data-animated-month":t.animate?"true":void 0,className:y[r.Month],style:_?.[r.Month],key:a,displayIndex:a,calendarMonth:e},"around"===v&&!t.hideNavigation&&0===a&&c.createElement(n.PreviousMonthButton,{type:"button",className:y[r.PreviousMonthButton],tabIndex:et?void 0:-1,"aria-disabled":!et||void 0,"aria-label":ev(et),onClick:eS,"data-animated-button":t.animate?"true":void 0},c.createElement(n.Chevron,{disabled:!et||void 0,className:y[r.Chevron],orientation:"rtl"===t.dir?"right":"left"})),c.createElement(n.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:y[r.MonthCaption],style:_?.[r.MonthCaption],calendarMonth:e,displayIndex:a},b?.startsWith("dropdown")?c.createElement(n.DropdownNav,{className:y[r.Dropdowns],style:_?.[r.Dropdowns]},"dropdown"===b||"dropdown-months"===b?c.createElement(n.MonthsDropdown,{className:y[r.MonthsDropdown],"aria-label":eb(),classNames:y,components:n,disabled:!!t.disableNavigation,onChange:eI(e.date),options:s,style:_?.[r.Dropdown],value:p.getMonth(e.date)}):c.createElement("span",null,I(e.date,p)),"dropdown"===b||"dropdown-years"===b?c.createElement(n.YearsDropdown,{className:y[r.YearsDropdown],"aria-label":eC(p.options),classNames:y,components:n,disabled:!!t.disableNavigation,onChange:ej(e.date),options:l,style:_?.[r.Dropdown],value:p.getYear(e.date)}):c.createElement("span",null,L(e.date,p)),c.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},W(e.date,p.options,p))):c.createElement(n.CaptionLabel,{className:y[r.CaptionLabel],role:"status","aria-live":"polite"},W(e.date,p.options,p))),"around"===v&&!t.hideNavigation&&a===x-1&&c.createElement(n.NextMonthButton,{type:"button",className:y[r.NextMonthButton],tabIndex:en?void 0:-1,"aria-disabled":!en||void 0,"aria-label":ex(en),onClick:eT,"data-animated-button":t.animate?"true":void 0},c.createElement(n.Chevron,{disabled:!en||void 0,className:y[r.Chevron],orientation:"rtl"===t.dir?"left":"right"})),a===x-1&&"after"===v&&!t.hideNavigation&&c.createElement(n.Nav,{"data-animated-nav":t.animate?"true":void 0,className:y[r.Nav],style:_?.[r.Nav],"aria-label":eg(),onPreviousClick:eS,onNextClick:eT,previousMonth:et,nextMonth:en}),c.createElement(n.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===g||"range"===g,"aria-label":ey(e.date,p.options,p)||void 0,className:y[r.MonthGrid],style:_?.[r.MonthGrid]},!t.hideWeekdays&&c.createElement(n.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:y[r.Weekdays],style:_?.[r.Weekdays]},P&&c.createElement(n.WeekNumberHeader,{"aria-label":ek(p.options),className:y[r.WeekNumberHeader],style:_?.[r.WeekNumberHeader],scope:"col"},R()),eD.map((e,t)=>c.createElement(n.Weekday,{"aria-label":ew(e,p.options,p),className:y[r.Weekday],key:t,style:_?.[r.Weekday],scope:"col"},Y(e,p.options,p)))),c.createElement(n.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:y[r.Weeks],style:_?.[r.Weeks]},e.weeks.map((e,a)=>c.createElement(n.Week,{className:y[r.Week],key:e.weekNumber,style:_?.[r.Week],week:e},P&&c.createElement(n.WeekNumber,{week:e,style:_?.[r.WeekNumber],"aria-label":eM(e.weekNumber,{locale:m}),className:y[r.WeekNumber],scope:"row",role:"rowheader"},j(e.weekNumber,p)),e.days.map(e=>{let{date:a}=e,s=eo(e);if(s[o.focused]=!s.hidden&&!!eu?.isEqualTo(e),s[i.selected]=ei?.(a)||s.selected,X(es)){let{from:e,to:t}=es;s[i.range_start]=!!(e&&t&&p.isSameDay(a,e)),s[i.range_end]=!!(e&&t&&p.isSameDay(a,t)),s[i.range_middle]=$(es,a,!0,p)}let l=function(e,t={},n={}){let o={...t?.[r.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{o={...o,...n?.[e]}}),o}(s,_,t.modifiersStyles),d=function(e,t,n={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[r])=>(n[r]?e.push(n[r]):t[o[r]]?e.push(t[o[r]]):t[i[r]]&&e.push(t[i[r]]),e),[t[r.Day]])}(s,y,t.modifiersClassNames),u=eO||s.hidden?void 0:em(a,s,p.options,p);return c.createElement(n.Day,{key:`${p.format(a,"yyyy-MM-dd")}_${p.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:s,className:d.join(" "),style:l,role:"gridcell","aria-selected":s.selected||void 0,"aria-label":u,"data-day":p.format(a,"yyyy-MM-dd"),"data-month":e.outside?p.format(a,"yyyy-MM"):void 0,"data-selected":s.selected||void 0,"data-disabled":s.disabled||void 0,"data-hidden":s.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":s.focused||void 0,"data-today":s.today||void 0},!s.hidden&&eO?c.createElement(n.DayButton,{className:y[r.DayButton],style:_?.[r.DayButton],type:"button",day:e,modifiers:s,disabled:s.disabled||void 0,tabIndex:ec(e)?0:-1,"aria-label":ep(a,s,p.options,p),onClick:eE(e,s),onBlur:eP(e,s),onFocus:eN(e,s),onKeyDown:e_(e,s),onMouseEnter:eW(e,s),onMouseLeave:eA(e,s)},A(a,p.options,p)):!s.hidden&&A(e.date,p.options,p))}))))))})),t.footer&&c.createElement(n.Footer,{className:y[r.Footer],style:_?.[r.Footer],role:"status","aria-live":"polite"},t.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(s||(s={}))},1284:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1351:(e,t,n)=>{let r;function o(e){return e+.5|0}n.d(t,{$:()=>tc,A:()=>eA,B:()=>eW,C:()=>td,D:()=>eC,E:()=>tM,F:()=>z,G:()=>tG,H:()=>eu,I:()=>tz,J:()=>t1,K:()=>t0,L:()=>eH,M:()=>t$,N:()=>eb,O:()=>L,P:()=>ei,Q:()=>$,R:()=>tD,S:()=>eE,T:()=>ea,U:()=>eM,V:()=>tr,W:()=>eN,X:()=>ti,Y:()=>tu,Z:()=>tp,_:()=>eL,a:()=>tC,a0:()=>tk,a1:()=>e$,a2:()=>ez,a3:()=>e3,a4:()=>V,a5:()=>ee,a6:()=>e6,a7:()=>en,a8:()=>function e(t,n,r,o){return new Proxy({_cacheable:!1,_proxy:t,_context:n,_subProxy:r,_stack:new Set,_descriptors:tT(t,o),setContext:n=>e(t,n,r,o),override:i=>e(t.override(i),n,r,o)},{deleteProperty:(e,n)=>(delete e[n],delete t[n],!0),get:(t,n,r)=>tP(t,n,()=>(function(t,n,r){let{_proxy:o,_context:i,_subProxy:a,_descriptors:s}=t,l=o[n];return en(l)&&s.isScriptable(n)&&(l=function(e,t,n,r){let{_proxy:o,_context:i,_subProxy:a,_stack:s}=n;if(s.has(e))throw Error("Recursion detected: "+Array.from(s).join("->")+"->"+e);s.add(e);let l=t(i,a||r);return s.delete(e),tN(e,l)&&(l=tW(o._scopes,o,e,l)),l}(n,l,t,r)),j(l)&&l.length&&(l=function(t,n,r,o){let{_proxy:i,_context:a,_subProxy:s,_descriptors:l}=r;if(void 0!==a.index&&o(t))return n[a.index%n.length];if(R(n[0])){let r=n,o=i._scopes.filter(e=>e!==r);for(let d of(n=[],r)){let r=tW(o,i,t,d);n.push(e(r,a,s&&s[t],l))}}return n}(n,l,t,s.isIndexable)),tN(n,l)&&(l=e(l,i,a&&a[n],s)),l})(t,n,r)),getOwnPropertyDescriptor:(e,n)=>e._descriptors.allKeys?Reflect.has(t,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,n),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,n)=>Reflect.has(t,n),ownKeys:()=>Reflect.ownKeys(t),set:(e,n,r)=>(t[n]=r,delete e[n],!0)})},a9:()=>tS,aA:()=>t4,aB:()=>t9,aC:()=>eX,aD:()=>t3,aE:()=>tl,aF:()=>eD,aG:()=>W,aH:()=>ex,aI:()=>ey,aJ:()=>ev,aK:()=>em,aL:()=>ek,aM:()=>e9,aN:()=>eh,aO:()=>to,aP:()=>eI,aQ:()=>e_,aa:()=>tT,ab:()=>K,ac:()=>A,ad:()=>eB,ae:()=>tJ,af:()=>ta,ag:()=>er,ah:()=>na,ai:()=>X,aj:()=>eo,ak:()=>eP,al:()=>tv,am:()=>tB,an:()=>nn,ao:()=>nt,ap:()=>t5,aq:()=>t8,ar:()=>t2,as:()=>tf,at:()=>th,au:()=>ts,av:()=>tm,aw:()=>tx,ax:()=>tw,ay:()=>ne,az:()=>eS,b:()=>j,b4:()=>ed,b5:()=>ec,b6:()=>ef,c:()=>eJ,d:()=>tn,e:()=>eQ,f:()=>J,g:()=>Y,h:()=>et,i:()=>R,j:()=>tO,k:()=>I,l:()=>eR,m:()=>H,n:()=>B,o:()=>e8,p:()=>eT,q:()=>eZ,r:()=>eF,s:()=>ep,t:()=>ew,u:()=>eY,v:()=>F,w:()=>eU,x:()=>eg,y:()=>tR,z:()=>tK});let i=(e,t,n)=>Math.max(Math.min(e,n),t);function a(e){return i(o(2.55*e),0,255)}function s(e){return i(o(255*e),0,255)}function l(e){return i(o(e/2.55)/100,0,1)}function d(e){return i(o(100*e),0,100)}let u={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},c=[..."0123456789ABCDEF"],f=e=>c[15&e],h=e=>c[(240&e)>>4]+c[15&e],p=e=>(240&e)>>4==(15&e),m=e=>p(e.r)&&p(e.g)&&p(e.b)&&p(e.a),y=(e,t)=>e<255?t(e):"",b=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function g(e,t,n){let r=t*Math.min(n,1-n),o=(t,o=(t+e/30)%12)=>n-r*Math.max(Math.min(o-3,9-o,1),-1);return[o(0),o(8),o(4)]}function v(e,t,n){let r=(r,o=(r+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[r(5),r(3),r(1)]}function x(e,t,n){let r,o=g(e,1,.5);for(t+n>1&&(r=1/(t+n),t*=r,n*=r),r=0;r<3;r++)o[r]*=1-t-n,o[r]+=t;return o}function w(e){let t,n,r,o=e.r/255,i=e.g/255,a=e.b/255,s=Math.max(o,i,a),l=Math.min(o,i,a),d=(s+l)/2;s!==l&&(r=s-l,n=d>.5?r/(2-s-l):r/(s+l),t=60*(t=o===s?(i-a)/r+6*(i<a):i===s?(a-o)/r+2:(o-i)/r+4)+.5);return[0|t,n||0,d]}function M(e,t,n,r){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,r)).map(s)}function k(e){return(e%360+360)%360}let C={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},D={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},O=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,S=e=>e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055,T=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function E(e,t,n){if(e){let r=w(e);r[t]=Math.max(0,Math.min(r[t]+r[t]*n,0===t?360:1)),e.r=(r=M(g,r,void 0,void 0))[0],e.g=r[1],e.b=r[2]}}function N(e,t){return e?Object.assign(t||{},e):e}function P(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=s(e[3]))):(t=N(e,{r:0,g:0,b:0,a:1})).a=s(t.a),t}class _{constructor(e){let t;if(e instanceof _)return e;let n=typeof e;"object"===n?t=P(e):"string"===n&&(t=function(e){var t,n=e.length;return"#"===e[0]&&(4===n||5===n?t={r:255&17*u[e[1]],g:255&17*u[e[2]],b:255&17*u[e[3]],a:5===n?17*u[e[4]]:255}:(7===n||9===n)&&(t={r:u[e[1]]<<4|u[e[2]],g:u[e[3]]<<4|u[e[4]],b:u[e[5]]<<4|u[e[6]],a:9===n?u[e[7]]<<4|u[e[8]]:255})),t}(e)||function(e){r||((r=function(){let e,t,n,r,o,i={},a=Object.keys(D),s=Object.keys(C);for(e=0;e<a.length;e++){for(t=0,r=o=a[e];t<s.length;t++)n=s[t],o=o.replace(n,C[n]);n=parseInt(D[r],16),i[o]=[n>>16&255,n>>8&255,255&n]}return i}()).transparent=[0,0,0,0]);let t=r[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:4===t.length?t[3]:255}}(e)||function(e){return"r"===e.charAt(0)?function(e){let t,n,r,o=O.exec(e),s=255;if(o){if(o[7]!==t){let e=+o[7];s=o[8]?a(e):i(255*e,0,255)}return t=+o[1],n=+o[3],r=+o[5],t=255&(o[2]?a(t):i(t,0,255)),{r:t,g:n=255&(o[4]?a(n):i(n,0,255)),b:r=255&(o[6]?a(r):i(r,0,255)),a:s}}}(e):function(e){let t,n=b.exec(e),r=255;if(!n)return;n[5]!==t&&(r=n[6]?a(+n[5]):s(+n[5]));let o=k(+n[2]),i=n[3]/100,l=n[4]/100;return{r:(t="hwb"===n[1]?M(x,o,i,l):"hsv"===n[1]?M(v,o,i,l):M(g,o,i,l))[0],g:t[1],b:t[2],a:r}}(e)}(e)),this._rgb=t,this._valid=!!t}get valid(){return this._valid}get rgb(){var e=N(this._rgb);return e&&(e.a=l(e.a)),e}set rgb(e){this._rgb=P(e)}rgbString(){var e;return this._valid?(e=this._rgb)&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${l(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`):void 0}hexString(){var e,t;return this._valid?(t=m(e=this._rgb)?f:h,e?"#"+t(e.r)+t(e.g)+t(e.b)+y(e.a,t):void 0):void 0}hslString(){return this._valid?function(e){if(!e)return;let t=w(e),n=t[0],r=d(t[1]),o=d(t[2]);return e.a<255?`hsla(${n}, ${r}%, ${o}%, ${l(e.a)})`:`hsl(${n}, ${r}%, ${o}%)`}(this._rgb):void 0}mix(e,t){if(e){let n,r=this.rgb,o=e.rgb,i=t===n?.5:t,a=2*i-1,s=r.a-o.a,l=((a*s==-1?a:(a+s)/(1+a*s))+1)/2;n=1-l,r.r=255&l*r.r+n*o.r+.5,r.g=255&l*r.g+n*o.g+.5,r.b=255&l*r.b+n*o.b+.5,r.a=i*r.a+(1-i)*o.a,this.rgb=r}return this}interpolate(e,t){return e&&(this._rgb=function(e,t,n){let r=T(l(e.r)),o=T(l(e.g)),i=T(l(e.b));return{r:s(S(r+n*(T(l(t.r))-r))),g:s(S(o+n*(T(l(t.g))-o))),b:s(S(i+n*(T(l(t.b))-i))),a:e.a+n*(t.a-e.a)}}(this._rgb,e._rgb,t)),this}clone(){return new _(this.rgb)}alpha(e){return this._rgb.a=s(e),this}clearer(e){let t=this._rgb;return t.a*=1-e,this}greyscale(){let e=this._rgb,t=o(.3*e.r+.59*e.g+.11*e.b);return e.r=e.g=e.b=t,this}opaquer(e){let t=this._rgb;return t.a*=1+e,this}negate(){let e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return E(this._rgb,2,e),this}darken(e){return E(this._rgb,2,-e),this}saturate(e){return E(this._rgb,1,e),this}desaturate(e){return E(this._rgb,1,-e),this}rotate(e){var t,n;return t=this._rgb,(n=w(t))[0]=k(n[0]+e),t.r=(n=M(g,n,void 0,void 0))[0],t.g=n[1],t.b=n[2],this}}function W(){}let A=(()=>{let e=0;return()=>e++})();function I(e){return null==e}function j(e){if(Array.isArray&&Array.isArray(e))return!0;let t=Object.prototype.toString.call(e);return"[object"===t.slice(0,7)&&"Array]"===t.slice(-6)}function R(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function Y(e){return("number"==typeof e||e instanceof Number)&&isFinite(+e)}function L(e,t){return Y(e)?e:t}function F(e,t){return void 0===e?t:e}let H=(e,t)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100:e/t,B=(e,t)=>"string"==typeof e&&e.endsWith("%")?parseFloat(e)/100*t:+e;function $(e,t,n){if(e&&"function"==typeof e.call)return e.apply(n,t)}function z(e,t,n,r){let o,i,a;if(j(e))if(i=e.length,r)for(o=i-1;o>=0;o--)t.call(n,e[o],o);else for(o=0;o<i;o++)t.call(n,e[o],o);else if(R(e))for(o=0,i=(a=Object.keys(e)).length;o<i;o++)t.call(n,e[a[o]],a[o])}function X(e,t){let n,r,o,i;if(!e||!t||e.length!==t.length)return!1;for(n=0,r=e.length;n<r;++n)if(o=e[n],i=t[n],o.datasetIndex!==i.datasetIndex||o.index!==i.index)return!1;return!0}function Z(e){if(j(e))return e.map(Z);if(R(e)){let t=Object.create(null),n=Object.keys(e),r=n.length,o=0;for(;o<r;++o)t[n[o]]=Z(e[n[o]]);return t}return e}function U(e){return -1===["__proto__","prototype","constructor"].indexOf(e)}function q(e,t,n,r){if(!U(e))return;let o=t[e],i=n[e];R(o)&&R(i)?V(o,i,r):t[e]=Z(i)}function V(e,t,n){let r,o=j(t)?t:[t],i=o.length;if(!R(e))return e;let a=(n=n||{}).merger||q;for(let t=0;t<i;++t){if(!R(r=o[t]))continue;let i=Object.keys(r);for(let t=0,o=i.length;t<o;++t)a(i[t],e,r,n)}return e}function K(e,t){return V(e,t,{merger:Q})}function Q(e,t,n){if(!U(e))return;let r=t[e],o=n[e];R(r)&&R(o)?K(r,o):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=Z(o))}let G={"":e=>e,x:e=>e.x,y:e=>e.y};function J(e,t){return(G[t]||(G[t]=function(e){let t=function(e){let t=e.split("."),n=[],r="";for(let e of t)(r+=e).endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}(e);return e=>{for(let n of t){if(""===n)break;e=e&&e[n]}return e}}(t)))(e)}function ee(e){return e.charAt(0).toUpperCase()+e.slice(1)}let et=e=>void 0!==e,en=e=>"function"==typeof e,er=(e,t)=>{if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0};function eo(e){return"mouseup"===e.type||"click"===e.type||"contextmenu"===e.type}let ei=Math.PI,ea=2*ei,es=ea+ei,el=Number.POSITIVE_INFINITY,ed=ei/180,eu=ei/2,ec=ei/4,ef=2*ei/3,eh=Math.log10,ep=Math.sign;function em(e,t,n){return Math.abs(e-t)<n}function ey(e){let t=Math.round(e),n=Math.pow(10,Math.floor(eh(e=em(e,t,e/1e3)?t:e))),r=e/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function eb(e){let t,n=[],r=Math.sqrt(e);for(t=1;t<r;t++)e%t==0&&(n.push(t),n.push(e/t));return r===(0|r)&&n.push(r),n.sort((e,t)=>e-t).pop(),n}function eg(e){return"symbol"!=typeof e&&("object"!=typeof e||null===e||!!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e))&&!isNaN(parseFloat(e))&&isFinite(e)}function ev(e,t){let n=Math.round(e);return n-t<=e&&n+t>=e}function ex(e,t,n){let r,o,i;for(r=0,o=e.length;r<o;r++)isNaN(i=e[r][n])||(t.min=Math.min(t.min,i),t.max=Math.max(t.max,i))}function ew(e){return ei/180*e}function eM(e){return 180/ei*e}function ek(e){if(!Y(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function eC(e,t){let n=t.x-e.x,r=t.y-e.y,o=Math.sqrt(n*n+r*r),i=Math.atan2(r,n);return i<-.5*ei&&(i+=ea),{angle:i,distance:o}}function eD(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function eO(e,t){return(e-t+es)%ea-ei}function eS(e){return(e%ea+ea)%ea}function eT(e,t,n,r){let o=eS(e),i=eS(t),a=eS(n),s=eS(i-o),l=eS(a-o),d=eS(o-i),u=eS(o-a);return o===i||o===a||r&&i===a||s>l&&d<u}function eE(e,t,n){return Math.max(t,Math.min(n,e))}function eN(e){return eE(e,-32768,32767)}function eP(e,t,n,r=1e-6){return e>=Math.min(t,n)-r&&e<=Math.max(t,n)+r}function e_(e,t,n){let r;n=n||(n=>e[n]<t);let o=e.length-1,i=0;for(;o-i>1;)n(r=i+o>>1)?i=r:o=r;return{lo:i,hi:o}}let eW=(e,t,n,r)=>e_(e,n,r?r=>{let o=e[r][t];return o<n||o===n&&e[r+1][t]===n}:r=>e[r][t]<n),eA=(e,t,n)=>e_(e,n,r=>e[r][t]>=n);function eI(e,t,n){let r=0,o=e.length;for(;r<o&&e[r]<t;)r++;for(;o>r&&e[o-1]>n;)o--;return r>0||o<e.length?e.slice(r,o):e}let ej=["push","pop","shift","splice","unshift"];function eR(e,t){if(e._chartjs)return void e._chartjs.listeners.push(t);Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),ej.forEach(t=>{let n="_onData"+ee(t),r=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...t){let o=r.apply(this,t);return e._chartjs.listeners.forEach(e=>{"function"==typeof e[n]&&e[n](...t)}),o}})})}function eY(e,t){let n=e._chartjs;if(!n)return;let r=n.listeners,o=r.indexOf(t);-1!==o&&r.splice(o,1),r.length>0||(ej.forEach(t=>{delete e[t]}),delete e._chartjs)}function eL(e){let t=new Set(e);return t.size===e.length?e:Array.from(t)}let eF="undefined"==typeof window?function(e){return e()}:window.requestAnimationFrame;function eH(e,t){let n=[],r=!1;return function(...o){n=o,r||(r=!0,eF.call(window,()=>{r=!1,e.apply(t,n)}))}}function eB(e,t){let n;return function(...r){return t?(clearTimeout(n),n=setTimeout(e,t,r)):e.apply(this,r),t}}let e$=e=>"start"===e?"left":"end"===e?"right":"center",ez=(e,t,n)=>"start"===e?t:"end"===e?n:(t+n)/2,eX=(e,t,n,r)=>e===(r?"left":"right")?n:"center"===e?(t+n)/2:t;function eZ(e,t,n){let r=t.length,o=0,i=r;if(e._sorted){let{iScale:a,vScale:s,_parsed:l}=e,d=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,u=a.axis,{min:c,max:f,minDefined:h,maxDefined:p}=a.getUserBounds();if(h){if(o=Math.min(eW(l,u,c).lo,n?r:eW(t,u,a.getPixelForValue(c)).lo),d){let e=l.slice(0,o+1).reverse().findIndex(e=>!I(e[s.axis]));o-=Math.max(0,e)}o=eE(o,0,r-1)}if(p){let e=Math.max(eW(l,a.axis,f,!0).hi+1,n?0:eW(t,u,a.getPixelForValue(f),!0).hi+1);if(d){let t=l.slice(e-1).findIndex(e=>!I(e[s.axis]));e+=Math.max(0,t)}i=eE(e,o,r)-o}else i=r-o}return{start:o,count:i}}function eU(e){let{xScale:t,yScale:n,_scaleRanges:r}=e,o={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!r)return e._scaleRanges=o,!0;let i=r.xmin!==t.min||r.xmax!==t.max||r.ymin!==n.min||r.ymax!==n.max;return Object.assign(r,o),i}let eq=e=>0===e||1===e,eV=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*ea/n)),eK=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*ea/n)+1,eQ={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*eu)+1,easeOutSine:e=>Math.sin(e*eu),easeInOutSine:e=>-.5*(Math.cos(ei*e)-1),easeInExpo:e=>0===e?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>1===e?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>eq(e)?e:e<.5?.5*Math.pow(2,10*(2*e-1)):.5*(-Math.pow(2,-10*(2*e-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>eq(e)?e:eV(e,.075,.3),easeOutElastic:e=>eq(e)?e:eK(e,.075,.3),easeInOutElastic:e=>eq(e)?e:e<.5?.5*eV(2*e,.1125,.45):.5+.5*eK(2*e-1,.1125,.45),easeInBack:e=>e*e*(2.70158*e-1.70158),easeOutBack:e=>(e-=1)*e*(2.70158*e********)+1,easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-eQ.easeOutBounce(1-e),easeOutBounce:e=>e<.36363636363636365?7.5625*e*e:e<.7272727272727273?7.5625*(e-=.5454545454545454)*e+.75:e<.9090909090909091?7.5625*(e-=.8181818181818182)*e+.9375:7.5625*(e-=.9545454545454546)*e+.984375,easeInOutBounce:e=>e<.5?.5*eQ.easeInBounce(2*e):.5*eQ.easeOutBounce(2*e-1)+.5};function eG(e){if(e&&"object"==typeof e){let t=e.toString();return"[object CanvasPattern]"===t||"[object CanvasGradient]"===t}return!1}function eJ(e){return eG(e)?e:new _(e)}function e0(e){return eG(e)?e:new _(e).saturate(.5).darken(.1).hexString()}let e1=["x","y","borderWidth","radius","tension"],e2=["color","borderColor","backgroundColor"],e5=new Map;function e8(e,t,n){return(function(e,t){let n=e+JSON.stringify(t=t||{}),r=e5.get(n);return r||(r=new Intl.NumberFormat(e,t),e5.set(n,r)),r})(t,n).format(e)}let e4={values:e=>j(e)?e:""+e,numeric(e,t,n){let r;if(0===e)return"0";let o=this.chart.options.locale,i=e;if(n.length>1){var a,s;let t,o=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(o<1e-4||o>1e15)&&(r="scientific"),a=e,Math.abs(t=(s=n).length>3?s[2].value-s[1].value:s[1].value-s[0].value)>=1&&a!==Math.floor(a)&&(t=a-Math.floor(a)),i=t}let l=eh(Math.abs(i)),d=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),u={notation:r,minimumFractionDigits:d,maximumFractionDigits:d};return Object.assign(u,this.options.ticks.format),e8(e,o,u)},logarithmic(e,t,n){return 0===e?"0":[1,2,3,5,10,15].includes(n[t].significand||e/Math.pow(10,Math.floor(eh(e))))||t>.8*n.length?e4.numeric.call(this,e,t,n):""}};var e9={formatters:e4};let e3=Object.create(null),e6=Object.create(null);function e7(e,t){if(!t)return e;let n=t.split(".");for(let t=0,r=n.length;t<r;++t){let r=n[t];e=e[r]||(e[r]=Object.create(null))}return e}function te(e,t,n){return"string"==typeof t?V(e7(e,t),n):V(e7(e,""),t)}class tt{constructor(e,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,t)=>e0(t.backgroundColor),this.hoverBorderColor=(e,t)=>e0(t.borderColor),this.hoverColor=(e,t)=>e0(t.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return te(this,e,t)}get(e){return e7(this,e)}describe(e,t){return te(e6,e,t)}override(e,t){return te(e3,e,t)}route(e,t,n,r){let o=e7(this,e),i=e7(this,n),a="_"+t;Object.defineProperties(o,{[a]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){let e=this[a],t=i[r];return R(e)?Object.assign({},t,e):F(e,t)},set(e){this[a]=e}}})}apply(e){e.forEach(e=>e(this))}}var tn=new tt({_scriptable:e=>!e.startsWith("on"),_indexable:e=>"events"!==e,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>"onProgress"!==e&&"onComplete"!==e&&"fn"!==e}),e.set("animations",{colors:{type:"color",properties:e2},numbers:{type:"number",properties:e1}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>0|e}}}})},function(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:e9.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&"callback"!==e&&"parser"!==e,_indexable:e=>"borderDash"!==e&&"tickBorderDash"!==e&&"dash"!==e}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:e=>"backdropPadding"!==e&&"callback"!==e,_indexable:e=>"backdropPadding"!==e})}]);function tr(e,t,n,r,o){let i=t[o];return i||(i=t[o]=e.measureText(o).width,n.push(o)),i>r&&(r=i),r}function to(e,t,n,r){let o,i,a,s,l,d=(r=r||{}).data=r.data||{},u=r.garbageCollect=r.garbageCollect||[];r.font!==t&&(d=r.data={},u=r.garbageCollect=[],r.font=t),e.save(),e.font=t;let c=0,f=n.length;for(o=0;o<f;o++)if(null==(s=n[o])||j(s)){if(j(s))for(i=0,a=s.length;i<a;i++)null==(l=s[i])||j(l)||(c=tr(e,d,u,c,l))}else c=tr(e,d,u,c,s);e.restore();let h=u.length/2;if(h>n.length){for(o=0;o<h;o++)delete d[u[o]];u.splice(0,h)}return c}function ti(e,t,n){let r=e.currentDevicePixelRatio,o=0!==n?Math.max(n/2,.5):0;return Math.round((t-o)*r)/r+o}function ta(e,t){(t||e)&&((t=t||e.getContext("2d")).save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function ts(e,t,n,r){tl(e,t,n,r,null)}function tl(e,t,n,r,o){let i,a,s,l,d,u,c,f,h=t.pointStyle,p=t.rotation,m=t.radius,y=(p||0)*ed;if(h&&"object"==typeof h&&("[object HTMLImageElement]"===(i=h.toString())||"[object HTMLCanvasElement]"===i)){e.save(),e.translate(n,r),e.rotate(y),e.drawImage(h,-h.width/2,-h.height/2,h.width,h.height),e.restore();return}if(!isNaN(m)&&!(m<=0)){switch(e.beginPath(),h){default:o?e.ellipse(n,r,o/2,m,0,0,ea):e.arc(n,r,m,0,ea),e.closePath();break;case"triangle":u=o?o/2:m,e.moveTo(n+Math.sin(y)*u,r-Math.cos(y)*m),y+=ef,e.lineTo(n+Math.sin(y)*u,r-Math.cos(y)*m),y+=ef,e.lineTo(n+Math.sin(y)*u,r-Math.cos(y)*m),e.closePath();break;case"rectRounded":d=.516*m,a=Math.cos(y+ec)*(l=m-d),c=Math.cos(y+ec)*(o?o/2-d:l),s=Math.sin(y+ec)*l,f=Math.sin(y+ec)*(o?o/2-d:l),e.arc(n-c,r-s,d,y-ei,y-eu),e.arc(n+f,r-a,d,y-eu,y),e.arc(n+c,r+s,d,y,y+eu),e.arc(n-f,r+a,d,y+eu,y+ei),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,u=o?o/2:l,e.rect(n-u,r-l,2*u,2*l);break}y+=ec;case"rectRot":c=Math.cos(y)*(o?o/2:m),a=Math.cos(y)*m,s=Math.sin(y)*m,f=Math.sin(y)*(o?o/2:m),e.moveTo(n-c,r-s),e.lineTo(n+f,r-a),e.lineTo(n+c,r+s),e.lineTo(n-f,r+a),e.closePath();break;case"crossRot":y+=ec;case"cross":c=Math.cos(y)*(o?o/2:m),a=Math.cos(y)*m,s=Math.sin(y)*m,f=Math.sin(y)*(o?o/2:m),e.moveTo(n-c,r-s),e.lineTo(n+c,r+s),e.moveTo(n+f,r-a),e.lineTo(n-f,r+a);break;case"star":c=Math.cos(y)*(o?o/2:m),a=Math.cos(y)*m,s=Math.sin(y)*m,f=Math.sin(y)*(o?o/2:m),e.moveTo(n-c,r-s),e.lineTo(n+c,r+s),e.moveTo(n+f,r-a),e.lineTo(n-f,r+a),y+=ec,c=Math.cos(y)*(o?o/2:m),a=Math.cos(y)*m,s=Math.sin(y)*m,f=Math.sin(y)*(o?o/2:m),e.moveTo(n-c,r-s),e.lineTo(n+c,r+s),e.moveTo(n+f,r-a),e.lineTo(n-f,r+a);break;case"line":a=o?o/2:Math.cos(y)*m,s=Math.sin(y)*m,e.moveTo(n-a,r-s),e.lineTo(n+a,r+s);break;case"dash":e.moveTo(n,r),e.lineTo(n+Math.cos(y)*(o?o/2:m),r+Math.sin(y)*m);break;case!1:e.closePath()}e.fill(),t.borderWidth>0&&e.stroke()}}function td(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function tu(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function tc(e){e.restore()}function tf(e,t,n,r,o){if(!t)return e.lineTo(n.x,n.y);if("middle"===o){let r=(t.x+n.x)/2;e.lineTo(r,t.y),e.lineTo(r,n.y)}else"after"===o!=!!r?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function th(e,t,n,r){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(r?t.cp1x:t.cp2x,r?t.cp1y:t.cp2y,r?n.cp2x:n.cp1x,r?n.cp2y:n.cp1y,n.x,n.y)}function tp(e,t,n,r,o,i={}){let a,s,l=j(t)?t:[t],d=i.strokeWidth>0&&""!==i.strokeColor;for(e.save(),e.font=o.string,i.translation&&e.translate(i.translation[0],i.translation[1]),I(i.rotation)||e.rotate(i.rotation),i.color&&(e.fillStyle=i.color),i.textAlign&&(e.textAlign=i.textAlign),i.textBaseline&&(e.textBaseline=i.textBaseline),a=0;a<l.length;++a)s=l[a],i.backdrop&&function(e,t){let n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}(e,i.backdrop),d&&(i.strokeColor&&(e.strokeStyle=i.strokeColor),I(i.strokeWidth)||(e.lineWidth=i.strokeWidth),e.strokeText(s,n,r,i.maxWidth)),e.fillText(s,n,r,i.maxWidth),function(e,t,n,r,o){if(o.strikethrough||o.underline){let i=e.measureText(r),a=t-i.actualBoundingBoxLeft,s=t+i.actualBoundingBoxRight,l=n-i.actualBoundingBoxAscent,d=n+i.actualBoundingBoxDescent,u=o.strikethrough?(l+d)/2:d;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=o.decorationWidth||2,e.moveTo(a,u),e.lineTo(s,u),e.stroke()}}(e,n,r,s,i),r+=Number(o.lineHeight);e.restore()}function tm(e,t){let{x:n,y:r,w:o,h:i,radius:a}=t;e.arc(n+a.topLeft,r+a.topLeft,a.topLeft,1.5*ei,ei,!0),e.lineTo(n,r+i-a.bottomLeft),e.arc(n+a.bottomLeft,r+i-a.bottomLeft,a.bottomLeft,ei,eu,!0),e.lineTo(n+o-a.bottomRight,r+i),e.arc(n+o-a.bottomRight,r+i-a.bottomRight,a.bottomRight,eu,0,!0),e.lineTo(n+o,r+a.topRight),e.arc(n+o-a.topRight,r+a.topRight,a.topRight,0,-eu,!0),e.lineTo(n+a.topLeft,r)}let ty=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,tb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,tg=e=>+e||0;function tv(e,t){let n={},r=R(t),o=r?Object.keys(t):t,i=R(e)?r?n=>F(e[n],e[t[n]]):t=>e[t]:()=>e;for(let e of o)n[e]=tg(i(e));return n}function tx(e){return tv(e,{top:"y",right:"x",bottom:"y",left:"x"})}function tw(e){return tv(e,["topLeft","topRight","bottomLeft","bottomRight"])}function tM(e){let t=tx(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function tk(e,t){e=e||{},t=t||tn.font;let n=F(e.size,t.size);"string"==typeof n&&(n=parseInt(n,10));let r=F(e.style,t.style);r&&!(""+r).match(tb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);let o={family:F(e.family,t.family),lineHeight:function(e,t){let n=(""+e).match(ty);if(!n||"normal"===n[1])return 1.2*t;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100}return t*e}(F(e.lineHeight,t.lineHeight),n),size:n,style:r,weight:F(e.weight,t.weight),string:""};return o.string=!o||I(o.size)||I(o.family)?null:(o.style?o.style+" ":"")+(o.weight?o.weight+" ":"")+o.size+"px "+o.family,o}function tC(e,t,n,r){let o,i,a,s=!0;for(o=0,i=e.length;o<i;++o)if(void 0!==(a=e[o])&&(void 0!==t&&"function"==typeof a&&(a=a(t),s=!1),void 0!==n&&j(a)&&(a=a[n%a.length],s=!1),void 0!==a))return r&&!s&&(r.cacheable=!1),a}function tD(e,t,n){let{min:r,max:o}=e,i=B(t,(o-r)/2),a=(e,t)=>n&&0===e?0:e+t;return{min:a(r,-Math.abs(i)),max:a(o,i)}}function tO(e,t){return Object.assign(Object.create(e),t)}function tS(e,t=[""],n,r,o=()=>e[0]){let i=n||e;return void 0===r&&(r=tI("_fallback",e)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:i,_fallback:r,_getTarget:o,override:n=>tS([n,...e],t,i,r)},{deleteProperty:(t,n)=>(delete t[n],delete t._keys,delete e[0][n],!0),get:(n,r)=>tP(n,r,()=>(function(e,t,n,r){let o;for(let i of t)if(void 0!==(o=tI(tE(i,e),n)))return tN(e,o)?tW(n,r,e,o):o})(r,t,e,n)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e._scopes[0],t),getPrototypeOf:()=>Reflect.getPrototypeOf(e[0]),has:(e,t)=>tj(e).includes(t),ownKeys:e=>tj(e),set(e,t,n){let r=e._storage||(e._storage=o());return e[t]=r[t]=n,delete e._keys,!0}})}function tT(e,t={scriptable:!0,indexable:!0}){let{_scriptable:n=t.scriptable,_indexable:r=t.indexable,_allKeys:o=t.allKeys}=e;return{allKeys:o,scriptable:n,indexable:r,isScriptable:en(n)?n:()=>n,isIndexable:en(r)?r:()=>r}}let tE=(e,t)=>e?e+ee(t):t,tN=(e,t)=>R(t)&&"adapters"!==e&&(null===Object.getPrototypeOf(t)||t.constructor===Object);function tP(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||"constructor"===t)return e[t];let r=n();return e[t]=r,r}let t_=(e,t)=>!0===e?t:"string"==typeof e?J(t,e):void 0;function tW(e,t,n,r){var o;let i=t._rootScopes,a=(o=t._fallback,en(o)?o(n,r):o),s=[...e,...i],l=new Set;l.add(r);let d=tA(l,s,n,a||n,r);return null!==d&&(void 0===a||a===n||null!==(d=tA(l,s,a,d,r)))&&tS(Array.from(l),[""],i,a,()=>(function(e,t,n){let r=e._getTarget();t in r||(r[t]={});let o=r[t];return j(o)&&R(n)?n:o||{}})(t,n,r))}function tA(e,t,n,r,o){for(;n;)n=function(e,t,n,r,o){for(let a of t){let t=t_(n,a);if(t){var i;e.add(t);let a=(i=t._fallback,en(i)?i(n,o):i);if(void 0!==a&&a!==n&&a!==r)return a}else if(!1===t&&void 0!==r&&n!==r)return null}return!1}(e,t,n,r,o);return n}function tI(e,t){for(let n of t){if(!n)continue;let t=n[e];if(void 0!==t)return t}}function tj(e){let t=e._keys;return t||(t=e._keys=function(e){let t=new Set;for(let n of e)for(let e of Object.keys(n).filter(e=>!e.startsWith("_")))t.add(e);return Array.from(t)}(e._scopes)),t}function tR(e,t,n,r){let o,i,a,{iScale:s}=e,{key:l="r"}=this._parsing,d=Array(r);for(o=0;o<r;++o)a=t[i=o+n],d[o]={r:s.parse(J(a,l),i)};return d}let tY=Number.EPSILON||1e-14,tL=(e,t)=>t<e.length&&!e[t].skip&&e[t],tF=e=>"x"===e?"y":"x";function tH(e,t,n){return Math.max(Math.min(e,n),t)}function tB(e,t,n,r,o){let i,a,s,l;if(t.spanGaps&&(e=e.filter(e=>!e.skip)),"monotone"===t.cubicInterpolationMode)!function(e,t="x"){let n,r,o,i=tF(t),a=e.length,s=Array(a).fill(0),l=Array(a),d=tL(e,0);for(n=0;n<a;++n)if(r=o,o=d,d=tL(e,n+1),o){if(d){let e=d[t]-o[t];s[n]=0!==e?(d[i]-o[i])/e:0}l[n]=r?d?ep(s[n-1])!==ep(s[n])?0:(s[n-1]+s[n])/2:s[n-1]:s[n]}!function(e,t,n){let r,o,i,a,s,l=e.length,d=tL(e,0);for(let u=0;u<l-1;++u)if(s=d,d=tL(e,u+1),s&&d){if(em(t[u],0,tY)){n[u]=n[u+1]=0;continue}(a=Math.pow(r=n[u]/t[u],2)+Math.pow(o=n[u+1]/t[u],2))<=9||(i=3/Math.sqrt(a),n[u]=r*i*t[u],n[u+1]=o*i*t[u])}}(e,s,l),function(e,t,n="x"){let r,o,i,a=tF(n),s=e.length,l=tL(e,0);for(let d=0;d<s;++d){if(o=i,i=l,l=tL(e,d+1),!i)continue;let s=i[n],u=i[a];o&&(r=(s-o[n])/3,i[`cp1${n}`]=s-r,i[`cp1${a}`]=u-r*t[d]),l&&(r=(l[n]-s)/3,i[`cp2${n}`]=s+r,i[`cp2${a}`]=u+r*t[d])}}(e,l,t)}(e,o);else{let n=r?e[e.length-1]:e[0];for(i=0,a=e.length;i<a;++i)l=function(e,t,n,r){let o=e.skip?t:e,i=n.skip?t:n,a=eD(t,o),s=eD(i,t),l=a/(a+s),d=s/(a+s);l=isNaN(l)?0:l,d=isNaN(d)?0:d;let u=r*l,c=r*d;return{previous:{x:t.x-u*(i.x-o.x),y:t.y-u*(i.y-o.y)},next:{x:t.x+c*(i.x-o.x),y:t.y+c*(i.y-o.y)}}}(n,s=e[i],e[Math.min(i+1,a-!r)%a],t.tension),s.cp1x=l.previous.x,s.cp1y=l.previous.y,s.cp2x=l.next.x,s.cp2y=l.next.y,n=s}t.capBezierPoints&&function(e,t){let n,r,o,i,a,s=td(e[0],t);for(n=0,r=e.length;n<r;++n)a=i,i=s,s=n<r-1&&td(e[n+1],t),i&&(o=e[n],a&&(o.cp1x=tH(o.cp1x,t.left,t.right),o.cp1y=tH(o.cp1y,t.top,t.bottom)),s&&(o.cp2x=tH(o.cp2x,t.left,t.right),o.cp2y=tH(o.cp2y,t.top,t.bottom)))}(e,n)}function t$(){return"undefined"!=typeof window&&"undefined"!=typeof document}function tz(e){let t=e.parentNode;return t&&"[object ShadowRoot]"===t.toString()&&(t=t.host),t}function tX(e,t,n){let r;return"string"==typeof e?(r=parseInt(e,10),-1!==e.indexOf("%")&&(r=r/100*t.parentNode[n])):r=e,r}let tZ=e=>e.ownerDocument.defaultView.getComputedStyle(e,null),tU=["top","right","bottom","left"];function tq(e,t,n){let r={};n=n?"-"+n:"";for(let o=0;o<4;o++){let i=tU[o];r[i]=parseFloat(e[t+"-"+i+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}let tV=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function tK(e,t){if("native"in e)return e;let{canvas:n,currentDevicePixelRatio:r}=t,o=tZ(n),i="border-box"===o.boxSizing,a=tq(o,"padding"),s=tq(o,"border","width"),{x:l,y:d,box:u}=function(e,t){let n,r,o=e.touches,i=o&&o.length?o[0]:e,{offsetX:a,offsetY:s}=i,l=!1;if(tV(a,s,e.target))n=a,r=s;else{let e=t.getBoundingClientRect();n=i.clientX-e.left,r=i.clientY-e.top,l=!0}return{x:n,y:r,box:l}}(e,n),c=a.left+(u&&s.left),f=a.top+(u&&s.top),{width:h,height:p}=t;return i&&(h-=a.width+s.width,p-=a.height+s.height),{x:Math.round((l-c)/h*n.width/r),y:Math.round((d-f)/p*n.height/r)}}let tQ=e=>Math.round(10*e)/10;function tG(e,t,n,r){let o=tZ(e),i=tq(o,"margin"),a=tX(o.maxWidth,e,"clientWidth")||el,s=tX(o.maxHeight,e,"clientHeight")||el,l=function(e,t,n){let r,o;if(void 0===t||void 0===n){let i=e&&tz(e);if(i){let e=i.getBoundingClientRect(),a=tZ(i),s=tq(a,"border","width"),l=tq(a,"padding");t=e.width-l.width-s.width,n=e.height-l.height-s.height,r=tX(a.maxWidth,i,"clientWidth"),o=tX(a.maxHeight,i,"clientHeight")}else t=e.clientWidth,n=e.clientHeight}return{width:t,height:n,maxWidth:r||el,maxHeight:o||el}}(e,t,n),{width:d,height:u}=l;if("content-box"===o.boxSizing){let e=tq(o,"border","width"),t=tq(o,"padding");d-=t.width+e.width,u-=t.height+e.height}return d=Math.max(0,d-i.width),u=Math.max(0,r?d/r:u-i.height),d=tQ(Math.min(d,a,l.maxWidth)),u=tQ(Math.min(u,s,l.maxHeight)),d&&!u&&(u=tQ(d/2)),(void 0!==t||void 0!==n)&&r&&l.height&&u>l.height&&(d=tQ(Math.floor((u=l.height)*r))),{width:d,height:u}}function tJ(e,t,n){let r=t||1,o=Math.floor(e.height*r),i=Math.floor(e.width*r);e.height=Math.floor(e.height),e.width=Math.floor(e.width);let a=e.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${e.height}px`,a.style.width=`${e.width}px`),(e.currentDevicePixelRatio!==r||a.height!==o||a.width!==i)&&(e.currentDevicePixelRatio=r,a.height=o,a.width=i,e.ctx.setTransform(r,0,0,r,0,0),!0)}let t0=function(){let e=!1;try{let t={get passive(){return e=!0,!1}};t$()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch(e){}return e}();function t1(e,t){let n=tZ(e).getPropertyValue(t),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}function t2(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function t5(e,t,n,r){return{x:e.x+n*(t.x-e.x),y:"middle"===r?n<.5?e.y:t.y:"after"===r?n<1?e.y:t.y:n>0?t.y:e.y}}function t8(e,t,n,r){let o={x:e.cp2x,y:e.cp2y},i={x:t.cp1x,y:t.cp1y},a=t2(e,o,n),s=t2(o,i,n),l=t2(i,t,n),d=t2(a,s,n),u=t2(s,l,n);return t2(d,u,n)}function t4(e,t,n){var r;return e?(r=n,{x:e=>t+t+r-e,setWidth(e){r=e},textAlign:e=>"center"===e?e:"right"===e?"left":"right",xPlus:(e,t)=>e-t,leftForLtr:(e,t)=>e-t}):{x:e=>e,setWidth(e){},textAlign:e=>e,xPlus:(e,t)=>e+t,leftForLtr:(e,t)=>e}}function t9(e,t){let n,r;("ltr"===t||"rtl"===t)&&(r=[(n=e.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=r)}function t3(e,t){void 0!==t&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function t6(e){return"angle"===e?{between:eT,compare:eO,normalize:eS}:{between:eP,compare:(e,t)=>e-t,normalize:e=>e}}function t7({start:e,end:t,count:n,loop:r,style:o}){return{start:e%n,end:t%n,loop:r&&(t-e+1)%n==0,style:o}}function ne(e,t,n){let r,o,i;if(!n)return[e];let{property:a,start:s,end:l}=n,d=t.length,{compare:u,between:c,normalize:f}=t6(a),{start:h,end:p,loop:m,style:y}=function(e,t,n){let r,{property:o,start:i,end:a}=n,{between:s,normalize:l}=t6(o),d=t.length,{start:u,end:c,loop:f}=e;if(f){for(u+=d,c+=d,r=0;r<d&&s(l(t[u%d][o]),i,a);++r)u--,c--;u%=d,c%=d}return c<u&&(c+=d),{start:u,end:c,loop:f,style:e.style}}(e,t,n),b=[],g=!1,v=null,x=()=>c(s,i,r)&&0!==u(s,i),w=()=>0===u(l,r)||c(l,i,r),M=()=>g||x(),k=()=>!g||w();for(let e=h,n=h;e<=p;++e)(o=t[e%d]).skip||(r=f(o[a]))!==i&&(g=c(r,s,l),null===v&&M()&&(v=0===u(r,s)?e:n),null!==v&&k()&&(b.push(t7({start:v,end:e,loop:m,count:d,style:y})),v=null),n=e,i=r);return null!==v&&b.push(t7({start:v,end:p,loop:m,count:d,style:y})),b}function nt(e,t){let n=[],r=e.segments;for(let o=0;o<r.length;o++){let i=ne(r[o],e.points,t);i.length&&n.push(...i)}return n}function nn(e,t){let n=e.points,r=e.options.spanGaps,o=n.length;if(!o)return[];let i=!!e._loop,{start:a,end:s}=function(e,t,n,r){let o=0,i=t-1;if(n&&!r)for(;o<t&&!e[o].skip;)o++;for(;o<t&&e[o].skip;)o++;for(o%=t,n&&(i+=o);i>o&&e[i%t].skip;)i--;return{start:o,end:i%=t}}(n,o,i,r);if(!0===r)return nr(e,[{start:a,end:s,loop:i}],n,t);let l=s<a?s+o:s,d=!!e._fullLoop&&0===a&&s===o-1;return nr(e,function(e,t,n,r){let o,i=e.length,a=[],s=t,l=e[t];for(o=t+1;o<=n;++o){let n=e[o%i];n.skip||n.stop?l.skip||(r=!1,a.push({start:t%i,end:(o-1)%i,loop:r}),t=s=n.stop?o:null):(s=o,l.skip&&(t=o)),l=n}return null!==s&&a.push({start:t%i,end:s%i,loop:r}),a}(n,a,l,d),n,t)}function nr(e,t,n,r){return r&&r.setContext&&n?function(e,t,n,r){let o=e._chart.getContext(),i=no(e.options),{_datasetIndex:a,options:{spanGaps:s}}=e,l=n.length,d=[],u=i,c=t[0].start,f=c;function h(e,t,r,o){let i=s?-1:1;if(e!==t){for(e+=l;n[e%l].skip;)e-=i;for(;n[t%l].skip;)t+=i;e%l!=t%l&&(d.push({start:e%l,end:t%l,loop:r,style:o}),u=o,c=t%l)}}for(let e of t){let t,i=n[(c=s?c:e.start)%l];for(f=c+1;f<=e.end;f++){let s=n[f%l];(function(e,t){if(!t)return!1;let n=[],r=function(e,t){return eG(t)?(n.includes(t)||n.push(t),n.indexOf(t)):t};return JSON.stringify(e,r)!==JSON.stringify(t,r)})(t=no(r.setContext(tO(o,{type:"segment",p0:i,p1:s,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:a}))),u)&&h(c,f-1,e.loop,u),i=s,u=t}c<f-1&&h(c,f-1,e.loop,u)}return d}(e,t,n,r):t}function no(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function ni(e,t,n){return e.options.clip?e[n]:t[n]}function na(e,t){let n=t._clip;if(n.disabled)return!1;let r=function(e,t){let{xScale:n,yScale:r}=e;return n&&r?{left:ni(n,t,"left"),right:ni(n,t,"right"),top:ni(r,t,"top"),bottom:ni(r,t,"bottom")}:t}(t,e.chartArea);return{left:!1===n.left?0:r.left-(!0===n.left?0:n.left),right:!1===n.right?e.width:r.right+(!0===n.right?0:n.right),top:!1===n.top?0:r.top-(!0===n.top?0:n.top),bottom:!1===n.bottom?e.height:r.bottom+(!0===n.bottom?0:n.bottom)}}},1516:(e,t,n)=>{n.d(t,{A:()=>e$});var r=n(2502),o=n(1351);let i={modes:{point:(e,t)=>s(e,t,{intersect:!0}),nearest:(e,t,n)=>(function(e,t,n){let r=Number.POSITIVE_INFINITY;return s(e,t,n).reduce((e,i)=>{var a;let s=i.getCenterPoint(),l="x"===(a=n.axis)?{x:t.x,y:s.y}:"y"===a?{x:s.x,y:t.y}:s,d=(0,o.aF)(t,l);return d<r?(e=[i],r=d):d===r&&e.push(i),e},[]).sort((e,t)=>e._index-t._index).slice(0,1)})(e,t,n),x:(e,t,n)=>s(e,t,{intersect:n.intersect,axis:"x"}),y:(e,t,n)=>s(e,t,{intersect:n.intersect,axis:"y"})}};function a(e,t,n){return(i.modes[n.mode]||i.modes.nearest)(e,t,n)}function s(e,t,n){return e.filter(e=>{var r;return n.intersect?e.inRange(t.x,t.y):"x"!==(r=n.axis)&&"y"!==r?e.inRange(t.x,t.y,"x",!0)||e.inRange(t.x,t.y,"y",!0):e.inRange(t.x,t.y,r,!0)})}function l(e,t,n){let r=Math.cos(n),o=Math.sin(n),i=t.x,a=t.y;return{x:i+r*(e.x-i)-o*(e.y-a),y:a+o*(e.x-i)+r*(e.y-a)}}let d=(e,t)=>t>e||e.length>t.length&&e.slice(0,t.length)===t,u=(e,t,n)=>Math.min(n,Math.max(t,e)),c=(e,t)=>e.value>=e.start-t&&e.value<=e.end+t;function f(e,{x:t,y:n,x2:r,y2:o},i,{borderWidth:a,hitTolerance:s}){let l=(a+s)/2,d=e.x>=t-l-.001&&e.x<=r+l+.001,u=e.y>=n-l-.001&&e.y<=o+l+.001;return"x"===i?d:"y"===i?u:d&&u}function h(e,{rect:t,center:n},r,{rotation:i,borderWidth:a,hitTolerance:s}){return f(l(e,n,(0,o.t)(-i)),t,r,{borderWidth:a,hitTolerance:s})}function p(e,t){let{centerX:n,centerY:r}=e.getProps(["centerX","centerY"],t);return{x:n,y:r}}let m=e=>"string"==typeof e&&e.endsWith("%"),y=e=>parseFloat(e)/100,b=e=>u(y(e),0,1),g=(e,t)=>({x:e,y:t,x2:e,y2:t,width:0,height:0}),v={box:e=>g(e.centerX,e.centerY),doughnutLabel:e=>g(e.centerX,e.centerY),ellipse:e=>({centerX:e.centerX,centerY:e.centerX,radius:0,width:0,height:0}),label:e=>g(e.centerX,e.centerY),line:e=>g(e.x,e.y),point:e=>({centerX:e.centerX,centerY:e.centerY,radius:0,width:0,height:0}),polygon:e=>g(e.centerX,e.centerY)};function x(e,t){return"start"===t?0:"end"===t?e:m(t)?b(t)*e:e/2}function w(e,t,n=!0){return"number"==typeof t?t:m(t)?(n?b(t):y(t))*e:e}function M(e,t,{borderWidth:n,position:r,xAdjust:i,yAdjust:a},s){let l=(0,o.i)(s),d=t.width+(l?s.width:0)+n,u=t.height+(l?s.height:0)+n,c=k(r),f=S(e.x,d,i,c.x),h=S(e.y,u,a,c.y);return{x:f,y:h,x2:f+d,y2:h+u,width:d,height:u,centerX:f+d/2,centerY:h+u/2}}function k(e,t="center"){return(0,o.i)(e)?{x:(0,o.v)(e.x,t),y:(0,o.v)(e.y,t)}:{x:e=(0,o.v)(e,t),y:e}}let C=(e,t)=>e&&e.autoFit&&t<1;function D(e,t){let n=e.font,r=(0,o.b)(n)?n:[n];return C(e,t)?r.map(function(e){let n=(0,o.a0)(e);return n.size=Math.floor(e.size*t),n.lineHeight=e.lineHeight,(0,o.a0)(n)}):r.map(e=>(0,o.a0)(e))}function O(e){return e&&((0,o.h)(e.xValue)||(0,o.h)(e.yValue))}function S(e,t,n=0,r){return e-x(t,r)+n}function T(e,t,n){let r=n.init;return r?!0===r?N(t,n):function(e,t,n){let r=(0,o.Q)(n.init,[{chart:e,properties:t,options:n}]);return!0===r?N(t,n):(0,o.i)(r)?r:void 0}(e,t,n):void 0}function E(e,t,n){let r=!1;return t.forEach(t=>{(0,o.a7)(e[t])?(r=!0,n[t]=e[t]):(0,o.h)(n[t])&&delete n[t]}),r}function N(e,t){return v[t.type||"line"](e)}let P=new Map,_=e=>isNaN(e)||e<=0,W=e=>e.reduce(function(e,t){return e+t.string},"");function A(e){if(e&&"object"==typeof e){let t=e.toString();return"[object HTMLImageElement]"===t||"[object HTMLCanvasElement]"===t}}function I(e,{x:t,y:n},r){r&&(e.translate(t,n),e.rotate((0,o.t)(r)),e.translate(-t,-n))}function j(e,t){if(t&&t.borderWidth)return e.lineCap=t.borderCapStyle||"butt",e.setLineDash(t.borderDash),e.lineDashOffset=t.borderDashOffset,e.lineJoin=t.borderJoinStyle||"miter",e.lineWidth=t.borderWidth,e.strokeStyle=t.borderColor,!0}function R(e,t){e.shadowColor=t.backgroundShadowColor,e.shadowBlur=t.shadowBlur,e.shadowOffsetX=t.shadowOffsetX,e.shadowOffsetY=t.shadowOffsetY}function Y(e,t){let n=t.content;if(A(n))return{width:w(n.width,t.width),height:w(n.height,t.height)};let r=D(t),i=t.textStrokeWidth,a=(0,o.b)(n)?n:[n],s=a.join()+W(r)+i+(e._measureText?"-spriting":"");return P.has(s)||P.set(s,function(e,t,n,r){e.save();let o=t.length,i=0,a=r;for(let s=0;s<o;s++){let o=n[Math.min(s,n.length-1)];e.font=o.string;let l=t[s];i=Math.max(i,e.measureText(l).width+r),a+=o.lineHeight}return e.restore(),{width:i,height:a}}(e,a,r,i)),P.get(s)}function L(e,t,n){let{x:r,y:i,width:a,height:s}=t;e.save(),R(e,n);let l=j(e,n);e.fillStyle=n.backgroundColor,e.beginPath(),(0,o.av)(e,{x:r,y:i,w:a,h:s,radius:function(e,t,n){for(let t of Object.keys(e))e[t]=u(e[t],0,n);return e}((0,o.ax)(n.borderRadius),0,Math.min(a,s)/2)}),e.closePath(),e.fill(),l&&(e.shadowColor=n.borderShadowColor,e.stroke()),e.restore()}function F(e,t,n,r){let i=n.content;if(A(i)){e.save(),e.globalAlpha=function(e,t){let n=(0,o.x)(e)?e:t;return(0,o.x)(n)?u(n,0,1):1}(n.opacity,i.style.opacity),e.drawImage(i,t.x,t.y,t.width,t.height),e.restore();return}let a=(0,o.b)(i)?i:[i],s=D(n,r),l=n.color,d=(0,o.b)(l)?l:[l],c=function(e,t){let{x:n,width:r}=e,o=t.textAlign;return"center"===o?n+r/2:"end"===o||"right"===o?n+r:n}(t,n),f=t.y+n.textStrokeWidth/2;e.save(),e.textBaseline="middle",e.textAlign=n.textAlign,function(e,t){if(t.textStrokeWidth>0)return e.lineJoin="round",e.miterLimit=2,e.lineWidth=t.textStrokeWidth,e.strokeStyle=t.textStrokeColor,!0}(e,n)&&function(e,{x:t,y:n},r,o){e.beginPath();let i=0;r.forEach(function(r,a){let s=o[Math.min(a,o.length-1)],l=s.lineHeight;e.font=s.string,e.strokeText(r,t,n+l/2+i),i+=l}),e.stroke()}(e,{x:c,y:f},a,s),function(e,{x:t,y:n},r,{fonts:o,colors:i}){let a=0;r.forEach(function(r,s){let l=i[Math.min(s,i.length-1)],d=o[Math.min(s,o.length-1)],u=d.lineHeight;e.beginPath(),e.font=d.string,e.fillStyle=l,e.fillText(r,t,n+u/2+a),a+=u,e.fill()})}(e,{x:c,y:f},a,{fonts:s,colors:d}),e.restore()}let H=["left","bottom","top","right"],B={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function $(e,t,n){return t="number"==typeof t?t:e.parse(t),(0,o.g)(t)?e.getPixelForValue(t):n}function z(e,t,n){let r=t[n];if(r||"scaleID"===n)return r;let o=n.charAt(0),i=Object.values(e).filter(e=>e.axis&&e.axis===o);return i.length?i[0].id:o}function X(e,t){if(e){let n=e.options.reverse;return{start:$(e,t.min,n?t.end:t.start),end:$(e,t.max,n?t.start:t.end)}}}function Z(e,t){let{chartArea:n,scales:r}=e,o=r[z(r,t,"xScaleID")],i=r[z(r,t,"yScaleID")],a=n.width/2,s=n.height/2;return o&&(a=$(o,t.xValue,o.left+o.width/2)),i&&(s=$(i,t.yValue,i.top+i.height/2)),{x:a,y:s}}function U(e,t){let n=e.scales,r=n[z(n,t,"xScaleID")],o=n[z(n,t,"yScaleID")];if(!r&&!o)return{};let{left:i,right:a}=r||e.chartArea,{top:s,bottom:l}=o||e.chartArea,d=K(r,{min:t.xMin,max:t.xMax,start:i,end:a});i=d.start,a=d.end;let u=K(o,{min:t.yMin,max:t.yMax,start:l,end:s});return{x:i,y:s=u.start,x2:a,y2:l=u.end,width:a-i,height:l-s,centerX:i+(a-i)/2,centerY:s+(l-s)/2}}function q(e,t){if(!O(t)){let n=U(e,t),r=t.radius;(!r||isNaN(r))&&(t.radius=r=Math.min(n.width,n.height)/2);let o=2*r,i=n.centerX+t.xAdjust,a=n.centerY+t.yAdjust;return{x:i-r,y:a-r,x2:i+r,y2:a+r,centerX:i,centerY:a,width:o,height:o,radius:r}}var n=e,r=t;let o=Z(n,r),i=2*r.radius;return{x:o.x-r.radius+r.xAdjust,y:o.y-r.radius+r.yAdjust,x2:o.x+r.radius+r.xAdjust,y2:o.y+r.radius+r.yAdjust,centerX:o.x+r.xAdjust,centerY:o.y+r.yAdjust,radius:r.radius,width:i,height:i}}function V(e,t){let n=U(e,t);return n.initProperties=T(e,n,t),n.elements=[{type:"label",optionScope:"label",properties:function(e,t,n){let r=n.label;r.backgroundColor="transparent",r.callout.display=!1;let i=k(r.position),a=(0,o.E)(r.padding),s=Y(e.ctx,r),l=function({properties:e,options:t},n,r,o){let{x:i,x2:a,width:s}=e;return Q({start:i,end:a,size:s,borderWidth:t.borderWidth},{position:r.x,padding:{start:o.left,end:o.right},adjust:t.label.xAdjust,size:n.width})}({properties:t,options:n},s,i,a),d=function({properties:e,options:t},n,r,o){let{y:i,y2:a,height:s}=e;return Q({start:i,end:a,size:s,borderWidth:t.borderWidth},{position:r.y,padding:{start:o.top,end:o.bottom},adjust:t.label.yAdjust,size:n.height})}({properties:t,options:n},s,i,a),u=s.width+a.width,c=s.height+a.height;return{x:l,y:d,x2:l+u,y2:d+c,width:u,height:c,centerX:l+u/2,centerY:d+c/2,rotation:r.rotation}}(e,n,t),initProperties:n.initProperties}],n}function K(e,t){let n=X(e,t)||t;return{start:Math.min(n.start,n.end),end:Math.max(n.start,n.end)}}function Q(e,t){let{start:n,end:r,borderWidth:o}=e,{position:i,padding:{start:a,end:s},adjust:l}=t,d=r-o-n-a-s-t.size;return n+o/2+l+x(d,i)}let G=["enter","leave"],J=G.concat("click");function ee({state:e,event:t},n,r,o){let i;for(let a of r)0>o.indexOf(a)&&(i=et(a.options[n]||e.listeners[n],a,t)||i);return i}function et(e,t,n){return!0===(0,o.Q)(e,[t.$context,n])}let en=["afterDraw","beforeDraw"];function er(e,t,n){if(e.hooked){let r=t.options[n]||e.hooks[n];return(0,o.Q)(r,[t.$context])}}function eo(e,t,n,r){var i,a,s;if((0,o.g)(t[n])&&(i=e.options,a=n,s=r,!((0,o.h)(i[a])||(0,o.h)(i[s])))){let r=e[n]!==t[n];return e[n]=t[n],r}}function ei(e,t,n,r){for(let i of n){let n=e[i];if((0,o.h)(n)){let e=t.parse(n);r.min=Math.min(r.min,e),r.max=Math.max(r.max,e)}}}class ea extends r.Hg{inRange(e,t,n,r){let{x:i,y:a}=l({x:e,y:t},this.getCenterPoint(r),(0,o.t)(-this.options.rotation));return f({x:i,y:a},this.getProps(["x","y","x2","y2"],r),n,this.options)}getCenterPoint(e){return p(this,e)}draw(e){e.save(),I(e,this.getCenterPoint(),this.options.rotation),L(e,this,this.options),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return V(e,t)}}ea.id="boxAnnotation",ea.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},ea.defaultRoutes={borderColor:"color",backgroundColor:"color"},ea.descriptors={label:{_fallback:!0}};class es extends r.Hg{inRange(e,t,n,r){return h({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},n,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return p(this,e)}draw(e){let t=this.options;t.display&&t.content&&(function(e,t){let{_centerX:n,_centerY:r,_radius:o,_startAngle:i,_endAngle:a,_counterclockwise:s,options:l}=t;e.save();let d=j(e,l);e.fillStyle=l.backgroundColor,e.beginPath(),e.arc(n,r,o,i,a,s),e.closePath(),e.fill(),d&&e.stroke(),e.restore()}(e,this),e.save(),I(e,this.getCenterPoint(),this.rotation),F(e,this,t,this._fitRatio),e.restore())}resolveElementProperties(e,t){var n,i;let a=(n=e,i=t,n.getSortedVisibleDatasetMetas().reduce(function(e,t){let o=t.controller;return o instanceof r.ju&&function(e,t,n){if(!t.autoHide)return!0;for(let t=0;t<n.length;t++)if(!n[t].hidden&&e.getDataVisibility(t))return!0}(n,i,t.data)&&(!e||o.innerRadius<e.controller.innerRadius)&&o.options.circumference>=90?t:e},void 0));if(!a)return{};let{controllerMeta:s,point:l,radius:d}=function({chartArea:e},t,n){let{left:r,top:i,right:a,bottom:s}=e,{innerRadius:l,offsetX:d,offsetY:u}=n.controller,c=(r+a)/2+d,f=(i+s)/2+u,h={left:Math.max(c-l,r),right:Math.min(c+l,a),top:Math.max(f-l,i),bottom:Math.min(f+l,s)},p={x:(h.left+h.right)/2,y:(h.top+h.bottom)/2},m=t.spacing+t.borderWidth/2,y=l-m,b=p.y>f,g=function(e,t,n,r){let i=-2*t,a=Math.pow(i,2)-4*(Math.pow(t,2)+Math.pow(n-e,2)-Math.pow(r,2));if(a<=0)return{_startAngle:0,_endAngle:o.T};let s=(-i-Math.sqrt(a))/2,l=(-i+Math.sqrt(a))/2;return{_startAngle:(0,o.D)({x:t,y:n},{x:s,y:e}).angle,_endAngle:(0,o.D)({x:t,y:n},{x:l,y:e}).angle}}(b?i+m:s-m,c,f,y);return{controllerMeta:{_centerX:c,_centerY:f,_radius:y,_counterclockwise:b,...g},point:p,radius:Math.min(l,Math.min(h.right-h.left,h.bottom-h.top)/2)}}(e,t,a),u=Y(e.ctx,t),c=function({width:e,height:t},n){return 2*n/Math.sqrt(Math.pow(e,2)+Math.pow(t,2))}(u,d);C(t,c)&&(u={width:u.width*c,height:u.height*c});let{position:f,xAdjust:h,yAdjust:p}=t,m=M(l,u,{borderWidth:0,position:f,xAdjust:h,yAdjust:p});return{initProperties:T(e,m,t),...m,...s,rotation:t.rotation,_fitRatio:c}}}es.id="doughnutLabelAnnotation",es.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0},es.defaultRoutes={};class el extends r.Hg{inRange(e,t,n,r){return h({x:e,y:t},{rect:this.getProps(["x","y","x2","y2"],r),center:this.getCenterPoint(r)},n,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(e){return p(this,e)}draw(e){let t=this.options,n=!(0,o.h)(this._visible)||this._visible;t.display&&t.content&&n&&(e.save(),I(e,this.getCenterPoint(),this.rotation),function(e,t){let{pointX:n,pointY:r,options:i}=t,a=i.callout,s=a&&a.display&&function(e,t){let n=t.position;return H.includes(n)?n:function(e,t){let{x:n,y:r,x2:i,y2:a,width:s,height:d,pointX:u,pointY:c,centerX:f,centerY:h,rotation:p}=e,m={x:f,y:h},y=t.start,b=w(s,y),g=w(d,y),v=[n,n+b,n+b,i],x=[r+g,a,r,a],M=[];for(let e=0;e<4;e++){let t=l({x:v[e],y:x[e]},m,(0,o.t)(p));M.push({position:H[e],distance:(0,o.aF)(t,{x:u,y:c})})}return M.sort((e,t)=>e.distance-t.distance)[0].position}(e,t)}(t,a);if(!s||function(e,t,n){let{pointX:r,pointY:o}=e,i=t.margin,a=r,s=o;return"left"===n?a+=i:"right"===n?a-=i:"top"===n?s+=i:"bottom"===n&&(s-=i),e.inRange(a,s)}(t,a,s))return;if(e.save(),e.beginPath(),!j(e,a))return e.restore();let{separatorStart:d,separatorEnd:u}=function(e,t){let n,r,{x:o,y:i,x2:a,y2:s}=e,l=function(e,t){let{width:n,height:r,options:o}=e,i=o.callout.margin+o.borderWidth/2;return"right"===t?n+i:"bottom"===t?r+i:-i}(e,t);return r="left"===t||"right"===t?{x:(n={x:o+l,y:i}).x,y:s}:{x:a,y:(n={x:o,y:i+l}).y},{separatorStart:n,separatorEnd:r}}(t,s),{sideStart:c,sideEnd:f}=function(e,t,n){let r,o,{y:i,width:a,height:s,options:l}=e,d=l.callout.start,u=function(e,t){let n=t.side;return"left"===e||"top"===e?-n:n}(t,l.callout);return o="left"===t||"right"===t?{x:(r={x:n.x,y:i+w(s,d)}).x+u,y:r.y}:{x:(r={x:n.x+w(a,d),y:n.y}).x,y:r.y+u},{sideStart:r,sideEnd:o}}(t,s,d);(a.margin>0||0===i.borderWidth)&&(e.moveTo(d.x,d.y),e.lineTo(u.x,u.y)),e.moveTo(c.x,c.y),e.lineTo(f.x,f.y);let h=l({x:n,y:r},t.getCenterPoint(),(0,o.t)(-t.rotation));e.lineTo(h.x,h.y),e.stroke(),e.restore()}(e,this),L(e,this,t),F(e,function({x:e,y:t,width:n,height:r,options:i}){let a=i.borderWidth/2,s=(0,o.E)(i.padding);return{x:e+s.left+a,y:t+s.top+a,width:n-s.left-s.right-i.borderWidth,height:r-s.top-s.bottom-i.borderWidth}}(this),t),e.restore())}resolveElementProperties(e,t){let n;if(O(t))n=Z(e,t);else{let{centerX:r,centerY:o}=U(e,t);n={x:r,y:o}}let r=(0,o.E)(t.padding),i=M(n,Y(e.ctx,t),t,r);return{initProperties:T(e,i,t),pointX:n.x,pointY:n.y,...i,rotation:t.rotation}}}el.id="labelAnnotation",el.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},el.defaultRoutes={borderColor:"color"};let ed=(e,t,n)=>({x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}),eu=(e,t,n)=>ed(t,n,Math.abs((e-t.y)/(n.y-t.y))).x,ec=(e,t,n)=>ed(t,n,Math.abs((e-t.x)/(n.x-t.x))).y,ef=e=>e*e,eh=(e,t,{x:n,y:r,x2:o,y2:i},a)=>"y"===a?{start:Math.min(r,i),end:Math.max(r,i),value:t}:{start:Math.min(n,o),end:Math.max(n,o),value:e},ep=(e,t,n,r)=>(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n,em=(e,t,n,r)=>({x:ep(e.x,t.x,n.x,r),y:ep(e.y,t.y,n.y,r)}),ey=(e,t,n,r)=>2*(1-r)*(t-e)+2*r*(n-t),eb=(e,t,n,r)=>-Math.atan2(ey(e.x,t.x,n.x,r),ey(e.y,t.y,n.y,r))+.5*o.P;class eg extends r.Hg{inRange(e,t,n,r){let o=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n){let n={mouseX:e,mouseY:t},{path:i,ctx:a}=this;if(i){j(a,this.options),a.lineWidth+=this.options.hitTolerance;let{chart:o}=this.$context,s=e*o.currentDevicePixelRatio,l=t*o.currentDevicePixelRatio,d=a.isPointInStroke(i,s,l)||ew(this,n,r);return a.restore(),d}return function(e,{mouseX:t,mouseY:n},r=.001,o){let i,a,{x:s,y:l,x2:d,y2:u}=e.getProps(["x","y","x2","y2"],o),c=d-s,f=u-l,h=ef(c)+ef(f),p=0===h?-1:((t-s)*c+(n-l)*f)/h;return p<0?(i=s,a=l):p>1?(i=d,a=u):(i=s+p*c,a=l+p*f),ef(t-i)+ef(n-a)<=r}(this,n,ef(o),r)||ew(this,n,r)}return function(e,{mouseX:t,mouseY:n},r,{hitSize:o,useFinalPosition:i}){return c(eh(t,n,e.getProps(["x","y","x2","y2"],i),r),o)||ew(e,{mouseX:t,mouseY:n},i,r)}(this,{mouseX:e,mouseY:t},n,{hitSize:o,useFinalPosition:r})}getCenterPoint(e){return p(this,e)}draw(e){let{x:t,y:n,x2:r,y2:i,cp:a,options:s}=this;if(e.save(),!j(e,s))return e.restore();R(e,s);let l=Math.sqrt(Math.pow(r-t,2)+Math.pow(i-n,2));if(s.curve&&a)return function(e,t,n,r){let{x:i,y:a,x2:s,y2:l,options:d}=t,{startOpts:u,endOpts:c,startAdjust:f,endAdjust:h}=eC(t),p={x:i,y:a},m={x:s,y:l},y=eb(p,n,m,0),b=eb(p,n,m,1)-o.P,g=em(p,n,m,f/r),v=em(p,n,m,1-h/r),x=new Path2D;e.beginPath(),x.moveTo(g.x,g.y),x.quadraticCurveTo(n.x,n.y,v.x,v.y),e.shadowColor=d.borderShadowColor,e.stroke(x),t.path=x,t.ctx=e,eS(e,g,{angle:y,adjust:f},u),eS(e,v,{angle:b,adjust:h},c)}(e,this,a,l),e.restore();let{startOpts:d,endOpts:u,startAdjust:c,endAdjust:f}=eC(this),h=Math.atan2(i-n,r-t);e.translate(t,n),e.rotate(h),e.beginPath(),e.moveTo(0+c,0),e.lineTo(l-f,0),e.shadowColor=s.borderShadowColor,e.stroke(),eO(e,0,c,d),eO(e,l,-f,u),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){let n=function(e,t){let{scales:n,chartArea:r}=e,o=n[t.scaleID],i={x:r.left,y:r.top,x2:r.right,y2:r.bottom};return o?function(e,t,n){let r=$(e,n.value,NaN),o=$(e,n.endValue,r);e.isHorizontal()?(t.x=r,t.x2=o):(t.y=r,t.y2=o)}(o,i,t):function(e,t,n){for(let r of Object.keys(B)){let o=e[z(e,n,r)];if(o){let{min:e,max:i,start:a,end:s,startProp:l,endProp:d}=B[r],u=X(o,{min:n[e],max:n[i],start:o[a],end:o[s]});t[l]=u.start,t[d]=u.end}}}(n,i,t),i}(e,t),{x:r,y:i,x2:a,y2:s}=n,d=function({x:e,y:t,x2:n,y2:r},{top:o,right:i,bottom:a,left:s}){return!(e<s&&n<s||e>i&&n>i||t<o&&r<o||t>a&&r>a)}(n,e.chartArea),u=d?function(e,t,n){let{x:r,y:o}=ex(e,t,n),{x:i,y:a}=ex(t,e,n);return{x:r,y:o,x2:i,y2:a,width:Math.abs(i-r),height:Math.abs(a-o)}}({x:r,y:i},{x:a,y:s},e.chartArea):{x:r,y:i,x2:a,y2:s,width:Math.abs(a-r),height:Math.abs(s-i)};if(u.centerX=(a+r)/2,u.centerY=(s+i)/2,u.initProperties=T(e,u,t),t.curve){let e={x:u.x,y:u.y},n={x:u.x2,y:u.y2};u.cp=function(e,t,n){let{x:r,y:o,x2:i,y2:a,centerX:s,centerY:d}=e,u=Math.atan2(a-o,i-r),c=k(t.controlPoint,0);return l({x:s+w(n,c.x,!1),y:d+w(n,c.y,!1)},{x:s,y:d},u)}(u,t,(0,o.aF)(e,n))}let c=function(e,t,n){let r=n.borderWidth,i=(0,o.E)(n.padding),a=Y(e.ctx,n);return function(e,t,n,r){let{width:i,height:a,padding:s}=n,{xAdjust:l,yAdjust:d}=t,u={x:e.x,y:e.y},c={x:e.x2,y:e.y2},f="auto"===t.rotation?function(e){let{x:t,y:n,x2:r,y2:i}=e,a=Math.atan2(i-n,r-t);return a>o.P/2?a-o.P:a<-(o.P/2)?a+o.P:a}(e):(0,o.t)(t.rotation),h=function(e,t,n){let r=Math.cos(n),o=Math.sin(n);return{w:Math.abs(e*r)+Math.abs(t*o),h:Math.abs(e*o)+Math.abs(t*r)}}(i,a,f),p=function(e,t,n,r){let o,i=function(e,t){let{x:n,x2:r,y:o,y2:i}=e,a=Math.min(o,i)-t.top,s=Math.min(n,r)-t.left,l=t.bottom-Math.max(o,i),d=t.right-Math.max(n,r);return{x:Math.min(s,d),y:Math.min(a,l),dx:s<=d?1:-1,dy:a<=l?1:-1}}(e,r);return"start"===t.position?eM({w:e.x2-e.x,h:e.y2-e.y},n,t,i):"end"===t.position?1-eM({w:e.x-e.x2,h:e.y-e.y2},n,t,i):x(1,t.position)}(e,t,{labelSize:h,padding:s},r),m=e.cp?em(u,e.cp,c,p):ed(u,c,p),y={size:h.w,min:r.left,max:r.right,padding:s.left},b={size:h.h,min:r.top,max:r.bottom,padding:s.top},g=ek(m.x,y)+l,v=ek(m.y,b)+d;return{x:g-i/2,y:v-a/2,x2:g+i/2,y2:v+a/2,centerX:g,centerY:v,pointX:m.x,pointY:m.y,width:i,height:a,rotation:(0,o.U)(f)}}(t,n,{width:a.width+i.width+r,height:a.height+i.height+r,padding:i},e.chartArea)}(e,u,t.label);return c._visible=d,u.elements=[{type:"label",optionScope:"label",properties:c,initProperties:u.initProperties}],u}}eg.id="lineAnnotation";let ev={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};function ex({x:e,y:t},n,{top:r,right:o,bottom:i,left:a}){return e<a&&(t=ec(a,{x:e,y:t},n),e=a),e>o&&(t=ec(o,{x:e,y:t},n),e=o),t<r&&(e=eu(r,{x:e,y:t},n),t=r),t>i&&(e=eu(i,{x:e,y:t},n),t=i),{x:e,y:t}}function ew(e,{mouseX:t,mouseY:n},r,o){let i=e.label;return i.options.display&&i.inRange(t,n,o,r)}function eM(e,t,n,r){let{labelSize:o,padding:i}=t,a=e.w*r.dx,s=e.h*r.dy;return u(Math.max(a>0&&(o.w/2+i.left-r.x)/a,s>0&&(o.h/2+i.top-r.y)/s),0,.25)}function ek(e,t){let{size:n,min:r,max:o,padding:i}=t,a=n/2;return n>o-r?(o+r)/2:(r>=e-i-a&&(e=r+i+a),o<=e+i+a&&(e=o-i-a),e)}function eC(e){let t=e.options,n=t.arrowHeads&&t.arrowHeads.start,r=t.arrowHeads&&t.arrowHeads.end;return{startOpts:n,endOpts:r,startAdjust:eD(e,n),endAdjust:eD(e,r)}}function eD(e,t){if(!t||!t.display)return 0;let{length:n,width:r}=t,o=e.options.borderWidth/2;return Math.abs(eu(0,{x:n,y:r+o},{x:0,y:o}))}function eO(e,t,n,r){if(!r||!r.display)return;let{length:o,width:i,fill:a,backgroundColor:s,borderColor:l}=r,d=Math.abs(t-o)+n;e.beginPath(),R(e,r),j(e,r),e.moveTo(d,-i),e.lineTo(t+n,0),e.lineTo(d,i),!0===a?(e.fillStyle=s||l,e.closePath(),e.fill(),e.shadowColor="transparent"):e.shadowColor=r.borderShadowColor,e.stroke()}function eS(e,{x:t,y:n},{angle:r,adjust:o},i){i&&i.display&&(e.save(),e.translate(t,n),e.rotate(r),eO(e,0,-o,i),e.restore())}eg.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},ev),fill:!1,length:12,start:Object.assign({},ev),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},el.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},eg.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}},eg.defaultRoutes={borderColor:"color"};class eT extends r.Hg{inRange(e,t,n,r){let i=this.options.rotation,a=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n)return function(e,t,n,r){let{width:i,height:a,centerX:s,centerY:l}=t,d=i/2,u=a/2;if(d<=0||u<=0)return!1;let c=(0,o.t)(n||0),f=Math.cos(c),h=Math.sin(c);return Math.pow(f*(e.x-s)+h*(e.y-l),2)/Math.pow(d+r,2)+Math.pow(h*(e.x-s)-f*(e.y-l),2)/Math.pow(u+r,2)<=1.0001}({x:e,y:t},this.getProps(["width","height","centerX","centerY"],r),i,a);let{x:s,y:d,x2:u,y2:c}=this.getProps(["x","y","x2","y2"],r),f="y"===n?{start:d,end:c}:{start:s,end:u},h=l({x:e,y:t},this.getCenterPoint(r),(0,o.t)(-i));return h[n]>=f.start-a-.001&&h[n]<=f.end+a+.001}getCenterPoint(e){return p(this,e)}draw(e){let{width:t,height:n,centerX:r,centerY:i,options:a}=this;e.save(),I(e,this.getCenterPoint(),a.rotation),R(e,this.options),e.beginPath(),e.fillStyle=a.backgroundColor;let s=j(e,a);e.ellipse(r,i,n/2,t/2,o.P/2,0,2*o.P),e.fill(),s&&(e.shadowColor=a.borderShadowColor,e.stroke()),e.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(e,t){return V(e,t)}}eT.id="ellipseAnnotation",eT.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},ea.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0},eT.defaultRoutes={borderColor:"color",backgroundColor:"color"},eT.descriptors={label:{_fallback:!0}};class eE extends r.Hg{inRange(e,t,n,r){let{x:o,y:i,x2:a,y2:s,width:l}=this.getProps(["x","y","x2","y2","width"],r),d=(this.options.borderWidth+this.options.hitTolerance)/2;if("x"!==n&&"y"!==n){var u,f,h;return u={x:e,y:t},f=this.getCenterPoint(r),h=l/2,!!u&&!!f&&!(h<=0)&&Math.pow(u.x-f.x,2)+Math.pow(u.y-f.y,2)<=Math.pow(h+d,2)}return c("y"===n?{start:i,end:s,value:t}:{start:o,end:a,value:e},d)}getCenterPoint(e){return p(this,e)}draw(e){let t=this.options,n=t.borderWidth;if(t.radius<.1)return;e.save(),e.fillStyle=t.backgroundColor,R(e,t);let r=j(e,t);!function(e,t,n,r){let{radius:i,options:a}=t,s=a.pointStyle,l=a.rotation,d=(l||0)*o.b4;if(A(s)){e.save(),e.translate(n,r),e.rotate(d),e.drawImage(s,-s.width/2,-s.height/2,s.width,s.height),e.restore();return}_(i)||function(e,{x:t,y:n,radius:r,rotation:i,style:a,rad:s}){let l,d,u,c;switch(e.beginPath(),a){default:e.arc(t,n,r,0,o.T),e.closePath();break;case"triangle":e.moveTo(t+Math.sin(s)*r,n-Math.cos(s)*r),s+=o.b6,e.lineTo(t+Math.sin(s)*r,n-Math.cos(s)*r),s+=o.b6,e.lineTo(t+Math.sin(s)*r,n-Math.cos(s)*r),e.closePath();break;case"rectRounded":c=.516*r,u=r-c,l=Math.cos(s+o.b5)*u,d=Math.sin(s+o.b5)*u,e.arc(t-l,n-d,c,s-o.P,s-o.H),e.arc(t+d,n-l,c,s-o.H,s),e.arc(t+l,n+d,c,s,s+o.H),e.arc(t-d,n+l,c,s+o.H,s+o.P),e.closePath();break;case"rect":if(!i){u=Math.SQRT1_2*r,e.rect(t-u,n-u,2*u,2*u);break}s+=o.b5;case"rectRot":l=Math.cos(s)*r,d=Math.sin(s)*r,e.moveTo(t-l,n-d),e.lineTo(t+d,n-l),e.lineTo(t+l,n+d),e.lineTo(t-d,n+l),e.closePath();break;case"crossRot":s+=o.b5;case"cross":l=Math.cos(s)*r,d=Math.sin(s)*r,e.moveTo(t-l,n-d),e.lineTo(t+l,n+d),e.moveTo(t+d,n-l),e.lineTo(t-d,n+l);break;case"star":l=Math.cos(s)*r,d=Math.sin(s)*r,e.moveTo(t-l,n-d),e.lineTo(t+l,n+d),e.moveTo(t+d,n-l),e.lineTo(t-d,n+l),s+=o.b5,l=Math.cos(s)*r,d=Math.sin(s)*r,e.moveTo(t-l,n-d),e.lineTo(t+l,n+d),e.moveTo(t+d,n-l),e.lineTo(t-d,n+l);break;case"line":l=Math.cos(s)*r,d=Math.sin(s)*r,e.moveTo(t-l,n-d),e.lineTo(t+l,n+d);break;case"dash":e.moveTo(t,n),e.lineTo(t+Math.cos(s)*r,n+Math.sin(s)*r)}e.fill()}(e,{x:n,y:r,radius:i,rotation:l,style:s,rad:d})}(e,this,this.centerX,this.centerY),r&&!A(t.pointStyle)&&(e.shadowColor=t.borderShadowColor,e.stroke()),e.restore(),t.borderWidth=n}resolveElementProperties(e,t){let n=q(e,t);return n.initProperties=T(e,n,t),n}}eE.id="pointAnnotation",eE.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eE.defaultRoutes={borderColor:"color",backgroundColor:"color"};class eN extends r.Hg{inRange(e,t,n,r){if("x"!==n&&"y"!==n)return this.options.radius>=.1&&this.elements.length>1&&function(e,t,n,r){let o=!1,i=e[e.length-1].getProps(["bX","bY"],r);for(let a of e){let e=a.getProps(["bX","bY"],r);e.bY>n!=i.bY>n&&t<(i.bX-e.bX)*(n-e.bY)/(i.bY-e.bY)+e.bX&&(o=!o),i=e}return o}(this.elements,e,t,r);let i=l({x:e,y:t},this.getCenterPoint(r),(0,o.t)(-this.options.rotation)),a=this.elements.map(e=>"y"===n?e.bY:e.bX),s=Math.min(...a),d=Math.max(...a);return i[n]>=s&&i[n]<=d}getCenterPoint(e){return p(this,e)}draw(e){let{elements:t,options:n}=this;e.save(),e.beginPath(),e.fillStyle=n.backgroundColor,R(e,n);let r=j(e,n),o=!0;for(let n of t)o?(e.moveTo(n.x,n.y),o=!1):e.lineTo(n.x,n.y);e.closePath(),e.fill(),r&&(e.shadowColor=n.borderShadowColor,e.stroke()),e.restore()}resolveElementProperties(e,t){let n=q(e,t),{sides:r,rotation:i}=t,a=[],s=2*o.P/r,l=i*o.b4;for(let o=0;o<r;o++,l+=s){let r=function({centerX:e,centerY:t},{radius:n,borderWidth:r,hitTolerance:o},i){let a=(r+o)/2,s=Math.sin(i),l=Math.cos(i),d={x:e+s*n,y:t-l*n};return{type:"point",optionScope:"point",properties:{x:d.x,y:d.y,centerX:d.x,centerY:d.y,bX:e+s*(n+a),bY:t-l*(n+a)}}}(n,t,l);r.initProperties=T(e,n,t),a.push(r)}return n.elements=a,n}}eN.id="polygonAnnotation",eN.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0},eN.defaultRoutes={borderColor:"color",backgroundColor:"color"};let eP={box:ea,doughnutLabel:es,ellipse:eT,label:el,line:eg,point:eE,polygon:eN};Object.keys(eP).forEach(e=>{o.d.describe(`elements.${eP[e].id}`,{_fallback:"plugins.annotation.common"})});let e_={update:Object.assign},eW=J.concat(en),eA=(e,t)=>(0,o.i)(t)?eL(e,t):e,eI=e=>"color"===e||"font"===e;function ej(e="line"){return eP[e]?e:(console.warn(`Unknown annotation type: '${e}', defaulting to 'line'`),"line")}function eR(e,t,n,r){let o=eP[ej(n)],i=e[t];return i&&i instanceof o||Object.assign(i=e[t]=new o,r),i}function eY(e){let t=eP[ej(e.type)],n={};for(let r of(n.id=e.id,n.type=e.type,n.drawTime=e.drawTime,Object.assign(n,eL(e,t.defaults),eL(e,t.defaultRoutes)),eW))n[r]=e[r];return n}function eL(e,t){let n={};for(let r of Object.keys(t)){let i=t[r],a=e[r];eI(r)&&(0,o.b)(a)?n[r]=a.map(e=>eA(e,i)):n[r]=eA(a,i)}return n}let eF=new Map,eH=e=>"doughnutLabel"!==e.type,eB=J.concat(en);var e$={id:"annotation",version:"3.1.0",beforeRegister(){!function(e,t,n,r=!0){let o=n.split("."),i=0;for(let t of"4.0".split(".")){let a=o[i++];if(parseInt(t,10)<parseInt(a,10))break;if(d(a,t))if(!r)return!1;else throw Error(`${e} v${n} is not supported. v4.0 or newer is required.`)}}("chart.js","4.0",r.t1.version)},afterRegister(){r.t1.register(eP)},afterUnregister(){r.t1.unregister(eP)},beforeInit(e){eF.set(e,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(e,t,n){let r=eF.get(e).annotations=[],i=n.annotations;(0,o.i)(i)?Object.keys(i).forEach(e=>{let t=i[e];(0,o.i)(t)&&(t.id=e,r.push(t))}):(0,o.b)(i)&&r.push(...i);var a=r.filter(eH),s=e.scales;for(let e of a){var l=e,d=s;for(let e of["scaleID","xScaleID","yScaleID"]){let t=z(d,l,e);t&&!d[t]&&function(e,t){if("scaleID"===t)return!0;let n=t.charAt(0);for(let t of["Min","Max","Value"])if((0,o.h)(e[n+t]))return!0;return!1}(l,e)&&console.warn(`No scale found with id '${t}' for annotation '${l.id}'`)}}},afterDataLimits(e,t){let n=eF.get(e);!function(e,t,n){let r=function(e,t,n){let r=t.axis,i=t.id,a=r+"ScaleID",s={min:(0,o.v)(t.min,Number.NEGATIVE_INFINITY),max:(0,o.v)(t.max,Number.POSITIVE_INFINITY)};for(let o of n)o.scaleID===i?ei(o,t,["value","endValue"],s):z(e,o,a)===i&&ei(o,t,[r+"Min",r+"Max",r+"Value"],s);return s}(e.scales,t,n),i=eo(t,r,"min","suggestedMin");(i=eo(t,r,"max","suggestedMax")||i)&&(0,o.a7)(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}(e,t.scale,n.annotations.filter(eH).filter(e=>e.display&&e.adjustScaleRange))},afterUpdate(e,t,n){let i=eF.get(e);i.listened=E(n,J,i.listeners),i.moveListened=!1,G.forEach(e=>{(0,o.a7)(n[e])&&(i.moveListened=!0)}),i.listened&&i.moveListened||i.annotations.forEach(e=>{!i.listened&&(0,o.a7)(e.click)&&(i.listened=!0),i.moveListened||G.forEach(t=>{(0,o.a7)(e[t])&&(i.listened=!0,i.moveListened=!0)})}),function(e,t,n,i){var a,s,l,d,u,c,f,h;let p=(a=e,s=n.animations,"reset"===(l=i)||"none"===l||"resize"===l?e_:new r.Qw(a,s)),m=t.annotations,y=function(e,t){let n=t.length,r=e.length;return r<n?e.splice(r,0,...Array(n-r)):r>n&&e.splice(n,r-n),e}(t.elements,m);for(let t=0;t<m.length;t++){let n=m[t],r=eR(y,t,n.type),i=n.setContext((d=e,u=r,c=y,f=n,u.$context||(u.$context=Object.assign(Object.create(d.getContext()),{element:u,get elements(){return c.filter(e=>e&&e.options)},id:f.id,type:"annotation"})))),a=r.resolveElementProperties(e,i);a.skip=isNaN((h=a).x)||isNaN(h.y),"elements"in a&&(function(e,t,n,r){let o=e.elements||(e.elements=[]);o.length=t.length;for(let e=0;e<t.length;e++){let i=t[e],a=i.properties,s=eR(o,e,i.type,i.initProperties);a.options=eY(n[i.optionScope].override(i)),r.update(s,a)}}(r,a.elements,i,p),delete a.elements),(0,o.h)(r.x)||Object.assign(r,a),Object.assign(r,a.initProperties),a.options=eY(i),p.update(r,a)}}(e,i,n,t.mode),i.visibleElements=i.elements.filter(e=>!e.skip&&e.options.display);let a=i.visibleElements;i.hooked=E(n,en,i.hooks),i.hooked||a.forEach(e=>{i.hooked||en.forEach(t=>{(0,o.a7)(e.options[t])&&(i.hooked=!0)})})},beforeDatasetsDraw(e,t,n){ez(e,"beforeDatasetsDraw",n.clip)},afterDatasetsDraw(e,t,n){ez(e,"afterDatasetsDraw",n.clip)},beforeDatasetDraw(e,t,n){ez(e,t.index,n.clip)},beforeDraw(e,t,n){ez(e,"beforeDraw",n.clip)},afterDraw(e,t,n){ez(e,"afterDraw",n.clip)},beforeEvent(e,t,n){(function(e,t,n){if(e.listened)switch(t.type){case"mousemove":case"mouseout":let r;var o=e,i=t,s=n;if(!o.moveListened)return;r="mousemove"===i.type?a(o.visibleElements,i,s.interaction):[];let l=o.hovered;o.hovered=r;let d={state:o,event:i},u=ee(d,"leave",l,r);return ee(d,"enter",r,l)||u;case"click":let c;var f=e,h=t,p=n;let m=f.listeners;for(let e of a(f.visibleElements,h,p.interaction))c=et(e.options.click||m.click,e,h)||c;return c}})(eF.get(e),t.event,n)&&(t.changed=!0)},afterDestroy(e){eF.delete(e)},getAnnotations(e){let t=eF.get(e);return t?t.elements:[]},_getAnnotationElementsAtEventForMode:(e,t,n)=>a(e,t,n),defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:e=>!eB.includes(e)&&"init"!==e,annotations:{_allKeys:!1,_fallback:(e,t)=>`elements.${eP[ej(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:eI,_fallback:!0},_indexable:eI}},additionalOptionScopes:[""]};function ez(e,t,n){let{ctx:r,chartArea:i}=e,a=eF.get(e);for(let e of(n&&(0,o.Y)(r,i),(function(e,t){let n=[];for(let r of e)if(r.options.drawTime===t&&n.push({element:r,main:!0}),r.elements&&r.elements.length)for(let e of r.elements)e.options.display&&e.options.drawTime===t&&n.push({element:e});return n})(a.visibleElements,t).sort((e,t)=>e.element.options.z-t.element.options.z))){var s=r,l=i,d=a,u=e;let t=u.element;u.main?(er(d,t,"beforeDraw"),t.draw(s,l),er(d,t,"afterDraw")):t.draw(s,l)}n&&(0,o.$)(r)}},1586:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3786:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4410:(e,t,n)=>{n.d(t,{UC:()=>el,ZL:()=>es,bL:()=>ei,l9:()=>ea});var r,o=n(2115),i=n(5185),a=n(6101),s=n(6081),l=n(7650),d=n(9708),u=n(5155),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,d.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),f=n(9033),h=n(1595),p="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,s;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:d,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:x,onDismiss:w,...M}=e,k=o.useContext(m),[C,D]=o.useState(null),O=null!=(s=null==C?void 0:C.ownerDocument)?s:null==(n=globalThis)?void 0:n.document,[,S]=o.useState({}),T=(0,a.s)(t,e=>D(e)),E=Array.from(k.layers),[N]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),P=E.indexOf(N),_=C?E.indexOf(C):-1,W=k.layersWithOutsidePointerEventsDisabled.size>0,A=_>=P,I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){g("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));A&&!n&&(null==y||y(e),null==x||x(e),e.defaultPrevented||null==w||w())},O),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&g("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==v||v(e),null==x||x(e),e.defaultPrevented||null==w||w())},O);return(0,h.U)(e=>{_===k.layers.size-1&&(null==d||d(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},O),o.useEffect(()=>{if(C)return l&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(r=O.body.style.pointerEvents,O.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(C)),k.layers.add(C),b(),()=>{l&&1===k.layersWithOutsidePointerEventsDisabled.size&&(O.body.style.pointerEvents=r)}},[C,O,l,k]),o.useEffect(()=>()=>{C&&(k.layers.delete(C),k.layersWithOutsidePointerEventsDisabled.delete(C),b())},[C,k]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(p,e),()=>document.removeEventListener(p,e)},[]),(0,u.jsx)(c.div,{...M,ref:T,style:{pointerEvents:W?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,I.onPointerDownCapture)})});function b(){let e=new CustomEvent(p);document.dispatchEvent(e)}function g(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&l.flushSync(()=>i.dispatchEvent(a));else i.dispatchEvent(a)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(m),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(c.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var v=n(2293),x="focusScope.autoFocusOnMount",w="focusScope.autoFocusOnUnmount",M={bubbles:!1,cancelable:!0},k=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:s,...l}=e,[d,h]=o.useState(null),p=(0,f.c)(i),m=(0,f.c)(s),y=o.useRef(null),b=(0,a.s)(t,e=>h(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(g.paused||!d)return;let t=e.target;d.contains(t)?y.current=t:O(y.current,{select:!0})},t=function(e){if(g.paused||!d)return;let t=e.relatedTarget;null!==t&&(d.contains(t)||O(y.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&O(d)});return d&&n.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,d,g.paused]),o.useEffect(()=>{if(d){S.add(g);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(x,M);d.addEventListener(x,p),d.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(O(r,{select:t}),document.activeElement!==n)return}(C(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&O(d))}return()=>{d.removeEventListener(x,p),setTimeout(()=>{let t=new CustomEvent(w,M);d.addEventListener(w,m),d.dispatchEvent(t),t.defaultPrevented||O(null!=e?e:document.body,{select:!0}),d.removeEventListener(w,m),S.remove(g)},0)}}},[d,p,m,g]);let v=o.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=C(e);return[D(t,e),D(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&O(i,{select:!0})):(e.preventDefault(),n&&O(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,u.jsx)(c.div,{tabIndex:-1,...l,ref:b,onKeyDown:v})});function C(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function D(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function O(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}k.displayName="FocusScope";var S=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=T(e,t)).unshift(t)},remove(t){var n;null==(n=(e=T(e,t))[0])||n.resume()}}}();function T(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var E=n(1285),N=n(5773),P=n(2712),_=o.forwardRef((e,t)=>{var n,r;let{container:i,...a}=e,[s,d]=o.useState(!1);(0,P.N)(()=>d(!0),[]);let f=i||s&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return f?l.createPortal((0,u.jsx)(c.div,{...a,ref:t}),f):null});_.displayName="Portal";var W=n(8905),A=n(5845),I=n(8168),j=n(3795),R="Popover",[Y,L]=(0,s.A)(R,[N.Bk]),F=(0,N.Bk)(),[H,B]=Y(R),$=e=>{let{__scopePopover:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:s=!1}=e,l=F(t),d=o.useRef(null),[c,f]=o.useState(!1),[h,p]=(0,A.i)({prop:r,defaultProp:null!=i&&i,onChange:a,caller:R});return(0,u.jsx)(N.bL,{...l,children:(0,u.jsx)(H,{scope:t,contentId:(0,E.B)(),triggerRef:d,open:h,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:c,onCustomAnchorAdd:o.useCallback(()=>f(!0),[]),onCustomAnchorRemove:o.useCallback(()=>f(!1),[]),modal:s,children:n})})};$.displayName=R;var z="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=B(z,n),a=F(n),{onCustomAnchorAdd:s,onCustomAnchorRemove:l}=i;return o.useEffect(()=>(s(),()=>l()),[s,l]),(0,u.jsx)(N.Mz,{...a,...r,ref:t})}).displayName=z;var X="PopoverTrigger",Z=o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=B(X,n),s=F(n),l=(0,a.s)(t,o.triggerRef),d=(0,u.jsx)(c.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eo(o.open),...r,ref:l,onClick:(0,i.m)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?d:(0,u.jsx)(N.Mz,{asChild:!0,...s,children:d})});Z.displayName=X;var U="PopoverPortal",[q,V]=Y(U,{forceMount:void 0}),K=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,i=B(U,t);return(0,u.jsx)(q,{scope:t,forceMount:n,children:(0,u.jsx)(W.C,{present:n||i.open,children:(0,u.jsx)(_,{asChild:!0,container:o,children:r})})})};K.displayName=U;var Q="PopoverContent",G=o.forwardRef((e,t)=>{let n=V(Q,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,i=B(Q,e.__scopePopover);return(0,u.jsx)(W.C,{present:r||i.open,children:i.modal?(0,u.jsx)(ee,{...o,ref:t}):(0,u.jsx)(et,{...o,ref:t})})});G.displayName=Q;var J=(0,d.TL)("PopoverContent.RemoveScroll"),ee=o.forwardRef((e,t)=>{let n=B(Q,e.__scopePopover),r=o.useRef(null),s=(0,a.s)(t,r),l=o.useRef(!1);return o.useEffect(()=>{let e=r.current;if(e)return(0,I.Eq)(e)},[]),(0,u.jsx)(j.A,{as:J,allowPinchZoom:!0,children:(0,u.jsx)(en,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),l.current||null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;l.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),et=o.forwardRef((e,t)=>{let n=B(Q,e.__scopePopover),r=o.useRef(!1),i=o.useRef(!1);return(0,u.jsx)(en,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,a;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let s=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),en=o.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:d,onInteractOutside:c,...f}=e,h=B(Q,n),p=F(n);return(0,v.Oh)(),(0,u.jsx)(k,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,u.jsx)(y,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:c,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:d,onDismiss:()=>h.onOpenChange(!1),children:(0,u.jsx)(N.UC,{"data-state":eo(h.open),role:"dialog",id:h.contentId,...p,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),er="PopoverClose";function eo(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=B(er,n);return(0,u.jsx)(c.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=er,o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=F(n);return(0,u.jsx)(N.i3,{...o,...r,ref:t})}).displayName="PopoverArrow";var ei=$,ea=Z,es=K,el=G},4472:(e,t,n)=>{n.d(t,{C1:()=>M,bL:()=>w});var r=n(2115),o=n(6081);n(7650);var i=n(9708),a=n(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),l="Progress",[d,u]=(0,o.A)(l),[c,f]=d(l),h=r.forwardRef((e,t)=>{var n,r,o,i;let{__scopeProgress:l,value:d=null,max:u,getValueLabel:f=y,...h}=e;(u||0===u)&&!v(u)&&console.error((n="".concat(u),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=v(u)?u:100;null===d||x(d,p)||console.error((o="".concat(d),i="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let m=x(d,p)?d:null,w=g(m)?f(m,p):void 0;return(0,a.jsx)(c,{scope:l,value:m,max:p,children:(0,a.jsx)(s.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":w,role:"progressbar","data-state":b(m,p),"data-value":null!=m?m:void 0,"data-max":p,...h,ref:t})})});h.displayName=l;var p="ProgressIndicator",m=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...o}=e,i=f(p,r);return(0,a.jsx)(s.div,{"data-state":b(i.value,i.max),"data-value":null!=(n=i.value)?n:void 0,"data-max":i.max,...o,ref:t})});function y(e,t){return"".concat(Math.round(e/t*100),"%")}function b(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function x(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=p;var w=h,M=m},4631:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},4861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},7716:(e,t,n)=>{n.d(t,{d:()=>o});var r=n(9447);function o(e,t){return+(0,r.a)(e)>+(0,r.a)(t)}},8126:(e,t)=>{function n(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8794:(e,t,n)=>{n.d(t,{H:()=>a});var r=n(5703),o=n(7239),i=n(9447);function a(e,t){var n;let a,m,y=()=>(0,o.w)(null==t?void 0:t.in,NaN),b=null!=(n=null==t?void 0:t.additionalDigits)?n:2,g=function(e){let t,n={},r=e.split(s.dateTimeDelimiter);if(r.length>2)return n;if(/:/.test(r[0])?t=r[0]:(n.date=r[0],t=r[1],s.timeZoneDelimiter.test(n.date)&&(n.date=e.split(s.timeZoneDelimiter)[0],t=e.substr(n.date.length,e.length))),t){let e=s.timezone.exec(t);e?(n.time=t.replace(e[1],""),n.timezone=e[1]):n.time=t}return n}(e);if(g.date){let e=function(e,t){let n=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};let o=r[1]?parseInt(r[1]):null,i=r[2]?parseInt(r[2]):null;return{year:null===i?o:100*i,restDateString:e.slice((r[1]||r[2]).length)}}(g.date,b);a=function(e,t){var n,r,o,i,a,s,d,u;if(null===t)return new Date(NaN);let f=e.match(l);if(!f)return new Date(NaN);let m=!!f[4],y=c(f[1]),b=c(f[2])-1,g=c(f[3]),v=c(f[4]),x=c(f[5])-1;if(m){return(n=0,r=v,o=x,r>=1&&r<=53&&o>=0&&o<=6)?function(e,t,n){let r=new Date(0);r.setUTCFullYear(e,0,4);let o=r.getUTCDay()||7;return r.setUTCDate(r.getUTCDate()+((t-1)*7+n+1-o)),r}(t,v,x):new Date(NaN)}{let e=new Date(0);return(i=t,a=b,s=g,a>=0&&a<=11&&s>=1&&s<=(h[a]||(p(i)?29:28))&&(d=t,(u=y)>=1&&u<=(p(d)?366:365)))?(e.setUTCFullYear(t,b,Math.max(y,g)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!a||isNaN(+a))return y();let v=+a,x=0;if(g.time&&isNaN(x=function(e){var t,n,o;let i=e.match(d);if(!i)return NaN;let a=f(i[1]),s=f(i[2]),l=f(i[3]);return(t=a,n=s,o=l,24===t?0===n&&0===o:o>=0&&o<60&&n>=0&&n<60&&t>=0&&t<25)?a*r.s0+s*r.Cg+1e3*l:NaN}(g.time)))return y();if(g.timezone){if(isNaN(m=function(e){var t,n;if("Z"===e)return 0;let o=e.match(u);if(!o)return 0;let i="+"===o[1]?-1:1,a=parseInt(o[2]),s=o[3]&&parseInt(o[3])||0;return(t=0,(n=s)>=0&&n<=59)?i*(a*r.s0+s*r.Cg):NaN}(g.timezone)))return y()}else{let e=new Date(v+x),n=(0,i.a)(0,null==t?void 0:t.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return(0,i.a)(v+x+m,null==t?void 0:t.in)}let s={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},l=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,d=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,u=/^([+-])(\d{2})(?::?(\d{2}))?$/;function c(e){return e?parseInt(e):1}function f(e){return e&&parseFloat(e.replace(",","."))||0}let h=[31,null,31,30,31,30,31,31,30,31,30,31];function p(e){return e%400==0||e%4==0&&e%100!=0}},9074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9107:(e,t,n)=>{n.d(t,{e:()=>o});var r=n(714);function o(e,t,n){return(0,r.f)(e,-t,n)}}}]);
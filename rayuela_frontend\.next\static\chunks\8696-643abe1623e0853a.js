"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8696],{467:(e,t,r)=>{r.d(t,{K:()=>s,S:()=>l});var a=r(2656),n=r(5731);let i=(0,a._C)();async function l(e,t){try{let r={};return e&&(r.model_id=e),t&&(r.metric_type=t),await i.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(r)}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}async function s(){try{return await i.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(a){let e=a instanceof Error?a.message:"Error al obtener m\xe9tricas de confianza",t=a.status||500,r=a.body;throw new n.hD(e,t,r)}}},4556:(e,t,r)=>{r.d(t,{Q:()=>l});var a=r(6072),n=r(5731),i=r(2115);function l(){var e,t,r;let l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[s,o]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!1),[d,g]=(0,i.useState)(!1),[v,f]=(0,i.useState)(!1),[p,m]=(0,i.useState)(null),{data:h,error:b,isLoading:y,isValidating:w,mutate:x}=(0,a.Ay)("api-keys",async()=>await (0,n.PX)(),{revalidateOnFocus:null==(e=l.revalidateOnFocus)||e,refreshInterval:l.refreshInterval,dedupingInterval:null!=(t=l.dedupingInterval)?t:6e4,errorRetryCount:null!=(r=l.errorRetryCount)?r:3,onError:e=>{console.error("Error fetching API keys:",e)}}),A=(null==h?void 0:h.api_keys)&&h.api_keys.length>0?h.api_keys.find(e=>e.is_active)||h.api_keys[0]:null,C=async e=>{o(!0),m(null);try{let t={name:e.name||"",permissions:[]},r=await (0,n.Iq)(t);return await x(),r}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al crear API Key",500);throw m(e),e}finally{o(!1)}},k=async(e,t)=>{u(!0),m(null);try{let r={name:t.name||void 0,permissions:[]},a=await (0,n.XW)(e.toString(),r);return await x(),a}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al actualizar API Key",500);throw m(e),e}finally{u(!1)}},D=async e=>{g(!0),m(null);try{return await (0,n.mA)(e),await x(),!0}catch(t){let e=t instanceof n.hD?t:new n.hD("Error al revocar API Key",500);throw m(e),e}finally{g(!1)}},S=async()=>{f(!0),m(null);try{let e=await (0,n.Iq)({name:"API Key ".concat(new Date().toLocaleDateString("es-ES"))});return await x(),e}catch(e){return m(e instanceof n.hD?e:new n.hD("Error al regenerar API Key",500)),null}finally{f(!1)}};return{data:null!=h?h:null,primaryKey:null!=A?A:null,error:null!=b?b:null,isLoading:y,isValidating:w,mutate:x,dataUpdatedAt:0,createApiKey:C,updateApiKey:k,revokeApiKey:D,regenerateApiKey:S,isCreating:s,isUpdating:c,isRevoking:d,isRegenerating:v,operationError:p,getFormattedApiKey:e=>{let t=e||A;return(null==t?void 0:t.prefix)&&(null==t?void 0:t.last_chars)?"".concat(t.prefix,"••••••••").concat(t.last_chars):null}}}},6126:(e,t,r)=>{r.d(t,{E:()=>s});var a=r(5155);r(2115);var n=r(2085),i=r(9434);let l=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 active:scale-95",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80 active:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80 active:bg-destructive/90",success:"border-transparent bg-success text-success-foreground hover:bg-success/80 active:bg-success/90 dark:bg-success/20 dark:text-success dark:border-success/40",warning:"border-transparent bg-warning text-warning-foreground hover:bg-warning/80 active:bg-warning/90 dark:bg-warning/20 dark:text-warning dark:border-warning/40",info:"border-transparent bg-info text-info-foreground hover:bg-info/80 active:bg-info/90 dark:bg-info/20 dark:text-info dark:border-info/40",outline:"text-foreground hover:bg-accent hover:text-accent-foreground","outline-success":"border-success/40 text-success hover:bg-success/15 active:bg-success/25 dark:border-success/50 dark:hover:bg-success/20","outline-warning":"border-warning/40 text-warning hover:bg-warning/15 active:bg-warning/25 dark:border-warning/50 dark:hover:bg-warning/20","outline-info":"border-info/40 text-info hover:bg-info/15 active:bg-info/25 dark:border-info/50 dark:hover:bg-info/20"}},defaultVariants:{variant:"default"}});function s(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:r}),t),...n})}},6695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>s,Zp:()=>i,aR:()=>l,wL:()=>u});var a=r(5155);r(2115);var n=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border shadow-sm","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("text-subheading rayuela-accent",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-caption",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},7018:(e,t,r)=>{r.d(t,{q:()=>d});var a=r(5155),n=r(2115),i=r(285),l=r(3999),s=r(5731),o=r(6671),c=r(4631),u=r(3786);function d(e){let{children:t,className:r,variant:d="outline",...g}=e,{token:v,apiKey:f}=(0,l.A)(),[p,m]=(0,n.useState)(!1),h=async()=>{if(!v||!f)return void o.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");m(!0);try{let e=await (0,s.oE)();if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n del Portal de Facturaci\xf3n:",e),o.o.error(e.message||"Error al acceder al portal de facturaci\xf3n"),m(!1)}};return(0,a.jsx)(i.Button,{onClick:h,disabled:p,className:r,variant:d,...g,children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Redirigiendo..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),t||"Gestionar Facturaci\xf3n"]})})}},7916:(e,t,r)=>{r.d(t,{s:()=>d});var a=r(5155),n=r(2115),i=r(285),l=r(3999),s=r(5731),o=r(6671),c=r(4631),u=r(1586);function d(e){let{priceId:t,planName:r,actionType:d="subscribe",children:g,className:v,variant:f="default",...p}=e,{token:m,apiKey:h}=(0,l.A)(),[b,y]=(0,n.useState)(!1),w=async()=>{if(!m||!h)return void o.o.error("Debes iniciar sesi\xf3n para realizar esta acci\xf3n");y(!0);try{if("contact"===d){window.location.href="/contact-sales";return}let e=await (0,s.fw)(t);if(e.url)window.location.href=e.url;else throw Error("No se recibi\xf3 una URL de redirecci\xf3n")}catch(e){console.error("Error al crear sesi\xf3n de checkout:",e),o.o.error(e.message||"Error al procesar la suscripci\xf3n al plan ".concat(r)),y(!1)}};return(0,a.jsx)(i.Button,{onClick:w,disabled:b,className:v,variant:f,...p,children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Procesando..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),g||("upgrade"===d?"Actualizar a ".concat(r):"downgrade"===d?"Cambiar a ".concat(r):"contact"===d?"Contactar con Ventas":"Suscribirse a ".concat(r))]})})}},8534:(e,t,r)=>{r.d(t,{mm:()=>s,vK:()=>c});var a=r(5155);r(2115);var n=r(9434);let i={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",xl:"h-8 w-8","2xl":"h-12 w-12"},l={success:"text-success",warning:"text-warning",error:"text-destructive",info:"text-info",primary:"text-primary",secondary:"text-secondary-foreground",muted:"text-muted-foreground",interactive:"text-primary hover:text-primary/80",neutral:"text-foreground",subtle:"text-muted-foreground",metric:"text-primary",action:"text-primary",navigation:"text-muted-foreground hover:text-foreground"};function s(e){let{icon:t,size:r="md",context:s="neutral",className:o,"aria-label":c,"aria-hidden":u=!c,...d}=e;return(0,a.jsx)(t,{className:(0,n.cn)(i[r],l[s],"shrink-0",o),"aria-label":c,"aria-hidden":u,...d})}let o={tight:"gap-1",normal:"gap-2",loose:"gap-3"};function c(e){let{icon:t,children:r,size:i="sm",context:l="neutral",iconPosition:c="left",spacing:u="normal",className:d}=e,g=(0,a.jsx)(s,{icon:t,size:i,context:l,"aria-hidden":!0});return(0,a.jsxs)("span",{className:(0,n.cn)("inline-flex items-center",o[u],d),children:["left"===c&&g,r,"right"===c&&g]})}},8856:(e,t,r)=>{r.d(t,{E:()=>i});var a=r(5155),n=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("animate-pulse rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-shimmer",t),...r})}},9859:(e,t,r)=>{r.d(t,{T4:()=>o,As:()=>a.A,A$:()=>l,Xn:()=>d,TB:()=>c});var a=r(3999),n=r(6072),i=r(5731);function l(){let{token:e,apiKey:t}=(0,a.A)(),{data:r,error:l,isLoading:s,mutate:o}=(0,n.Ay)(e&&t?["plans",e,t]:null,async()=>await (0,i.T9)(),{refreshInterval:3e5,revalidateOnFocus:!1});return{plans:r||{},error:l,isLoading:s,refresh:o,getPlanLimits:e=>{if(!r)return null;let t=r[e];return(null==t?void 0:t.limits)||null},getPlanById:e=>r&&r[e]||null,getAllPlans:()=>r?Object.values(r):[],getPlanName:e=>{if(!r)return e;let t=r[e];return(null==t?void 0:t.name)||e}}}var s=r(2656);function o(){var e,t,r;let l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{token:o,apiKey:c}=(0,a.A)(),{data:u,error:d,isLoading:g,isValidating:v,mutate:f}=(0,n.Ay)(o&&c?["account-info",o,c]:null,async()=>await (0,i.Dm)(),{revalidateOnFocus:null!=(e=l.revalidateOnFocus)&&e,refreshInterval:l.refreshInterval,dedupingInterval:null!=(t=l.dedupingInterval)?t:6e4,errorRetryCount:null!=(r=l.errorRetryCount)?r:3}),p=async e=>{if(!o||!c)throw Error("No token or API key available");try{let t=(await (0,s._C)().patchCurrentAccountApiV1AccountsCurrentPatch({onboardingChecklistStatus:e})).data;return await f(t,{revalidate:!1}),t}catch(r){console.error("Error updating checklist status:",r);let t=u?{...u,onboardingChecklistStatus:e}:void 0;throw await f(t,{revalidate:!1}),r}};return{accountData:u,error:d,isLoading:g,isValidating:v,refresh:f,lastUpdated:null,getCreationDate:()=>u&&u.createdAt?new Date(u.createdAt):null,isActive:()=>!!u&&u.isActive,getSubscriptionPlan:()=>u&&u.subscription?u.subscription.plan:null,isSubscriptionActive:()=>!!u&&!!u.subscription&&u.subscription.isActive,getSubscriptionExpiryDate:()=>u&&u.subscription&&u.subscription.expiresAt?new Date(u.subscription.expiresAt):null,getChecklistStatus:()=>u&&u.onboardingChecklistStatus?u.onboardingChecklistStatus:{},updateChecklistStatus:p}}function c(){let{token:e,apiKey:t}=(0,a.A)(),{data:r,error:l,isLoading:s,mutate:o}=(0,n.Ay)(e&&t?["usage-summary",e,t]:null,async()=>await (0,i.S3)(),{refreshInterval:3e4,revalidateOnFocus:!0}),c=()=>{var e;return r&&(null==(e=r.apiCalls)?void 0:e.percentage)||0},u=()=>{var e;return r&&(null==(e=r.storage)?void 0:e.percentage)||0};return{usageData:r,error:l,isLoading:s,mutate:o,getApiCallsUsed:()=>{var e;return(null==r||null==(e=r.apiCalls)?void 0:e.used)||0},getApiCallsLimit:()=>{var e;return(null==r||null==(e=r.apiCalls)?void 0:e.limit)||0},getApiCallsRemaining:()=>{var e,t;let a=(null==r||null==(e=r.apiCalls)?void 0:e.used)||0;return Math.max(0,((null==r||null==(t=r.apiCalls)?void 0:t.limit)||0)-a)},getStorageUsed:()=>{var e;return(null==r||null==(e=r.storage)?void 0:e.usedBytes)||0},getStorageLimit:()=>{var e;return(null==r||null==(e=r.storage)?void 0:e.limitBytes)||0},getStorageRemaining:()=>{var e,t;let a=(null==r||null==(e=r.storage)?void 0:e.usedBytes)||0;return Math.max(0,((null==r||null==(t=r.storage)?void 0:t.limitBytes)||0)-a)},hasUsageActivity:()=>{var e;return((null==r||null==(e=r.apiCalls)?void 0:e.used)||0)>0},getApiCallsPercentage:c,getStoragePercentage:u,canTrainNow:()=>{var e;return!!r&&((null==(e=r.training)?void 0:e.canTrainNow)||!1)},getNextTrainingDate:()=>{var e;return r&&(null==(e=r.training)?void 0:e.nextAvailable)?new Date(r.training.nextAvailable):null},getLastStorageMeasurement:()=>{var e;return r&&(null==(e=r.storage)?void 0:e.lastMeasured)||null},getNextApiCallsReset:()=>{var e;return r&&(null==(e=r.apiCalls)?void 0:e.resetDate)?new Date(r.apiCalls.resetDate):null},isApiCallsLimitReached:()=>!!r&&c()>=100,isStorageLimitReached:()=>!!r&&u()>=100,getStorageUsedFormatted:()=>{var e;if(!r)return"0 B";let t=(null==(e=r.storage)?void 0:e.usedBytes)||0;if(0===t)return"0 B";let a=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB","TB"][a]},getStorageLimitFormatted:()=>{var e;if(!r)return"0 B";let t=(null==(e=r.storage)?void 0:e.limitBytes)||0,a=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB","TB"][a]},getApiCallsUsedFormatted:()=>{var e;return r?((null==(e=r.apiCalls)?void 0:e.used)||0).toLocaleString():"0"},getApiCallsLimitFormatted:()=>{var e;return r?((null==(e=r.apiCalls)?void 0:e.limit)||0).toLocaleString():"0"},getAvailableModels:()=>{var e;return r&&(null==(e=r.planLimits)?void 0:e.availableModels)||[]},getMaxRequestsPerMinute:()=>{var e;return r&&(null==(e=r.planLimits)?void 0:e.maxRequestsPerMinute)||0}}}function u(e){return!!e&&"object"==typeof e&&"string"==typeof e.date&&"number"==typeof e.api_calls&&"number"==typeof e.storage}function d(e,t){let{token:r,apiKey:l}=(0,a.A)(),s=null==e?void 0:e.toISOString().split("T")[0],o=null==t?void 0:t.toISOString().split("T")[0],{data:c,error:d,isLoading:g,mutate:v}=(0,n.Ay)(r&&l&&s&&o?["usage-history",r,l,s,o]:null,async()=>await (0,i.M2)(),{refreshInterval:6e4,revalidateOnFocus:!0,onError:e=>{e instanceof i.hD?console.error("Error fetching usage history:",e.message,e.body):console.error("Unexpected error:",e)}});return{data:c,error:d,isLoading:g,mutate:v,getTotalApiCalls:()=>c?c.reduce((e,t)=>u(t)?e+t.api_calls:e,0):0,getPeakUsageDay:()=>{if(!c||0===c.length)return null;let e=c.filter(u);if(0===e.length)return null;let t=e.reduce((e,t)=>t.api_calls>e.api_calls?t:e,e[0]);return{date:new Date(t.date),apiCalls:t.api_calls}},getLatestStorageUsage:()=>{if(!c||0===c.length)return 0;let e=c.filter(u);return 0===e.length?0:e.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())[0].storage},getChartData:()=>c?c.filter(u).sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()).map(e=>({date:new Date(e.date),apiCalls:e.api_calls,storage:e.storage})):[],getGrowthRate:e=>{if(!c||c.length<2)return 0;let t=c.filter(u);if(t.length<2)return 0;let r=t.sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),a="apiCalls"===e?r[0].api_calls:r[0].storage,n="apiCalls"===e?r[r.length-1].api_calls:r[r.length-1].storage;return 0===a?100*(n>0):(n-a)/a*100}}}r(4556),r(467),r(2115)}}]);
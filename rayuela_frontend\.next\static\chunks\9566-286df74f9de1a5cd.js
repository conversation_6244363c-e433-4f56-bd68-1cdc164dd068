"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9566],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1414:(e,t,n)=>{e.exports=n(2436)},1684:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},1788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2436:(e,t,n)=>{var r=n(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,o=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),i=r[0].inst,c=r[1];return l(function(){i.value=n,i.getSnapshot=t,s(i)&&c({inst:i})},[e,n,t]),o(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},4357:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4927:(e,t,n)=>{n.d(t,{C1:()=>R,bL:()=>O});var r=n(2115),i=n(6101),a=n(6081),o=n(5185),l=n(5845),u=n(5503),s=n(1275),c=n(2712),d=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[i,a]=r.useState(),o=r.useRef(null),l=r.useRef(e),u=r.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(o.current);u.current="mounted"===s?e:"none"},[s]),(0,c.N)(()=>{let t=o.current,n=l.current;if(n!==e){let r=u.current,i=f(t);e?d("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==i?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),(0,c.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=f(o.current).includes(e.animationName);if(e.target===i&&r&&(d("ANIMATION_END"),!l.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},a=e=>{e.target===i&&(u.current=f(o.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,a(e)},[])}}(t),o="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),l=(0,i.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||a.isPresent?r.cloneElement(o,{ref:l}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}d.displayName="Presence";var p=n(3540),y=n(5155),g="Checkbox",[v,m]=(0,a.A)(g),[h,w]=v(g);function b(e){let{__scopeCheckbox:t,checked:n,children:i,defaultChecked:a,disabled:o,form:u,name:s,onCheckedChange:c,required:d,value:f="on",internal_do_not_use_render:p}=e,[v,m]=(0,l.i)({prop:n,defaultProp:null!=a&&a,onChange:c,caller:g}),[w,b]=r.useState(null),[k,E]=r.useState(null),O=r.useRef(!1),S=!w||!!u||!!w.closest("form"),R={checked:v,disabled:o,setChecked:m,control:w,setControl:b,name:s,form:u,value:f,hasConsumerStoppedPropagationRef:O,required:d,defaultChecked:!x(a)&&a,isFormControl:S,bubbleInput:k,setBubbleInput:E};return(0,y.jsx)(h,{scope:t,...R,children:"function"==typeof p?p(R):i})}var k="CheckboxTrigger",E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:a,onClick:l,...u}=e,{control:s,value:c,disabled:d,checked:f,required:g,setControl:v,setChecked:m,hasConsumerStoppedPropagationRef:h,isFormControl:b,bubbleInput:E}=w(k,n),O=(0,i.s)(t,v),S=r.useRef(f);return r.useEffect(()=>{let e=null==s?void 0:s.form;if(e){let t=()=>m(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,m]),(0,y.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":x(f)?"mixed":f,"aria-required":g,"data-state":N(f),"data-disabled":d?"":void 0,disabled:d,value:c,...u,ref:O,onKeyDown:(0,o.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(l,e=>{m(e=>!!x(e)||!e),E&&b&&(h.current=e.isPropagationStopped(),h.current||e.stopPropagation())})})});E.displayName=k;var O=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:i,defaultChecked:a,required:o,disabled:l,value:u,onCheckedChange:s,form:c,...d}=e;return(0,y.jsx)(b,{__scopeCheckbox:n,checked:i,defaultChecked:a,disabled:l,required:o,onCheckedChange:s,name:r,form:c,value:u,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(E,{...d,ref:t,__scopeCheckbox:n}),r&&(0,y.jsx)(T,{__scopeCheckbox:n})]})}})});O.displayName=g;var S="CheckboxIndicator",R=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...i}=e,a=w(S,n);return(0,y.jsx)(d,{present:r||x(a.checked)||!0===a.checked,children:(0,y.jsx)(p.sG.span,{"data-state":N(a.checked),"data-disabled":a.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});R.displayName=S;var _="CheckboxBubbleInput",T=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...a}=e,{control:o,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:d,required:f,disabled:g,name:v,value:m,form:h,bubbleInput:b,setBubbleInput:k}=w(_,n),E=(0,i.s)(t,k),O=(0,u.Z)(c),S=(0,s.X)(o);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(O!==c&&e){let n=new Event("click",{bubbles:t});b.indeterminate=x(c),e.call(b,!x(c)&&c),b.dispatchEvent(n)}},[b,O,c,l]);let R=r.useRef(!x(c)&&c);return(0,y.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=d?d:R.current,required:f,disabled:g,name:v,value:m,form:h,...a,tabIndex:-1,ref:E,style:{...a.style,...S,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function x(e){return"indeterminate"===e}function N(e){return x(e)?"indeterminate":e?"checked":"unchecked"}T.displayName=_},6072:(e,t,n)=>{let r;n.d(t,{Ay:()=>ei});var i=n(2115),a=n(1414),o=Object.prototype.hasOwnProperty;let l=new WeakMap,u=()=>{},s=u(),c=Object,d=e=>e===s,f=e=>"function"==typeof e,p=(e,t)=>({...e,...t}),y=e=>f(e.then),g={},v={},m="undefined",h=typeof window!=m,w=typeof document!=m,b=h&&"Deno"in window,k=()=>h&&typeof window.requestAnimationFrame!=m,E=(e,t)=>{let n=l.get(e);return[()=>!d(t)&&e.get(t)||g,r=>{if(!d(t)){let i=e.get(t);t in v||(v[t]=i),n[5](t,p(i,r),i||g)}},n[6],()=>!d(t)&&t in v?v[t]:!d(t)&&e.get(t)||g]},O=!0,[S,R]=h&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[u,u],_={initFocus:e=>(w&&document.addEventListener("visibilitychange",e),S("focus",e),()=>{w&&document.removeEventListener("visibilitychange",e),R("focus",e)}),initReconnect:e=>{let t=()=>{O=!0,e()},n=()=>{O=!1};return S("online",t),S("offline",n),()=>{R("online",t),R("offline",n)}}},T=!i.useId,x=!h||b,N=e=>k()?window.requestAnimationFrame(e):setTimeout(e,1),L=x?i.useEffect:i.useLayoutEffect,j="undefined"!=typeof navigator&&navigator.connection,A=!x&&j&&(["slow-2g","2g"].includes(j.effectiveType)||j.saveData),C=new WeakMap,M=(e,t)=>c.prototype.toString.call(e)==="[object ".concat(t,"]"),D=0,I=e=>{let t,n,r=typeof e,i=M(e,"Date"),a=M(e,"RegExp"),o=M(e,"Object");if(c(e)!==e||i||a)t=i?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=C.get(e))return t;if(t=++D+"~",C.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=I(e[n])+",";C.set(e,t)}if(o){t="#";let r=c.keys(e).sort();for(;!d(n=r.pop());)d(e[n])||(t+=n+":"+I(e[n])+",");C.set(e,t)}}return t},V=e=>{if(f(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?I(e):"",t]},P=0,F=()=>++P;async function U(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,a,o]=t,u=p({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),c=u.populateCache,g=u.rollbackOnError,v=u.optimisticData,m=e=>"function"==typeof g?g(e):!1!==g,h=u.throwOnError;if(f(i)){let e=[];for(let t of r.keys())!/^\$(inf|sub)\$/.test(t)&&i(r.get(t)._k)&&e.push(t);return Promise.all(e.map(w))}return w(i);async function w(e){let n,[i]=V(e);if(!i)return;let[o,p]=E(r,i),[g,w,b,k]=l.get(r),O=()=>{let t=g[i];return(f(u.revalidate)?u.revalidate(o().data,e):!1!==u.revalidate)&&(delete b[i],delete k[i],t&&t[0])?t[0](2).then(()=>o().data):o().data};if(t.length<3)return O();let S=a,R=F();w[i]=[R,0];let _=!d(v),T=o(),x=T.data,N=T._c,L=d(N)?x:N;if(_&&p({data:v=f(v)?v(L,x):v,_c:L}),f(S))try{S=S(L)}catch(e){n=e}if(S&&y(S)){if(S=await S.catch(e=>{n=e}),R!==w[i][0]){if(n)throw n;return S}n&&_&&m(n)&&(c=!0,p({data:L,_c:s}))}if(c&&!n&&(f(c)?p({data:c(S,L),error:s,_c:s}):p({data:S,error:s,_c:s})),w[i][1]=F(),Promise.resolve(O()).then(()=>{p({_c:s})}),n){if(h)throw n;return}return S}}let W=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},q=(e,t)=>{if(!l.has(e)){let n=p(_,t),r=Object.create(null),i=U.bind(s,e),a=u,o=Object.create(null),c=(e,t)=>{let n=o[e]||[];return o[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},d=(t,n,r)=>{e.set(t,n);let i=o[t];if(i)for(let e of i)e(n,r)},f=()=>{if(!l.has(e)&&(l.set(e,[r,Object.create(null),Object.create(null),Object.create(null),i,d,c]),!x)){let t=n.initFocus(setTimeout.bind(s,W.bind(s,r,0))),i=n.initReconnect(setTimeout.bind(s,W.bind(s,r,1)));a=()=>{t&&t(),i&&i(),l.delete(e)}}};return f(),[e,i,f,a]}return[e,l.get(e)[4]]},[$,z]=q(new Map),G=p({onLoadingSlow:u,onSuccess:u,onError:u,onErrorRetry:(e,t,n,r,i)=>{let a=n.errorRetryCount,o=i.retryCount,l=~~((Math.random()+.5)*(1<<(o<8?o:8)))*n.errorRetryInterval;(d(a)||!(o>a))&&setTimeout(r,l,i)},onDiscarded:u,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:A?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:A?5e3:3e3,compare:function e(t,n){var r,i;if(t===n)return!0;if(t&&n&&(r=t.constructor)===n.constructor){if(r===Date)return t.getTime()===n.getTime();if(r===RegExp)return t.toString()===n.toString();if(r===Array){if((i=t.length)===n.length)for(;i--&&e(t[i],n[i]););return -1===i}if(!r||"object"==typeof t){for(r in i=0,t)if(o.call(t,r)&&++i&&!o.call(n,r)||!(r in n)||!e(t[r],n[r]))return!1;return Object.keys(n).length===i}}return t!=t&&n!=n},isPaused:()=>!1,cache:$,mutate:z,fallback:{}},{isOnline:()=>O,isVisible:()=>{let e=w&&document.visibilityState;return d(e)||"hidden"!==e}}),H=(e,t)=>{let n=p(e,t);if(t){let{use:r,fallback:i}=e,{use:a,fallback:o}=t;r&&a&&(n.use=r.concat(a)),i&&o&&(n.fallback=p(i,o))}return n},J=(0,i.createContext)({}),X=h&&window.__SWR_DEVTOOLS_USE__,B=X?window.__SWR_DEVTOOLS_USE__:[],K=e=>f(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],Z=()=>p(G,(0,i.useContext)(J)),Q=B.concat(e=>(t,n,r)=>{let i=n&&((...e)=>{let[r]=V(t),[,,,i]=l.get($);if(r.startsWith("$inf$"))return n(...e);let a=i[r];return d(a)?n(...e):(delete i[r],a)});return e(t,i,r)}),Y=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}};X&&(window.__SWR_DEVTOOLS_REACT__=i);let ee=()=>{},et=ee();new WeakMap;let en=i.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),er={dedupe:!0};c.defineProperty(e=>{let{value:t}=e,n=(0,i.useContext)(J),r=f(t),a=(0,i.useMemo)(()=>r?t(n):t,[r,n,t]),o=(0,i.useMemo)(()=>r?a:H(n,a),[r,n,a]),l=a&&a.provider,u=(0,i.useRef)(s);l&&!u.current&&(u.current=q(l(o.cache||$),a));let c=u.current;return c&&(o.cache=c[0],o.mutate=c[1]),L(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,i.createElement)(J.Provider,p(e,{value:o}))},"defaultValue",{value:G});let ei=(r=(e,t,n)=>{let{cache:r,compare:o,suspense:u,fallbackData:c,revalidateOnMount:g,revalidateIfStale:v,refreshInterval:m,refreshWhenHidden:h,refreshWhenOffline:w,keepPreviousData:b}=n,[k,O,S,R]=l.get(r),[_,j]=V(e),A=(0,i.useRef)(!1),C=(0,i.useRef)(!1),M=(0,i.useRef)(_),D=(0,i.useRef)(t),I=(0,i.useRef)(n),P=()=>I.current,W=()=>P().isVisible()&&P().isOnline(),[q,$,z,G]=E(r,_),H=(0,i.useRef)({}).current,J=d(c)?d(n.fallback)?s:n.fallback[_]:c,X=(e,t)=>{for(let n in H)if("data"===n){if(!o(e[n],t[n])&&(!d(e[n])||!o(eo,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},B=(0,i.useMemo)(()=>{let e=!!_&&!!t&&(d(g)?!P().isPaused()&&!u&&!1!==v:g),n=t=>{let n=p(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=q(),i=G(),a=n(r),o=r===i?a:n(i),l=a;return[()=>{let e=n(q());return X(e,l)?(l.data=e.data,l.isLoading=e.isLoading,l.isValidating=e.isValidating,l.error=e.error,l):(l=e,e)},()=>o]},[r,_]),K=(0,a.useSyncExternalStore)((0,i.useCallback)(e=>z(_,(t,n)=>{X(n,t)||e()}),[r,_]),B[0],B[1]),Z=!A.current,Q=k[_]&&k[_].length>0,ee=K.data,et=d(ee)?J&&y(J)?en(J):J:ee,ei=K.error,ea=(0,i.useRef)(et),eo=b?d(ee)?d(ea.current)?et:ea.current:ee:et,el=(!Q||!!d(ei))&&(Z&&!d(g)?g:!P().isPaused()&&(u?!d(et)&&v:d(et)||v)),eu=!!(_&&t&&Z&&el),es=d(K.isValidating)?eu:K.isValidating,ec=d(K.isLoading)?eu:K.isLoading,ed=(0,i.useCallback)(async e=>{let t,r,i=D.current;if(!_||!i||C.current||P().isPaused())return!1;let a=!0,l=e||{},u=!S[_]||!l.dedupe,c=()=>T?!C.current&&_===M.current&&A.current:_===M.current,p={isValidating:!1,isLoading:!1},y=()=>{$(p)},g=()=>{let e=S[_];e&&e[1]===r&&delete S[_]},v={isValidating:!0};d(q().data)&&(v.isLoading=!0);try{if(u&&($(v),n.loadingTimeout&&d(q().data)&&setTimeout(()=>{a&&c()&&P().onLoadingSlow(_,n)},n.loadingTimeout),S[_]=[i(j),F()]),[t,r]=S[_],t=await t,u&&setTimeout(g,n.dedupingInterval),!S[_]||S[_][1]!==r)return u&&c()&&P().onDiscarded(_),!1;p.error=s;let e=O[_];if(!d(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return y(),u&&c()&&P().onDiscarded(_),!1;let l=q().data;p.data=o(l,t)?l:t,u&&c()&&P().onSuccess(t,_,n)}catch(n){g();let e=P(),{shouldRetryOnError:t}=e;!e.isPaused()&&(p.error=n,u&&c()&&(e.onError(n,_,e),(!0===t||f(t)&&t(n))&&(!P().revalidateOnFocus||!P().revalidateOnReconnect||W())&&e.onErrorRetry(n,_,e,e=>{let t=k[_];t&&t[0]&&t[0](3,e)},{retryCount:(l.retryCount||0)+1,dedupe:!0})))}return a=!1,y(),!0},[_,r]),ef=(0,i.useCallback)((...e)=>U(r,M.current,...e),[]);if(L(()=>{D.current=t,I.current=n,d(ee)||(ea.current=ee)}),L(()=>{if(!_)return;let e=ed.bind(s,er),t=0;P().revalidateOnFocus&&(t=Date.now()+P().focusThrottleInterval);let n=Y(_,k,(n,r={})=>{if(0==n){let n=Date.now();P().revalidateOnFocus&&n>t&&W()&&(t=n+P().focusThrottleInterval,e())}else if(1==n)P().revalidateOnReconnect&&W()&&e();else if(2==n)return ed();else if(3==n)return ed(r)});return C.current=!1,M.current=_,A.current=!0,$({_k:j}),el&&(d(et)||x?e():N(e)),()=>{C.current=!0,n()}},[_]),L(()=>{let e;function t(){let t=f(m)?m(q().data):m;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!q().error&&(h||P().isVisible())&&(w||P().isOnline())?ed(er).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[m,h,w,_]),(0,i.useDebugValue)(eo),u&&d(et)&&_){if(!T&&x)throw Error("Fallback data is required when using Suspense in SSR.");D.current=t,I.current=n,C.current=!1;let e=R[_];if(d(e)||en(ef(e)),d(ei)){let e=ed(er);d(eo)||(e.status="fulfilled",e.value=!0),en(e)}else throw ei}return{mutate:ef,get data(){return H.data=!0,eo},get error(){return H.error=!0,ei},get isValidating(){return H.isValidating=!0,es},get isLoading(){return H.isLoading=!0,ec}}},function(...e){let t=Z(),[n,i,a]=K(e),o=H(t,a),l=r,{use:u}=o,s=(u||[]).concat(Q);for(let e=s.length;e--;)l=s[e](l);return l(n,i||o.fetcher||null,o)})}}]);
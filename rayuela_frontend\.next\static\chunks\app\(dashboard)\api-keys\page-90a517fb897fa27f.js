(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2032],{4556:(e,s,a)=>{"use strict";a.d(s,{Q:()=>n});var t=a(6072),r=a(5731),l=a(2115);function n(){var e,s,a;let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[i,c]=(0,l.useState)(!1),[d,o]=(0,l.useState)(!1),[m,x]=(0,l.useState)(!1),[u,h]=(0,l.useState)(!1),[p,j]=(0,l.useState)(null),{data:f,error:v,isLoading:y,isValidating:N,mutate:g}=(0,t.Ay)("api-keys",async()=>await (0,r.PX)(),{revalidateOnFocus:null==(e=n.revalidateOnFocus)||e,refreshInterval:n.refreshInterval,dedupingInterval:null!=(s=n.dedupingInterval)?s:6e4,errorRetryCount:null!=(a=n.errorRetryCount)?a:3,onError:e=>{console.error("Error fetching API keys:",e)}}),b=(null==f?void 0:f.api_keys)&&f.api_keys.length>0?f.api_keys.find(e=>e.is_active)||f.api_keys[0]:null,A=async e=>{c(!0),j(null);try{let s={name:e.name||"",permissions:[]},a=await (0,r.Iq)(s);return await g(),a}catch(s){let e=s instanceof r.hD?s:new r.hD("Error al crear API Key",500);throw j(e),e}finally{c(!1)}},w=async(e,s)=>{o(!0),j(null);try{let a={name:s.name||void 0,permissions:[]},t=await (0,r.XW)(e.toString(),a);return await g(),t}catch(s){let e=s instanceof r.hD?s:new r.hD("Error al actualizar API Key",500);throw j(e),e}finally{o(!1)}},I=async e=>{x(!0),j(null);try{return await (0,r.mA)(e),await g(),!0}catch(s){let e=s instanceof r.hD?s:new r.hD("Error al revocar API Key",500);throw j(e),e}finally{x(!1)}},P=async()=>{h(!0),j(null);try{let e=await (0,r.Iq)({name:"API Key ".concat(new Date().toLocaleDateString("es-ES"))});return await g(),e}catch(e){return j(e instanceof r.hD?e:new r.hD("Error al regenerar API Key",500)),null}finally{h(!1)}};return{data:null!=f?f:null,primaryKey:null!=b?b:null,error:null!=v?v:null,isLoading:y,isValidating:N,mutate:g,dataUpdatedAt:0,createApiKey:A,updateApiKey:w,revokeApiKey:I,regenerateApiKey:P,isCreating:i,isUpdating:d,isRevoking:m,isRegenerating:u,operationError:p,getFormattedApiKey:e=>{let s=e||b;return(null==s?void 0:s.prefix)&&(null==s?void 0:s.last_chars)?"".concat(s.prefix,"••••••••").concat(s.last_chars):null}}}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(5155);a(2115);var r=a(968),l=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},5127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>n,nA:()=>m,nd:()=>o});var t=a(5155),r=a(2115),l=a(9434);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto rounded-lg border bg-card shadow-sm",children:(0,t.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",a),...r})})});n.displayName="Table";let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("thead",{ref:s,className:(0,l.cn)("bg-muted/30 [&_tr]:border-b-0 [&_tr]:shadow-sm",a),...r})});i.displayName="TableHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",a),...r})});c.displayName="TableBody",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tfoot",{ref:s,className:(0,l.cn)("bg-muted/30 font-medium shadow-sm [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tr",{ref:s,className:(0,l.cn)("border-b border-border/50 transition-all hover:bg-muted/30 hover:shadow-xs data-[state=selected]:bg-muted/50 data-[state=selected]:shadow-xs",a),...r})});d.displayName="TableRow";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-semibold text-foreground [&:has([role=checkbox])]:pr-0",a),...r})});o.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});m.displayName="TableCell",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},6179:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>ea});var t=a(5155),r=a(2115),l=a(285),n=a(6695),i=a(8856),c=a(5127),d=a(6126),o=a(5365),m=a(3999),x=a(4556),u=a(6671),h=a(7588),p=a(3008),j=a(3439),f=a(5339),v=a(9803),y=a(4616),N=a(6932),g=a(7924),b=a(4357),A=a(2713),w=a(3717),I=a(2525),P=a(1284),K=a(5525),C=a(6785),_=a(7580),k=a(6874),E=a.n(k),z=a(8868),R=a(9434);function S(e){let{children:s,variant:a="default",className:r,...l}=e;return(0,t.jsx)("div",{className:(0,R.cn)({default:"",subtle:"bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6",elevated:"bg-card border border-border shadow-sm rounded-lg p-6"}[a],r),...l,children:s})}function D(e){let{variant:s="line",spacing:a="md",className:r,...l}=e;return(0,t.jsx)("div",{className:(0,R.cn)({sm:"my-4",md:"my-6",lg:"my-8"}[a],{line:"border-t border-border/50",space:"h-px",gradient:"h-px bg-gradient-to-r from-transparent via-border/50 to-transparent"}[s],r),...l})}function F(e){let{children:s,title:a,description:r,actions:l}=e;return(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)(S,{variant:"subtle",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:a}),r&&(0,t.jsx)("p",{className:"text-muted-foreground text-lg",children:r})]}),l&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:l})]})}),(0,t.jsx)(D,{variant:"gradient",spacing:"lg"}),(0,t.jsx)("div",{className:"space-y-6",children:s})]})}function B(e){let{title:s,description:a,icon:r,children:l,headerActions:i,className:c,...d}=e;return(0,t.jsxs)(n.Zp,{className:(0,R.cn)("shadow-sm border-border/50 overflow-hidden",c),...d,children:[(0,t.jsx)(n.aR,{className:"border-b border-border/20 bg-muted/10",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 text-xl",children:[r&&(0,t.jsx)("span",{className:"text-primary",children:r}),s]}),a&&(0,t.jsx)(n.BT,{className:"text-base",children:a})]}),i&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:i})]})}),(0,t.jsx)(n.Wu,{className:"p-0",children:l})]})}function T(e){let{children:s,index:a,className:r,...l}=e;return(0,t.jsx)("tr",{className:(0,R.cn)("border-b border-border/20 hover:bg-muted/30 transition-colors",a%2==0?"bg-background":"bg-muted/5",r),...l,children:s})}var L=a(8534),M=a(2278);let q=M.bL,G=M.l9,O=M.ZL,X=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(M.hJ,{className:(0,R.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r,ref:s})});X.displayName=M.hJ.displayName;let Z=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsxs)(O,{children:[(0,t.jsx)(X,{}),(0,t.jsx)(M.UC,{ref:s,className:(0,R.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r})]})});Z.displayName=M.UC.displayName;let H=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,R.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...a})};H.displayName="AlertDialogHeader";let J=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,R.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};J.displayName="AlertDialogFooter";let U=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(M.hE,{ref:s,className:(0,R.cn)("text-lg font-semibold",a),...r})});U.displayName=M.hE.displayName;let V=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(M.VY,{ref:s,className:(0,R.cn)("text-sm text-muted-foreground",a),...r})});V.displayName=M.VY.displayName;let W=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(M.rc,{ref:s,className:(0,R.cn)((0,l.r)(),a),...r})});W.displayName=M.rc.displayName;let Q=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(M.ZD,{ref:s,className:(0,R.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",a),...r})});Q.displayName=M.ZD.displayName;var Y=a(4165),$=a(2523),ee=a(5057),es=a(9409);function ea(){var e,s,a;let{token:k,user:R,isLoading:S}=(0,m.A)(),[D,M]=(0,r.useState)(!1),[O,X]=(0,r.useState)(null),[ea,et]=(0,r.useState)(!1),[er,el]=(0,r.useState)(!1),[en,ei]=(0,r.useState)(null),[ec,ed]=(0,r.useState)(""),[eo,em]=(0,r.useState)(""),[ex,eu]=(0,r.useState)(""),[eh,ep]=(0,r.useState)("all"),{data:ej,error:ef,isLoading:ev,createApiKey:ey,updateApiKey:eN,revokeApiKey:eg,isCreating:eb,isUpdating:eA,isRevoking:ew,getFormattedApiKey:eI}=(0,x.Q)({revalidateOnFocus:!0,refreshInterval:3e4,dedupingInterval:5e3,errorRetryCount:3}),eP=(0,r.useMemo)(()=>(null==ej?void 0:ej.api_keys)?ej.api_keys.filter(e=>{let s="all"===eh||"active"===eh&&e.is_active||"revoked"===eh&&!e.is_active,a=""===ex||e.name&&e.name.toLowerCase().includes(ex.toLowerCase())||e.id.toString().includes(ex);return s&&a}):[],[null==ej?void 0:ej.api_keys,eh,ex]),eK=e=>e.prefix&&e.last_chars?"".concat(e.prefix).concat("••••••••••••••••").concat(e.last_chars):"ray_••••••••••••••••••••",eC=e=>eI(e),e_=async()=>{try{let e=await ey({name:ec.trim()||"API Key ".concat(new Date().toLocaleDateString("es-ES"))});(null==e?void 0:e.api_key)&&(X(e.api_key),M(!0),et(!1),ed(""),u.o.success("API Key creada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al crear la API Key")}},ek=async()=>{if(en)try{await eN(en.id,{name:eo.trim()})&&(el(!1),ei(null),em(""),u.o.success("API Key actualizada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al actualizar la API Key")}},eE=async(e,s)=>{try{await eg(e),u.o.success("API Key ".concat(s?'"'.concat(s,'"'):""," revocada con \xe9xito"))}catch(e){(0,h.h)(e,"Error al revocar la API Key")}},ez=e=>{let s=eC(e);s&&(navigator.clipboard.writeText(s),u.o.success("API Key copiada al portapapeles"))},eR=e=>{ei(e),em(e.name||""),el(!0)},eS=()=>{eu(""),ep("all")};if(ef)return(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Error al cargar las API Keys"}),(0,t.jsxs)(o.TN,{children:[ef.message,(0,t.jsx)("br",{}),"Intenta refrescar la p\xe1gina o contacta a soporte."]})]})});if(!k||!R)return(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)(o.Fc,{variant:"warning",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Sesi\xf3n no verificada"}),(0,t.jsxs)(o.TN,{children:["No se pudo verificar tu sesi\xf3n. Por favor, intenta ",(0,t.jsx)(E(),{href:"/login",className:"underline",children:"iniciar sesi\xf3n"})," de nuevo."]})]})});if(S||ev)return(0,t.jsx)("div",{className:"container mx-auto py-8 space-y-8",children:(0,t.jsxs)("div",{className:"bg-card/30 border border-border/50 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"API Keys"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2",children:"Gestiona m\xfaltiples claves para tus proyectos y entornos"})]}),(0,t.jsx)(i.E,{className:"h-10 w-32"})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(i.E,{className:"h-6 w-6"}),(0,t.jsx)(i.E,{className:"h-6 w-48"})]}),(0,t.jsx)(n.BT,{children:(0,t.jsx)(i.E,{className:"h-4 w-64"})})]}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.E,{className:"h-10 w-full"}),(0,t.jsx)(i.E,{className:"h-10 w-full"}),(0,t.jsx)(i.E,{className:"h-10 w-full"})]})})]})]})});let eD=(null==ej||null==(e=ej.api_keys)?void 0:e.length)||0,eF=(null==ej||null==(a=ej.api_keys)||null==(s=a.filter(e=>e.is_active))?void 0:s.length)||0,eB=eD-eF;return(0,t.jsxs)(F,{title:"API Keys",description:"Gestiona m\xfaltiples claves de API para acceder a los servicios de Rayuela",actions:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex gap-6 text-sm text-muted-foreground mr-4",children:[(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(L.mm,{icon:v.A,size:"sm",context:"success"}),eF," activas"]}),eB>0&&(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[(0,t.jsx)(L.mm,{icon:f.A,size:"sm",context:"error"}),eB," revocadas"]}),(0,t.jsxs)("span",{className:"font-medium",children:["Total: ",eD]})]}),(0,t.jsxs)(Y.lG,{open:ea,onOpenChange:et,children:[(0,t.jsx)(Y.zM,{asChild:!0,children:(0,t.jsxs)(l.Button,{className:"flex items-center gap-2",children:[(0,t.jsx)(L.mm,{icon:y.A,size:"sm",context:"neutral"}),"Nueva API Key"]})}),(0,t.jsxs)(Y.Cf,{children:[(0,t.jsxs)(Y.c7,{children:[(0,t.jsx)(Y.L3,{children:"Crear Nueva API Key"}),(0,t.jsx)(Y.rr,{children:"Crea una nueva API Key para acceder a los servicios de Rayuela. Puedes asignarle un nombre descriptivo."})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(ee.J,{htmlFor:"newKeyName",children:"Nombre de la API Key (opcional)"}),(0,t.jsx)($.p,{id:"newKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:ec,onChange:e=>ed(e.target.value)})]})}),(0,t.jsxs)(Y.Es,{children:[(0,t.jsx)(l.Button,{variant:"outline",onClick:()=>et(!1),children:"Cancelar"}),(0,t.jsx)(l.Button,{onClick:e_,disabled:eb,children:eb?"Creando...":"Crear API Key"})]})]})]})]}),children:[(0,t.jsx)(B,{title:"Filtros y B\xfasqueda",icon:(0,t.jsx)(L.mm,{icon:N.A,size:"md",context:"primary"}),description:"Encuentra r\xe1pidamente las API Keys que necesitas",children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(L.mm,{icon:g.A,size:"sm",context:"muted",className:"absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,t.jsx)($.p,{placeholder:"Buscar por nombre o ID...",value:ex,onChange:e=>eu(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(ee.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,t.jsxs)(es.l6,{value:eh,onValueChange:e=>ep(e),children:[(0,t.jsx)(es.bq,{id:"status-filter",className:"w-32",children:(0,t.jsx)(es.yv,{})}),(0,t.jsxs)(es.gC,{children:[(0,t.jsx)(es.eb,{value:"all",children:"Todas"}),(0,t.jsx)(es.eb,{value:"active",children:"Activas"}),(0,t.jsx)(es.eb,{value:"revoked",children:"Revocadas"})]})]}),(ex||"all"!==eh)&&(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:eS,children:"Limpiar"})]})]}),(ex||"all"!==eh)&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",eP.length," de ",eD," API Keys"]})]})}),(0,t.jsx)(B,{title:"Tus API Keys",icon:(0,t.jsx)(L.mm,{icon:v.A,size:"md",context:"primary"}),description:"Cada clave funciona independientemente y puede ser usada para diferentes proyectos o entornos.",children:(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{className:"bg-muted/10",children:(0,t.jsxs)(c.Hj,{className:"border-b border-border/30",children:[(0,t.jsx)(c.nd,{className:"font-semibold",children:"Nombre"}),(0,t.jsx)(c.nd,{className:"font-semibold",children:"Clave"}),(0,t.jsx)(c.nd,{className:"font-semibold",children:"Estado"}),(0,t.jsx)(c.nd,{className:"font-semibold",children:"Creaci\xf3n"}),(0,t.jsx)(c.nd,{className:"font-semibold",children:"\xdaltimo uso"}),(0,t.jsxs)(c.nd,{className:"font-semibold",children:["Uso ",(0,t.jsx)("span",{className:"text-xs font-normal",children:"(pr\xf3ximamente)"})]}),(0,t.jsx)(c.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,t.jsx)(c.BF,{children:eP&&eP.length>0?eP.map((e,s)=>(0,t.jsxs)(T,{index:s,className:e.is_active?"":"opacity-60",children:[(0,t.jsx)(c.nA,{className:"font-medium py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(L.mm,{icon:e.is_active?v.A:f.A,size:"xs",context:e.is_active?"success":"error"}),e.name||"API Key ".concat(e.id)]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"text-sm bg-muted/50 px-2 py-1 rounded border font-mono",children:eK(e)}),e.is_active&&(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>ez(e),className:"h-6 w-6 p-0 hover:bg-muted/50",title:"Copiar API Key completa",children:(0,t.jsx)(L.mm,{icon:b.A,size:"xs",context:"muted"})})]})}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsx)(d.E,{variant:e.is_active?"success":"destructive",children:e.is_active?"Activa":"Revocada"})}),(0,t.jsx)(c.nA,{className:"text-muted-foreground py-4",children:e.created_at?(0,p.GP)(new Date(e.created_at),"d 'de' MMMM, yyyy",{locale:j.es}):"N/A"}),(0,t.jsx)(c.nA,{className:"text-muted-foreground py-4",children:e.last_used?(0,p.GP)(new Date(e.last_used),"d 'de' MMMM, yyyy",{locale:j.es}):"Nunca"}),(0,t.jsx)(c.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,t.jsx)(L.mm,{icon:A.A,size:"xs",context:"muted"}),(0,t.jsx)("span",{className:"text-xs",children:"--"})]})}),(0,t.jsx)(c.nA,{className:"text-right py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[e.is_active&&(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>eR(e),className:"h-8 w-8 p-0 hover:bg-muted/50",title:"Editar nombre",children:(0,t.jsx)(L.mm,{icon:w.A,size:"sm",context:"muted"})}),e.is_active&&(0,t.jsxs)(q,{children:[(0,t.jsx)(G,{asChild:!0,children:(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10",title:"Revocar API Key",children:(0,t.jsx)(L.mm,{icon:I.A,size:"sm",context:"error"})})}),(0,t.jsxs)(Z,{children:[(0,t.jsxs)(H,{children:[(0,t.jsx)(U,{children:"\xbfEst\xe1s seguro?"}),(0,t.jsxs)(V,{children:['Esta acci\xf3n no se puede deshacer. Esto revocar\xe1 permanentemente la API Key "',e.name||"API Key ".concat(e.id),'" y cualquier aplicaci\xf3n que la use dejar\xe1 de funcionar.']})]}),(0,t.jsxs)(J,{children:[(0,t.jsx)(Q,{children:"Cancelar"}),(0,t.jsx)(W,{onClick:()=>eE(e.id,e.name||void 0),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:ew,children:ew?"Revocando...":"Revocar API Key"})]})]})]})]})})]},e.id)):(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===eD?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(L.mm,{icon:f.A,size:"2xl",context:"muted"}),(0,t.jsx)("p",{children:"No tienes API Keys creadas a\xfan"}),(0,t.jsx)("p",{className:"text-sm",children:"Crea tu primera API Key para comenzar a usar la API"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(L.mm,{icon:g.A,size:"2xl",context:"muted"}),(0,t.jsx)("p",{children:"No se encontraron API Keys con los filtros aplicados"}),(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:eS,children:"Limpiar filtros"})]})})})})})]})})}),(0,t.jsxs)(o.Fc,{variant:"info",children:[(0,t.jsx)(L.mm,{icon:P.A,size:"sm",context:"info"}),(0,t.jsx)(o.XL,{children:"Sistema Multi-API Key: Gesti\xf3n Profesional"}),(0,t.jsx)(o.TN,{children:(0,t.jsxs)("div",{className:"space-y-4 text-sm mt-3",children:[(0,t.jsxs)("p",{children:["Rayuela soporta m\xfaltiples API Keys para ofrecerte m\xe1xima flexibilidad y seguridad. Cada clave funciona de forma independiente y las claves completas ",(0,t.jsx)("strong",{children:"solo se muestran una vez"})," al crearlas."]}),(0,t.jsxs)("div",{className:"grid gap-3 mt-4",children:[(0,t.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,t.jsx)(L.mm,{icon:K.A,size:"md",context:"success",className:"mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Seguridad Avanzada"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Revoca claves espec\xedficas sin afectar otras integraciones. Cada clave act\xfaa independientemente."})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,t.jsx)(L.mm,{icon:C.A,size:"md",context:"info",className:"mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Organizaci\xf3n por Entorno"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:'Asigna nombres descriptivos: "Producci\xf3n", "Desarrollo", "Testing". Identifica f\xe1cilmente el prop\xf3sito de cada clave.'})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,t.jsx)(L.mm,{icon:_.A,size:"md",context:"primary",className:"mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Gesti\xf3n por Equipos"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Crea claves separadas para diferentes equipos o aplicaciones. Control granular sobre el acceso."})]})]}),(0,t.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg",children:[(0,t.jsx)(L.mm,{icon:A.A,size:"md",context:"metric",className:"mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"M\xe9tricas Futuras"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Pr\xf3ximamente: an\xe1lisis de uso individual por API Key, incluyendo requests, errores y patrones de consumo."})]})]})]}),(0,t.jsx)("div",{className:"mt-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,t.jsxs)("p",{className:"text-sm text-info",children:[(0,t.jsx)("strong",{children:"Tip profesional:"})," Mant\xe9n claves separadas para diferentes entornos. Si comprometes una clave en desarrollo, tu producci\xf3n seguir\xe1 segura."]})})]})})]}),D&&O&&(0,t.jsx)(z.A,{apiKey:O,onClose:()=>{M(!1)}}),(0,t.jsx)(Y.lG,{open:er,onOpenChange:el,children:(0,t.jsxs)(Y.Cf,{children:[(0,t.jsxs)(Y.c7,{children:[(0,t.jsx)(Y.L3,{children:"Editar API Key"}),(0,t.jsx)(Y.rr,{children:"Actualiza el nombre descriptivo de tu API Key. La clave en s\xed no se puede modificar."})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(ee.J,{htmlFor:"editKeyName",children:"Nombre de la API Key"}),(0,t.jsx)($.p,{id:"editKeyName",placeholder:"ej. Producci\xf3n, Desarrollo, Equipo Frontend...",value:eo,onChange:e=>em(e.target.value)})]})}),(0,t.jsxs)(Y.Es,{children:[(0,t.jsx)(l.Button,{variant:"outline",onClick:()=>el(!1),children:"Cancelar"}),(0,t.jsx)(l.Button,{onClick:ek,disabled:eA,children:eA?"Actualizando...":"Actualizar"})]})]})})]})}},6718:(e,s,a)=>{Promise.resolve().then(a.bind(a,6179))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,9352,1445,5674,3753,4214,8034,3843,5813,9566,6604,2092,3999,1833,8441,1684,7358],()=>s(6718)),_N_E=e.O()}]);
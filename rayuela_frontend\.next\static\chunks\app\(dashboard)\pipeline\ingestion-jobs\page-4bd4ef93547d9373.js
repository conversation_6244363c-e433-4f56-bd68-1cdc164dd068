(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9747],{1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2538:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T});var a=t(5155),r=t(2115),d=t(6695),o=t(285),c=t(8856),i=t(5127),l=t(5365),n=t(2355),m=t(4213),x=t(9869),h=t(5339),u=t(6932),p=t(7924),j=t(2657),g=t(1788),v=t(133),f=t(3008),N=t(3439),b=t(6874),_=t.n(b),y=t(2523),w=t(5057),S=t(9409),A=t(4165),C=t(2656),E=t(646);let k=(0,t(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var I=t(1154);function L(e){let{onIngestionStart:s,trigger:t}=e,[d,c]=(0,r.useState)(!1),[i,n]=(0,r.useState)("batch"),[m,u]=(0,r.useState)(null),[p,j]=(0,r.useState)(!1),[g,v]=(0,r.useState)(null),[f,N]=(0,r.useState)(!1),b=async()=>{if(!m)return void v("Por favor selecciona un archivo");j(!0),v(null);try{let e,t=await _(m);try{e=m.name.toLowerCase().endsWith(".json")?JSON.parse(t):m.name.toLowerCase().endsWith(".csv")?await C(t,i):JSON.parse(t)}catch(e){throw Error("Error al parsear el archivo. Aseg\xfarate de que el formato sea v\xe1lido.")}let a={data_type:i,file_name:m.name,file_size:m.size,...e},r=await s(a);console.log("Ingestion started:",r),N(!0),setTimeout(()=>{c(!1),N(!1),u(null),n("batch")},2e3)}catch(e){v(e instanceof Error?e.message:"Error iniciando ingesta de datos")}finally{j(!1)}},_=e=>new Promise((s,t)=>{let a=new FileReader;a.onload=e=>{var t;return s(null==(t=e.target)?void 0:t.result)},a.onerror=()=>t(Error("Error reading file")),a.readAsText(e)}),C=async(e,s)=>{let t=e.trim().split("\n"),a=t[0].split(",").map(e=>e.trim()),r=[];for(let e=1;e<t.length;e++){let s=t[e].split(",").map(e=>e.trim()),d={};a.forEach((e,t)=>{d[e]=s[t]}),r.push(d)}switch(s){case"users":return{users:r};case"products":return{products:r};case"interactions":return{interactions:r};case"batch":return{users:r.filter(e=>e.external_id&&!e.product_id),products:r.filter(e=>e.external_id&&e.name&&!e.end_user_external_id),interactions:r.filter(e=>e.end_user_external_id&&e.product_external_id)};default:return{data:r}}};return(0,a.jsxs)(A.lG,{open:d,onOpenChange:c,children:[(0,a.jsx)(A.zM,{asChild:!0,children:t||(0,a.jsxs)(o.Button,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),(0,a.jsxs)(A.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(A.c7,{children:[(0,a.jsx)(A.L3,{children:"Nueva Ingesta de Datos"}),(0,a.jsx)(A.rr,{children:"Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones"})]}),f?(0,a.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,a.jsx)(E.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Ingesta iniciada!"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu archivo est\xe1 siendo procesado"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"dataType",children:"Tipo de datos"}),(0,a.jsxs)(S.l6,{value:i,onValueChange:e=>n(e),children:[(0,a.jsx)(S.bq,{children:(0,a.jsx)(S.yv,{placeholder:"Selecciona el tipo de datos"})}),(0,a.jsxs)(S.gC,{children:[(0,a.jsx)(S.eb,{value:"batch",children:"Lote completo (usuarios, productos, interacciones)"}),(0,a.jsx)(S.eb,{value:"users",children:"Solo usuarios"}),(0,a.jsx)(S.eb,{value:"products",children:"Solo productos"}),(0,a.jsx)(S.eb,{value:"interactions",children:"Solo interacciones"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"file",children:"Archivo"}),(0,a.jsx)(y.p,{id:"file",type:"file",accept:".csv,.json,.txt",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){let e=t.name.toLowerCase().substr(t.name.lastIndexOf("."));if(!["text/csv","application/json","text/plain"].includes(t.type)&&![".csv",".json",".txt"].includes(e))return void v("Por favor selecciona un archivo CSV o JSON v\xe1lido");if(t.size>0xa00000)return void v("El archivo es demasiado grande. M\xe1ximo 10MB permitido");u(t),v(null)}},disabled:p}),m&&(0,a.jsxs)("div",{className:"mt-2 flex items-center text-sm text-muted-foreground",children:[(0,a.jsx)(k,{className:"h-4 w-4 mr-2"}),(0,a.jsxs)("span",{children:[m.name," (",(m.size/1024).toFixed(1)," KB)"]})]})]}),g&&(0,a.jsxs)(l.Fc,{variant:"destructive",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(l.TN,{children:g})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsx)("p",{children:"Formatos soportados: CSV, JSON"}),(0,a.jsx)("p",{children:"Tama\xf1o m\xe1ximo: 10MB"})]})]}),(0,a.jsxs)(A.Es,{children:[(0,a.jsx)(o.Button,{variant:"outline",onClick:()=>{p||(c(!1),u(null),v(null),N(!1),n("batch"))},disabled:p,children:"Cancelar"}),!f&&(0,a.jsx)(o.Button,{onClick:b,disabled:!m||p,children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Subiendo..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Iniciar Ingesta"]})})]})]})]})}var J=t(8338);function T(){let{jobs:e,isLoading:s,error:t,startBatchIngestion:b}=function(){let[e,s]=(0,r.useState)([]),[t,a]=(0,r.useState)(!0),[d,o]=(0,r.useState)(null),c=async()=>{try{a(!0),o(null);try{let e=[].map(e=>{let s={id:e.job_id,status:e.status.toUpperCase(),created_at:e.created_at,started_at:e.started_at||void 0,completed_at:e.completed_at||void 0,error_message:e.error_message||void 0,task_id:e.task_id||void 0,records_processed:e.processed_count?{users:"number"==typeof e.processed_count.users?e.processed_count.users:void 0,products:"number"==typeof e.processed_count.products?e.processed_count.products:void 0,interactions:"number"==typeof e.processed_count.interactions?e.processed_count.interactions:void 0,total:"number"==typeof e.processed_count.total?e.processed_count.total:void 0}:void 0};if(s.started_at&&s.completed_at){let e=new Date(s.started_at).getTime(),t=new Date(s.completed_at).getTime();s.duration=Math.round((t-e)/1e3)}return s});s(e);return}catch(e){console.warn("Listing endpoint not available, falling back to localStorage approach:",e)}let e=localStorage.getItem("ingestionJobIds"),t=e?JSON.parse(e):[],r=[];for(let e of t)try{let s=(await (0,C._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data,t={id:s.job_id,status:s.status.toUpperCase(),created_at:s.created_at,started_at:s.started_at||void 0,completed_at:s.completed_at||void 0,error_message:s.error_message||void 0,task_id:s.task_id||void 0,records_processed:s.processed_count?{users:"number"==typeof s.processed_count.users?s.processed_count.users:void 0,products:"number"==typeof s.processed_count.products?s.processed_count.products:void 0,interactions:"number"==typeof s.processed_count.interactions?s.processed_count.interactions:void 0,total:"number"==typeof s.processed_count.total?s.processed_count.total:void 0}:void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),s=new Date(t.completed_at).getTime();t.duration=Math.round((s-e)/1e3)}r.push(t)}catch(s){console.warn("Error fetching job ".concat(e,":"),s)}r.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()),s(r)}catch(e){o(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{a(!1)}},i=async e=>{try{return(await (0,C._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},l=async e=>{try{let s=await (0,C._C)().batchDataIngestionApiV1IngestionBatchPost(e),t=s.data.job_id,a=localStorage.getItem("ingestionJobIds"),r=a?JSON.parse(a):[];r.unshift(t);let d=r.slice(0,50);return localStorage.setItem("ingestionJobIds",JSON.stringify(d)),await c(),s.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return(0,r.useEffect)(()=>{c()},[]),{jobs:e,isLoading:t,error:d,fetchJobs:c,getJobStatus:i,startBatchIngestion:l}}(),[E,k]=(0,r.useState)(""),[I,T]=(0,r.useState)("all"),[F,B]=(0,r.useState)(null),D=e.filter(e=>{let s="all"===I||e.status.toLowerCase()===I,t=""===E||e.id.toString().includes(E)||e.file_path&&e.file_path.toLowerCase().includes(E.toLowerCase());return s&&t}),z=e=>(0,J.z3)(e),M=()=>{k(""),T("all")},O=e=>"FAILED"===e.status,P=e=>{console.log("Retrying job:",e)},V=e=>{console.log("Downloading file:",e)};return s?(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,a.jsx)(c.E,{className:"h-8 w-64 mb-2"}),(0,a.jsx)(c.E,{className:"h-4 w-96"})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(c.E,{className:"h-6 w-48"}),(0,a.jsx)(c.E,{className:"h-4 w-32"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)(c.E,{className:"h-64 w-full"})})]})]}):(0,a.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,a.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(_(),{href:"/pipeline",className:"text-muted-foreground hover:text-foreground",children:(0,a.jsx)(n.A,{className:"h-5 w-5"})}),(0,a.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-3",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-green-500"}),"Historial de Ingesta de Datos"]})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Seguimiento completo de todos tus procesos de carga de datos"}),(0,a.jsxs)("div",{className:"flex gap-4 mt-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{children:["Total: ",e.length]}),(0,a.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,a.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,a.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})]}),(0,a.jsx)(L,{onIngestionStart:b,trigger:(0,a.jsxs)(o.Button,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})})]})}),t&&(0,a.jsxs)(l.Fc,{variant:"destructive",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(l.XL,{children:"Error"}),(0,a.jsx)(l.TN,{children:t})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{className:"border-b border-border/20 bg-muted/20",children:(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Filtros"]})}),(0,a.jsx)(d.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(y.p,{placeholder:"Buscar por ID o archivo...",value:E,onChange:e=>k(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsx)(w.J,{htmlFor:"statusFilter",className:"text-sm whitespace-nowrap",children:"Estado:"}),(0,a.jsxs)(S.l6,{value:I,onValueChange:e=>T(e),children:[(0,a.jsx)(S.bq,{className:"w-32",children:(0,a.jsx)(S.yv,{})}),(0,a.jsxs)(S.gC,{children:[(0,a.jsx)(S.eb,{value:"all",children:"Todos"}),(0,a.jsx)(S.eb,{value:"pending",children:"Pendiente"}),(0,a.jsx)(S.eb,{value:"processing",children:"Procesando"}),(0,a.jsx)(S.eb,{value:"completed",children:"Completado"}),(0,a.jsx)(S.eb,{value:"failed",children:"Fallido"})]})]})]}),(0,a.jsx)(o.Button,{variant:"outline",size:"sm",onClick:M,children:"Limpiar"})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"border-b border-border/20 bg-muted/20",children:[(0,a.jsx)(d.ZB,{children:"Trabajos de Ingesta"}),(0,a.jsx)(d.BT,{children:"Lista completa de procesos de carga de datos con detalles y estad\xedsticas"})]}),(0,a.jsx)(d.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-hidden",children:(0,a.jsxs)(i.XI,{children:[(0,a.jsx)(i.A0,{className:"bg-muted/10",children:(0,a.jsxs)(i.Hj,{className:"border-b border-border/30",children:[(0,a.jsx)(i.nd,{className:"font-semibold",children:"Job ID"}),(0,a.jsx)(i.nd,{className:"font-semibold",children:"Estado"}),(0,a.jsx)(i.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,a.jsx)(i.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,a.jsx)(i.nd,{className:"font-semibold",children:"Registros Procesados"}),(0,a.jsx)(i.nd,{className:"font-semibold",children:"Archivo"}),(0,a.jsx)(i.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,a.jsx)(i.BF,{children:D.length>0?D.map((e,s)=>{var t;return(0,a.jsxs)(i.Hj,{className:s%2==0?"bg-muted/20":"",children:[(0,a.jsxs)(i.nA,{className:"font-medium py-4",children:["#",e.id]}),(0,a.jsx)(i.nA,{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,J.cR)(e.status),(0,J.KC)(e.status)]})}),(0,a.jsx)(i.nA,{className:"py-4",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{children:(0,f.GP)(new Date(e.created_at),"dd/MM/yyyy",{locale:N.es})}),(0,a.jsx)("div",{className:"text-muted-foreground",children:(0,f.GP)(new Date(e.created_at),"HH:mm",{locale:N.es})})]})}),(0,a.jsx)(i.nA,{className:"py-4",children:e.duration?(0,a.jsx)("span",{className:"text-sm font-medium",children:(0,J.a3)(e.duration)}):"PROCESSING"===e.status?(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"En curso"}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(i.nA,{className:"py-4",children:e.records_processed?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium",children:["Total: ",(null==(t=e.records_processed.total)?void 0:t.toLocaleString())||"—"]}),(0,a.jsxs)("div",{className:"text-muted-foreground text-xs",children:[e.records_processed.users&&"".concat(e.records_processed.users.toLocaleString()," usuarios"),e.records_processed.products&&", ".concat(e.records_processed.products.toLocaleString()," productos"),e.records_processed.interactions&&", ".concat(e.records_processed.interactions.toLocaleString()," interacciones")]})]}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(i.nA,{className:"py-4",children:e.file_path?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-medium truncate max-w-32",title:e.file_path,children:e.file_path.split("/").pop()}),e.file_size&&(0,a.jsx)("div",{className:"text-muted-foreground text-xs",children:z(e.file_size)})]}):(0,a.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,a.jsx)(i.nA,{className:"text-right py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,a.jsxs)(A.lG,{children:[(0,a.jsx)(A.zM,{asChild:!0,children:(0,a.jsx)(o.Button,{variant:"ghost",size:"sm",onClick:()=>B(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(A.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(A.c7,{children:[(0,a.jsxs)(A.L3,{children:["Detalles del Job #",e.id]}),(0,a.jsx)(A.rr,{children:"Informaci\xf3n completa del trabajo de ingesta de datos"})]}),F&&(0,a.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium",children:"Estado"}),(0,a.jsx)("div",{className:"mt-1",children:(0,J.KC)(F.status)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium",children:"Duraci\xf3n"}),(0,a.jsx)("p",{className:"text-sm",children:F.duration?(0,J.a3)(F.duration):"En curso"})]})]}),F.file_path&&(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium",children:"Archivo"}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-xs bg-muted p-2 rounded flex-1",children:F.file_path}),(0,a.jsx)(o.Button,{size:"sm",variant:"outline",onClick:()=>V(F.file_path),disabled:!0,children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]}),F.file_size&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Tama\xf1o: ",z(F.file_size)]})]}),F.records_processed&&(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium",children:"Registros Procesados"}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[F.records_processed.users&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Usuarios"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:F.records_processed.users.toLocaleString()})]}),F.records_processed.products&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Productos"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:F.records_processed.products.toLocaleString()})]}),F.records_processed.interactions&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Interacciones"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:F.records_processed.interactions.toLocaleString()})]}),F.records_processed.total&&(0,a.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs font-medium",children:"Total"}),(0,a.jsx)("div",{className:"text-sm font-bold",children:F.records_processed.total.toLocaleString()})]})]})]}),F.error_message&&(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,a.jsxs)(l.Fc,{variant:"destructive",className:"mt-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(l.TN,{className:"text-sm",children:F.error_message})]})]}),F.task_id&&(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{className:"text-sm font-medium",children:"Task ID"}),(0,a.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:F.task_id})]})]})]})]}),O(e)&&(0,a.jsx)(o.Button,{variant:"ghost",size:"sm",onClick:()=>P(e.id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e.id)}):(0,a.jsx)(i.Hj,{children:(0,a.jsx)(i.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-8 w-8"}),(0,a.jsx)("p",{children:"No hay trabajos de ingesta a\xfan"}),(0,a.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando subas datos"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-8 w-8"}),(0,a.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,a.jsx)(o.Button,{variant:"outline",size:"sm",onClick:M,children:"Limpiar filtros"})]})})})})})]})})})]}),(0,a.jsxs)(l.Fc,{children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)(l.XL,{children:"Informaci\xf3n sobre ingesta de datos"}),(0,a.jsx)(l.TN,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,a.jsx)("p",{children:"Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar usuarios, productos e interacciones en el sistema."}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Formatos soportados:"})," CSV, JSON"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Tipos de datos:"})," Usuarios, Productos, Interacciones"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Validaci\xf3n:"})," Se valida formato y campos obligatorios"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Procesamiento:"})," Los datos se procesan de forma as\xedncrona"]})]})]})})]})]})}},2620:(e,s,t)=>{Promise.resolve().then(t.bind(t,2538))},4213:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},9869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,9352,1445,5674,4214,8034,3843,5813,2092,3132,8441,1684,7358],()=>s(2620)),_N_E=e.O()}]);
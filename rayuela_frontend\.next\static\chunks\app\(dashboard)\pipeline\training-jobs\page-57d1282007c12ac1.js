(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5507],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},996:(e,s,a)=>{Promise.resolve().then(a.bind(a,6891))},6891:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var t=a(5155),r=a(2115),n=a(6695),l=a(285),i=a(8856),d=a(5127),o=a(5365),c=a(2355),m=a(9376),h=a(5690),x=a(5339),j=a(6932),u=a(7924),p=a(2657),g=a(133),b=a(3008),v=a(3439),N=a(6874),f=a.n(N),_=a(2523),y=a(5057),w=a(9409),E=a(4165),C=a(2656),A=a(646),S=a(381),k=a(1154);function F(e){let{onTrainingStart:s,trigger:a}=e,[n,i]=(0,r.useState)(!1),[d,c]=(0,r.useState)("hybrid"),[j,u]=(0,r.useState)(!1),[p,g]=(0,r.useState)({learning_rate:.001,epochs:50,batch_size:32,embedding_dim:64,regularization:.001}),[b,v]=(0,r.useState)(!1),[N,f]=(0,r.useState)(null),[C,F]=(0,r.useState)(!1),J=(e,s)=>{g(a=>({...a,[e]:s}))},z=async()=>{v(!0),f(null);try{let e={model_type:d,force:!1};j&&(e.hyperparameters={learning_rate:p.learning_rate,epochs:p.epochs,batch_size:p.batch_size,embedding_dim:p.embedding_dim,regularization:p.regularization});let a=await s(e);console.log("Training started:",a),F(!0),setTimeout(()=>{i(!1),F(!1),u(!1),c("hybrid")},3e3)}catch(e){f(e instanceof Error?e.message:"Error iniciando entrenamiento")}finally{v(!1)}};return(0,t.jsxs)(E.lG,{open:n,onOpenChange:i,children:[(0,t.jsx)(E.zM,{asChild:!0,children:a||(0,t.jsxs)(l.Button,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})}),(0,t.jsxs)(E.Cf,{className:"sm:max-w-lg",children:[(0,t.jsxs)(E.c7,{children:[(0,t.jsx)(E.L3,{children:"Nuevo Entrenamiento de Modelo"}),(0,t.jsx)(E.rr,{children:"Inicia el entrenamiento de un modelo de recomendaci\xf3n personalizado con tus datos"})]}),C?(0,t.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,t.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Entrenamiento iniciado!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu modelo est\xe1 siendo entrenado"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"modelType",children:"Tipo de modelo"}),(0,t.jsxs)(w.l6,{value:d,onValueChange:e=>c(e),children:[(0,t.jsx)(w.bq,{children:(0,t.jsx)(w.yv,{placeholder:"Selecciona el tipo de modelo"})}),(0,t.jsxs)(w.gC,{children:[(0,t.jsx)(w.eb,{value:"hybrid",children:"H\xedbrido (recomendado)"}),(0,t.jsx)(w.eb,{value:"collaborative",children:"Filtrado colaborativo"}),(0,t.jsx)(w.eb,{value:"content",children:"Basado en contenido"})]})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["hybrid"===d&&"Combina m\xfaltiples t\xe9cnicas para mejores resultados","collaborative"===d&&"Basado en comportamiento de usuarios similares","content"===d&&"Basado en caracter\xedsticas de productos"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{id:"advanced",type:"checkbox",className:"rounded border-gray-300",checked:j,onChange:e=>u(e.target.checked)}),(0,t.jsxs)(y.J,{htmlFor:"advanced",className:"flex items-center",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"Configuraci\xf3n avanzada"]})]}),j&&(0,t.jsxs)("div",{className:"space-y-3 p-4 border rounded-lg bg-muted/50",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"learning_rate",children:"Learning Rate"}),(0,t.jsx)(_.p,{id:"learning_rate",type:"number",step:"0.0001",value:p.learning_rate,onChange:e=>J("learning_rate",parseFloat(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"epochs",children:"\xc9pocas"}),(0,t.jsx)(_.p,{id:"epochs",type:"number",value:p.epochs,onChange:e=>J("epochs",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"batch_size",children:"Batch Size"}),(0,t.jsx)(_.p,{id:"batch_size",type:"number",value:p.batch_size,onChange:e=>J("batch_size",parseInt(e.target.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"embedding_dim",children:"Embedding Dim"}),(0,t.jsx)(_.p,{id:"embedding_dim",type:"number",value:p.embedding_dim,onChange:e=>J("embedding_dim",parseInt(e.target.value))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{htmlFor:"regularization",children:"Regularizaci\xf3n"}),(0,t.jsx)(_.p,{id:"regularization",type:"number",step:"0.0001",value:p.regularization,onChange:e=>J("regularization",parseFloat(e.target.value))})]})]}),N&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:N})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"El entrenamiento puede tomar varios minutos dependiendo del volumen de datos"}),(0,t.jsx)("p",{children:"Se requiere un m\xednimo de 100 interacciones para entrenar un modelo"})]})]}),(0,t.jsxs)(E.Es,{children:[(0,t.jsx)(l.Button,{variant:"outline",onClick:()=>{b||(i(!1),f(null),F(!1),u(!1),c("hybrid"))},disabled:b,children:"Cancelar"}),!C&&(0,t.jsx)(l.Button,{onClick:z,disabled:b,children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Entrenando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Iniciar Entrenamiento"]})})]})]})]})}var J=a(8338);function z(){let{jobs:e,isLoading:s,error:a,startTraining:N}=function(){let[e,s]=(0,r.useState)([]),[a,t]=(0,r.useState)(!0),[n,l]=(0,r.useState)(null),i=async()=>{try{t(!0),l(null);try{let e=[].map(e=>{var s,a;let t={id:e.job_id,model_name:(null==(s=e.model)?void 0:s.artifact_name)||"Recommendation Model",model_version:(null==(a=e.model)?void 0:a.artifact_version)||"v1.0",status:e.status.toUpperCase(),created_at:e.created_at,started_at:e.started_at||void 0,completed_at:e.completed_at||void 0,error_message:e.error_message||void 0,task_id:e.task_id||void 0,parameters:e.parameters?Object.fromEntries(Object.entries(e.parameters).filter(e=>{let[s,a]=e;return"number"==typeof a||"string"==typeof a})):void 0,metrics:e.metrics?Object.fromEntries(Object.entries(e.metrics).filter(e=>{let[s,a]=e;return"number"==typeof a})):void 0};if(t.started_at&&t.completed_at){let e=new Date(t.started_at).getTime(),s=new Date(t.completed_at).getTime();t.duration=Math.round((s-e)/1e3)}return t});s(e);return}catch(e){console.warn("Listing endpoint not available, falling back to localStorage approach:",e)}let r=localStorage.getItem("trainingJobIds"),n=r?JSON.parse(r):[],i=[];for(let s of n)try{var e,a;let t=(await (0,C._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(s)).data,r={id:t.job_id,model_name:(null==(e=t.model)?void 0:e.artifact_name)||"Recommendation Model",model_version:(null==(a=t.model)?void 0:a.artifact_version)||"v1.0",status:t.status.toUpperCase(),created_at:t.created_at,started_at:t.started_at||void 0,completed_at:t.completed_at||void 0,error_message:t.error_message||void 0,task_id:t.task_id||void 0,parameters:t.parameters?Object.fromEntries(Object.entries(t.parameters).filter(e=>{let[s,a]=e;return"number"==typeof a||"string"==typeof a})):void 0,metrics:t.metrics?Object.fromEntries(Object.entries(t.metrics).filter(e=>{let[s,a]=e;return"number"==typeof a})):void 0};if(r.started_at&&r.completed_at){let e=new Date(r.started_at).getTime(),s=new Date(r.completed_at).getTime();r.duration=Math.round((s-e)/1e3)}i.push(r)}catch(e){console.warn("Error fetching training job ".concat(s,":"),e)}i.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()),s(i)}catch(e){l(e instanceof Error?e.message:"Error loading training jobs"),console.error("Error loading training jobs:",e)}finally{t(!1)}},d=async e=>{try{return(await (0,C._C)().getTrainingJobStatusApiV1PipelineJobsJobIdStatusGet(e)).data}catch(e){throw console.error("Error fetching training job status:",e),e}},o=async e=>{try{let e=await (0,C._C)().trainModelsApiV1PipelineTrainPost(),s=e.data.job_id;if(s){let e=localStorage.getItem("trainingJobIds"),a=e?JSON.parse(e):[];a.unshift(s);let t=a.slice(0,20);localStorage.setItem("trainingJobIds",JSON.stringify(t))}return await i(),e.data}catch(e){throw console.error("Error starting training:",e),e}};return(0,r.useEffect)(()=>{i()},[]),{jobs:e,isLoading:a,error:n,fetchJobs:i,getJobStatus:d,startTraining:o}}(),[A,S]=(0,r.useState)(""),[k,z]=(0,r.useState)("all"),[M,T]=(0,r.useState)(null),I=e.filter(e=>{let s="all"===k||e.status.toLowerCase()===k,a=""===A||e.model_name.toLowerCase().includes(A.toLowerCase())||e.model_version.toLowerCase().includes(A.toLowerCase())||e.id.toString().includes(A);return s&&a}),O=()=>{S(""),z("all")},L=e=>"FAILED"===e.status,B=e=>{console.log("Retrying job:",e)};return s?(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,t.jsx)(i.E,{className:"h-8 w-64 mb-2"}),(0,t.jsx)(i.E,{className:"h-4 w-96"})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(i.E,{className:"h-6 w-48"}),(0,t.jsx)(i.E,{className:"h-4 w-32"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(i.E,{className:"h-64 w-full"})})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,t.jsx)(f(),{href:"/pipeline",className:"text-muted-foreground hover:text-foreground",children:(0,t.jsx)(c.A,{className:"h-5 w-5"})}),(0,t.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-3",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-purple-500"}),"Historial de Entrenamientos"]})]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Seguimiento completo de todos tus trabajos de entrenamiento de modelos"}),(0,t.jsxs)("div",{className:"flex gap-4 mt-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("span",{children:["Total: ",e.length]}),(0,t.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,t.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,t.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})]}),(0,t.jsx)(F,{onTrainingStart:N,trigger:(0,t.jsxs)(l.Button,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Nuevo Entrenamiento"]})})]})}),a&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Error"}),(0,t.jsx)(o.TN,{children:a})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{className:"border-b border-border/20 bg-muted/20",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Filtros"]})}),(0,t.jsxs)(n.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(_.p,{placeholder:"Buscar por modelo, versi\xf3n o ID...",value:A,onChange:e=>S(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(y.J,{htmlFor:"status-filter",className:"text-sm font-medium whitespace-nowrap",children:"Estado:"}),(0,t.jsxs)(w.l6,{value:k,onValueChange:e=>z(e),children:[(0,t.jsx)(w.bq,{id:"status-filter",className:"w-40",children:(0,t.jsx)(w.yv,{})}),(0,t.jsxs)(w.gC,{children:[(0,t.jsx)(w.eb,{value:"all",children:"Todos"}),(0,t.jsx)(w.eb,{value:"pending",children:"Pendientes"}),(0,t.jsx)(w.eb,{value:"processing",children:"Procesando"}),(0,t.jsx)(w.eb,{value:"completed",children:"Completados"}),(0,t.jsx)(w.eb,{value:"failed",children:"Fallidos"})]})]}),(A||"all"!==k)&&(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:O,children:"Limpiar"})]})]}),(A||"all"!==k)&&(0,t.jsxs)("div",{className:"mt-2 text-sm text-muted-foreground",children:["Mostrando ",I.length," de ",e.length," trabajos"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"border-b border-border/20 bg-muted/20",children:[(0,t.jsx)(n.ZB,{children:"Trabajos de Entrenamiento"}),(0,t.jsx)(n.BT,{children:"Lista completa de entrenamientos con detalles y m\xe9tricas"})]}),(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-hidden",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{className:"bg-muted/10",children:(0,t.jsxs)(d.Hj,{className:"border-b border-border/30",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Job ID"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Modelo / Versi\xf3n"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Estado"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"M\xe9tricas"}),(0,t.jsx)(d.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,t.jsx)(d.BF,{children:I.length>0?I.map((e,s)=>(0,t.jsxs)(d.Hj,{className:"\n                        border-b border-border/20 \n                        hover:bg-muted/30 \n                        transition-colors\n                        ".concat(s%2==0?"bg-background":"bg-muted/5","\n                      "),children:[(0,t.jsx)(d.nA,{className:"font-medium py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,J.cR)(e.status),"#",e.id]})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.model_name}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.model_version})]})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,J.KC)(e.status)}),(0,t.jsx)(d.nA,{className:"py-4 text-muted-foreground",children:(0,b.GP)(new Date(e.created_at),"d 'de' MMM, yyyy HH:mm",{locale:v.es})}),(0,t.jsx)(d.nA,{className:"py-4 text-muted-foreground",children:e.duration?(0,J.a3)(e.duration):"PROCESSING"===e.status?"⏳ En curso":"—"}),(0,t.jsx)(d.nA,{className:"py-4",children:e.metrics?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Acc: ",(100*e.metrics.accuracy).toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"text-muted-foreground",children:["F1: ",(100*e.metrics.f1_score).toFixed(1),"%"]})]}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,t.jsx)(d.nA,{className:"text-right py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,t.jsxs)(E.lG,{children:[(0,t.jsx)(E.zM,{asChild:!0,children:(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>T(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(E.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(E.c7,{children:[(0,t.jsxs)(E.L3,{children:["Detalles del Job #",e.id]}),(0,t.jsx)(E.rr,{children:"Informaci\xf3n completa del trabajo de entrenamiento"})]}),M&&(0,t.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Modelo"}),(0,t.jsxs)("p",{className:"text-sm",children:[M.model_name," ",M.model_version]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Estado"}),(0,t.jsx)("div",{className:"mt-1",children:(0,J.KC)(M.status)})]})]}),M.parameters&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Par\xe1metros"}),(0,t.jsx)("pre",{className:"text-xs bg-muted p-2 rounded mt-1 overflow-x-auto",children:JSON.stringify(M.parameters,null,2)})]}),M.metrics&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"M\xe9tricas"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-1",children:Object.entries(M.metrics).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,t.jsx)("div",{className:"text-xs font-medium",children:s}),(0,t.jsxs)("div",{className:"text-sm",children:[(100*a).toFixed(2),"%"]})]},s)})})]}),M.error_message&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,t.jsxs)(o.Fc,{variant:"destructive",className:"mt-1",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{className:"text-sm",children:M.error_message})]})]}),M.task_id&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium",children:"Task ID"}),(0,t.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:M.task_id})]})]})]})]}),L(e)&&(0,t.jsx)(l.Button,{variant:"ghost",size:"sm",onClick:()=>B(e.id),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})})]},e.id)):(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,t.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No hay trabajos de entrenamiento a\xfan"}),(0,t.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando inicies entrenamientos"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"h-8 w-8"}),(0,t.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,t.jsx)(l.Button,{variant:"outline",size:"sm",onClick:O,children:"Limpiar filtros"})]})})})})})]})})})]}),(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Informaci\xf3n sobre entrenamientos"}),(0,t.jsx)(o.TN,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,t.jsx)("p",{children:"Los trabajos de entrenamiento pueden tomar desde minutos hasta horas dependiendo del tama\xf1o de los datos y la complejidad del modelo."}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Pendiente:"})," El trabajo est\xe1 en cola esperando recursos"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Procesando:"})," El entrenamiento est\xe1 en curso"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Completado:"})," El modelo se entren\xf3 exitosamente"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Fallido:"})," Ocurri\xf3 un error durante el entrenamiento"]})]})]})})]})]})}},9376:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,9352,1445,5674,4214,8034,3843,5813,2092,3132,8441,1684,7358],()=>s(996)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7080],{467:(e,a,i)=>{"use strict";i.d(a,{K:()=>o,S:()=>n});var r=i(2656),t=i(5731);let s=(0,r._C)();async function n(e,a){try{let i={};return e&&(i.model_id=e),a&&(i.metric_type=a),await s.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(i)}catch(r){let e=r instanceof Error?r.message:"Error al obtener m\xe9tricas de rendimiento de recomendaciones",a=r.status||500,i=r.body;throw new t.hD(e,a,i)}}async function o(){try{return await s.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet()}catch(r){let e=r instanceof Error?r.message:"Error al obtener m\xe9tricas de confianza",a=r.status||500,i=r.body;throw new t.hD(e,a,i)}}},1169:(e,a,i)=>{"use strict";i.d(a,{A:()=>c});var r=i(5155),t=i(2115),s=i(5695),n=i(3999),o=i(8856);let c=function(e){let{children:a}=e,{user:i,isLoading:c}=(0,n.A)(),l=(0,s.useRouter)();return((0,t.useEffect)(()=>{c||i&&i.is_admin||l.push("/dashboard")},[i,c,l]),c)?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(o.E,{className:"h-8 w-[200px]"}),(0,r.jsx)(o.E,{className:"h-[400px] w-full"})]}):i&&i.is_admin?(0,r.jsx)(r.Fragment,{children:a}):null}},3270:(e,a,i)=>{Promise.resolve().then(i.bind(i,8929))},6102:(e,a,i)=>{"use strict";i.d(a,{Bc:()=>n,ZI:()=>l,k$:()=>c,m_:()=>o});var r=i(5155);i(2115);var t=i(3815),s=i(9434);function n(e){let{delayDuration:a=0,...i}=e;return(0,r.jsx)(t.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...i})}function o(e){let{...a}=e;return(0,r.jsx)(n,{children:(0,r.jsx)(t.bL,{"data-slot":"tooltip",...a})})}function c(e){let{...a}=e;return(0,r.jsx)(t.l9,{"data-slot":"tooltip-trigger",...a})}function l(e){let{className:a,sideOffset:i=0,children:n,...o}=e;return(0,r.jsx)(t.ZL,{children:(0,r.jsxs)(t.UC,{"data-slot":"tooltip-content",sideOffset:i,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...o,children:[n,(0,r.jsx)(t.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},7313:(e,a,i)=>{"use strict";i.d(a,{Xi:()=>l,av:()=>d,j7:()=>c,tU:()=>o});var r=i(5155),t=i(2115),s=i(6176),n=i(9434);let o=s.bL,c=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,r.jsx)(s.B8,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",i),...t})});c.displayName=s.B8.displayName;let l=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,r.jsx)(s.l9,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",i),...t})});l.displayName=s.l9.displayName;let d=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,r.jsx)(s.UC,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...t})});d.displayName=s.UC.displayName},8929:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>K});var r=i(5155),t=i(2115),s=i(6072),n=i(3999),o=i(6695),c=i(285),l=i(9409),d=i(7313),m=i(6671),_=i(7588),C=i(6102),E=i(4788);function T(e){let{content:a,side:i="top",align:t="center",className:s="",iconSize:n=16,iconClassName:o=""}=e;return(0,r.jsx)(C.Bc,{children:(0,r.jsxs)(C.m_,{delayDuration:300,children:[(0,r.jsx)(C.k$,{asChild:!0,children:(0,r.jsx)("span",{className:"inline-flex cursor-help ".concat(o),children:(0,r.jsx)(E.A,{size:n,className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"})})}),(0,r.jsx)(C.ZI,{side:i,align:t,className:"max-w-xs p-3 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md ".concat(s),children:a})]})})}var R=i(3904),u=i(2713),I=i(232),p=i(6126),N=i(9287),h=i(6474),x=i(9434);let O=N.bL,f=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,r.jsx)(N.q7,{ref:a,className:(0,x.cn)("border-b border-border/50 last:border-b-0",i),...t})});f.displayName="AccordionItem";let g=t.forwardRef((e,a)=>{let{className:i,children:t,...s}=e;return(0,r.jsx)(N.Y9,{className:"flex",children:(0,r.jsxs)(N.l9,{ref:a,className:(0,x.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:bg-muted/30 hover:text-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg -mx-2 px-2 [&[data-state=open]>svg]:rotate-180",i),...s,children:[t,(0,r.jsx)(h.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200 text-muted-foreground"})]})})});g.displayName="AccordionTrigger";let A=t.forwardRef((e,a)=>{let{className:i,children:t,...s}=e;return(0,r.jsx)(N.UC,{ref:a,className:"overflow-hidden text-sm text-muted-foreground transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,r.jsx)("div",{className:(0,x.cn)("pb-4 pt-0 px-2",i),children:t})})});A.displayName="AccordionContent";var M=i(235),v=i(825),y=i(1539),j=i(5339),b=i(4213),D=i(7580),L=i(3109),S=i(463),P=i(646),Y=i(3052),w=i(8534);let F=[{id:"improve-precision",metricPath:["summary","precision"],threshold:.5,comparison:"lt",title:"METRIC_REC_PRECISION_TITLE",description:"METRIC_REC_PRECISION_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_PRECISION_ACTION_1","METRIC_REC_PRECISION_ACTION_2","METRIC_REC_PRECISION_ACTION_3"],metrics:[{name:"METRIC_PRECISION",valuePath:["summary","precision"],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-diversity",metricPath:["summary","diversity"],threshold:.4,comparison:"lt",title:"METRIC_REC_DIVERSITY_TITLE",description:"METRIC_REC_DIVERSITY_DESC",priority:"medium",category:"diversity",iconKey:"ShuffleIcon",actions:["METRIC_REC_DIVERSITY_ACTION_1","METRIC_REC_DIVERSITY_ACTION_2","METRIC_REC_DIVERSITY_ACTION_3","METRIC_REC_DIVERSITY_ACTION_4"],metrics:[{name:"METRIC_DIVERSITY",valuePath:["summary","diversity"],valueMultiplier:100,target:40,unit:"%"},{name:"METRIC_CATALOG_COVERAGE",valuePath:["summary","catalog_coverage"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-novelty",metricPath:["summary","novelty"],threshold:.3,comparison:"lt",title:"METRIC_REC_NOVELTY_TITLE",description:"METRIC_REC_NOVELTY_DESC",priority:"medium",category:"diversity",iconKey:"ZapIcon",actions:["METRIC_REC_NOVELTY_ACTION_1","METRIC_REC_NOVELTY_ACTION_2","METRIC_REC_NOVELTY_ACTION_3","METRIC_REC_NOVELTY_ACTION_4"],metrics:[{name:"METRIC_NOVELTY",valuePath:["summary","novelty"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-ranking",metricPath:["summary","ndcg"],threshold:.45,comparison:"lt",title:"METRIC_REC_RANKING_TITLE",description:"METRIC_REC_RANKING_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_RANKING_ACTION_1","METRIC_REC_RANKING_ACTION_2","METRIC_REC_RANKING_ACTION_3","METRIC_REC_RANKING_ACTION_4"],metrics:[{name:"METRIC_NDCG",valuePath:["summary","ndcg"],valueMultiplier:100,target:45,unit:"%"}]},{id:"improve-serendipity",metricPath:["summary","serendipity"],threshold:.25,comparison:"lt",title:"METRIC_REC_SERENDIPITY_TITLE",description:"METRIC_REC_SERENDIPITY_DESC",priority:"low",category:"diversity",iconKey:"LightbulbIcon",actions:["METRIC_REC_SERENDIPITY_ACTION_1","METRIC_REC_SERENDIPITY_ACTION_2","METRIC_REC_SERENDIPITY_ACTION_3","METRIC_REC_SERENDIPITY_ACTION_4"],metrics:[{name:"METRIC_SERENDIPITY",valuePath:["summary","serendipity"],valueMultiplier:100,target:25,unit:"%"}]},{id:"improve-confidence",specialLogic:{type:"avgConfidence",params:{threshold:.6,paths:[["confidence_distribution","collaborative","avg"],["confidence_distribution","content","avg"],["confidence_distribution","hybrid","avg"]]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CONFIDENCE_TITLE",description:"METRIC_REC_CONFIDENCE_DESC",priority:"high",category:"confidence",iconKey:"AlertCircleIcon",actions:["METRIC_REC_CONFIDENCE_ACTION_1","METRIC_REC_CONFIDENCE_ACTION_2","METRIC_REC_CONFIDENCE_ACTION_3","METRIC_REC_CONFIDENCE_ACTION_4"],metrics:[{name:"METRIC_AVG_CONFIDENCE",valuePath:[],valueMultiplier:100,target:60,unit:"%"}]},{id:"improve-model-type",specialLogic:{type:"worstModel",params:{threshold:.5,models:{collaborative:{path:["confidence_distribution","collaborative","avg"],name:"METRIC_MODEL_COLLABORATIVE",actions:["METRIC_REC_MODEL_COLLAB_ACTION_1","METRIC_REC_MODEL_COLLAB_ACTION_2","METRIC_REC_MODEL_COLLAB_ACTION_3","METRIC_REC_MODEL_COLLAB_ACTION_4"]},content:{path:["confidence_distribution","content","avg"],name:"METRIC_MODEL_CONTENT",actions:["METRIC_REC_MODEL_CONTENT_ACTION_1","METRIC_REC_MODEL_CONTENT_ACTION_2","METRIC_REC_MODEL_CONTENT_ACTION_3","METRIC_REC_MODEL_CONTENT_ACTION_4"]},hybrid:{path:["confidence_distribution","hybrid","avg"],name:"METRIC_MODEL_HYBRID",actions:["METRIC_REC_MODEL_HYBRID_ACTION_1","METRIC_REC_MODEL_HYBRID_ACTION_2","METRIC_REC_MODEL_HYBRID_ACTION_3","METRIC_REC_MODEL_HYBRID_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_MODEL_TITLE",description:"METRIC_REC_MODEL_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:[],metrics:[{name:"METRIC_MODEL_CONFIDENCE",valuePath:[],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-low-confidence-categories",specialLogic:{type:"lowConfidenceCategories",params:{threshold:.5,maxCategories:3}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CATEGORIES_TITLE",description:"METRIC_REC_CATEGORIES_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:["METRIC_REC_CATEGORIES_ACTION_1","METRIC_REC_CATEGORIES_ACTION_2","METRIC_REC_CATEGORIES_ACTION_3","METRIC_REC_CATEGORIES_ACTION_4"],metrics:[]},{id:"improve-confidence-factors",specialLogic:{type:"lowestConfidenceFactor",params:{threshold:.4,factors:{user_history_size:{name:"METRIC_FACTOR_USER_HISTORY",actions:["METRIC_REC_FACTOR_USER_HISTORY_ACTION_1","METRIC_REC_FACTOR_USER_HISTORY_ACTION_2","METRIC_REC_FACTOR_USER_HISTORY_ACTION_3","METRIC_REC_FACTOR_USER_HISTORY_ACTION_4"]},item_popularity:{name:"METRIC_FACTOR_ITEM_POPULARITY",actions:["METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4"]},category_strength:{name:"METRIC_FACTOR_CATEGORY_STRENGTH",actions:["METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4"]},model_type:{name:"METRIC_FACTOR_MODEL_TYPE",actions:["METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_FACTOR_TITLE",description:"METRIC_REC_FACTOR_DESC",priority:"medium",category:"confidence",iconKey:"UsersIcon",actions:[],metrics:[{name:"METRIC_FACTOR",valuePath:[],valueMultiplier:100,target:40,unit:"%"}]},{id:"address-negative-trend",specialLogic:{type:"negativeTrend",params:{minDays:3,threshold:.9,path:["confidence_trends","last_7_days"]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_TREND_TITLE",description:"METRIC_REC_TREND_DESC",priority:"high",category:"confidence",iconKey:"TrendingUpIcon",actions:["METRIC_REC_TREND_ACTION_1","METRIC_REC_TREND_ACTION_2","METRIC_REC_TREND_ACTION_3","METRIC_REC_TREND_ACTION_4"],metrics:[{name:"METRIC_CONFIDENCE_CHANGE",valuePath:[],valueMultiplier:100,target:0,unit:"%"}]}],G={COMPONENT_TITLE:"Recomendaciones Inteligentes",COMPONENT_DESCRIPTION:"Basadas en el an\xe1lisis de {count} \xe1reas de mejora potencial",LOADING_TEXT:"Analizando m\xe9tricas para generar recomendaciones...",OPTIMIZED_TITLE:"Sistema Optimizado",OPTIMIZED_DESCRIPTION:"No se han detectado \xe1reas de mejora significativas en este momento.",OPTIMIZED_MESSAGE:"Las m\xe9tricas actuales indican que su sistema de recomendaci\xf3n est\xe1 funcionando de manera \xf3ptima. Contin\xfae monitoreando las m\xe9tricas para mantener este rendimiento.",RECOMMENDATION_COUNT:"{count} {count, plural, one {recomendaci\xf3n} other {recomendaciones}}",ACTIONS_TITLE:"Acciones recomendadas:",CATEGORY_ACCURACY:"Precisi\xf3n y Relevancia",CATEGORY_DIVERSITY:"Diversidad y Descubrimiento",CATEGORY_CONFIDENCE:"Confianza y Fiabilidad",CATEGORY_PERFORMANCE:"Rendimiento y Eficiencia",METRIC_PRECISION:"Precisi\xf3n",METRIC_DIVERSITY:"Diversidad",METRIC_CATALOG_COVERAGE:"Cobertura del Cat\xe1logo",METRIC_NOVELTY:"Novedad",METRIC_NDCG:"NDCG",METRIC_SERENDIPITY:"Serendipia",METRIC_AVG_CONFIDENCE:"Confianza Promedio",METRIC_CONFIDENCE_CHANGE:"Cambio en confianza",METRIC_TARGET:"Meta: {value}{unit}",METRIC_MODEL_COLLABORATIVE:"colaborativo",METRIC_MODEL_CONTENT:"basado en contenido",METRIC_MODEL_HYBRID:"h\xedbrido",METRIC_MODEL_CONFIDENCE:"Confianza {model}",METRIC_FACTOR_USER_HISTORY:"tama\xf1o del historial de usuario",METRIC_FACTOR_ITEM_POPULARITY:"popularidad del \xedtem",METRIC_FACTOR_CATEGORY_STRENGTH:"fuerza de categor\xeda",METRIC_FACTOR_MODEL_TYPE:"tipo de modelo",METRIC_FACTOR:"Factor {factor}",METRIC_REC_PRECISION_TITLE:"Mejorar la precisi\xf3n de las recomendaciones",METRIC_REC_DIVERSITY_TITLE:"Aumentar la diversidad de recomendaciones",METRIC_REC_NOVELTY_TITLE:"Incrementar la novedad de las recomendaciones",METRIC_REC_RANKING_TITLE:"Mejorar la calidad del ranking de recomendaciones",METRIC_REC_SERENDIPITY_TITLE:"Aumentar la serendipia en las recomendaciones",METRIC_REC_CONFIDENCE_TITLE:"Aumentar la confianza en las recomendaciones",METRIC_REC_MODEL_TITLE:"Mejorar el rendimiento del modelo {model}",METRIC_REC_CATEGORIES_TITLE:"Mejorar recomendaciones en categor\xedas de baja confianza",METRIC_REC_FACTOR_TITLE:"Mejorar el factor de {factor}",METRIC_REC_TREND_TITLE:"Abordar tendencia negativa en confianza",METRIC_REC_PRECISION_DESC:"La precisi\xf3n actual est\xe1 por debajo del umbral recomendado del 50%. Considere ajustar los modelos para mejorar la relevancia de las recomendaciones.",METRIC_REC_DIVERSITY_DESC:"Las recomendaciones actuales muestran poca diversidad, lo que puede llevar a una experiencia mon\xf3tona para los usuarios.",METRIC_REC_NOVELTY_DESC:"El sistema tiende a recomendar \xedtems muy populares, limitando el descubrimiento de nuevos productos.",METRIC_REC_RANKING_DESC:"El NDCG actual indica que el orden de las recomendaciones podr\xeda no ser \xf3ptimo, afectando la experiencia del usuario.",METRIC_REC_SERENDIPITY_DESC:"Las recomendaciones actuales podr\xedan ser demasiado predecibles, limitando el descubrimiento de \xedtems inesperados pero relevantes.",METRIC_REC_CONFIDENCE_DESC:"El nivel de confianza promedio est\xe1 por debajo del umbral recomendado del 60%, lo que puede indicar incertidumbre en las predicciones.",METRIC_REC_MODEL_DESC:"El modelo {model} muestra un nivel de confianza bajo, lo que reduce la efectividad general del sistema.",METRIC_REC_CATEGORIES_DESC:'Algunas categor\xedas de productos muestran niveles de confianza particularmente bajos, especialmente "{category}".',METRIC_REC_FACTOR_DESC:"El factor de {factor} tiene una contribuci\xf3n baja a la confianza general, lo que indica un \xe1rea de mejora.",METRIC_REC_TREND_DESC:"La confianza promedio ha disminuido significativamente en los \xfaltimos d\xedas, lo que podr\xeda indicar un problema emergente.",METRIC_REC_PRECISION_ACTION_1:"Ajustar los par\xe1metros del modelo colaborativo para dar m\xe1s peso a interacciones recientes",METRIC_REC_PRECISION_ACTION_2:"Aumentar el tama\xf1o del conjunto de entrenamiento con m\xe1s datos de interacciones",METRIC_REC_PRECISION_ACTION_3:"Implementar t\xe9cnicas de filtrado para eliminar outliers en los datos de entrenamiento",METRIC_REC_DIVERSITY_ACTION_1:"Implementar un algoritmo de re-ranking para diversificar los resultados",METRIC_REC_DIVERSITY_ACTION_2:"Ajustar los par\xe1metros del modelo para reducir la concentraci\xf3n en \xedtems populares",METRIC_REC_DIVERSITY_ACTION_3:"Introducir un factor de aleatoriedad controlada en las recomendaciones finales",METRIC_REC_DIVERSITY_ACTION_4:"Considerar categor\xedas menos representadas en las recomendaciones",METRIC_REC_NOVELTY_ACTION_1:"Ajustar el algoritmo para dar m\xe1s peso a \xedtems menos populares",METRIC_REC_NOVELTY_ACTION_2:"Implementar un factor de penalizaci\xf3n para \xedtems extremadamente populares",METRIC_REC_NOVELTY_ACTION_3:'Crear un segmento espec\xedfico de "descubrimientos" con \xedtems de baja popularidad pero alta relevancia',METRIC_REC_NOVELTY_ACTION_4:"Considerar t\xe9cnicas de filtrado colaborativo basadas en vecindad para encontrar \xedtems nicho",METRIC_REC_RANKING_ACTION_1:"Implementar o mejorar algoritmos de Learning-to-Rank",METRIC_REC_RANKING_ACTION_2:"Ajustar los factores de relevancia en el c\xe1lculo del ranking",METRIC_REC_RANKING_ACTION_3:"Considerar se\xf1ales adicionales como recencia o tendencia para el ranking",METRIC_REC_RANKING_ACTION_4:"Experimentar con diferentes funciones de p\xe9rdida optimizadas para NDCG",METRIC_REC_SERENDIPITY_ACTION_1:"Implementar un componente de serendipia que ocasionalmente introduzca \xedtems inesperados",METRIC_REC_SERENDIPITY_ACTION_2:"Explorar conexiones no obvias entre preferencias de usuario y productos",METRIC_REC_SERENDIPITY_ACTION_3:"Considerar t\xe9cnicas de recomendaci\xf3n basadas en conocimiento para descubrir relaciones no evidentes",METRIC_REC_SERENDIPITY_ACTION_4:"Experimentar con modelos de grafos para encontrar conexiones de segundo o tercer grado",METRIC_REC_CONFIDENCE_ACTION_1:"Recopilar m\xe1s datos de interacciones para mejorar la base de predicciones",METRIC_REC_CONFIDENCE_ACTION_2:"Ajustar los umbrales de confianza para filtrar recomendaciones de baja calidad",METRIC_REC_CONFIDENCE_ACTION_3:"Implementar t\xe9cnicas de ensemble para combinar m\xfaltiples modelos",METRIC_REC_CONFIDENCE_ACTION_4:"Mejorar la calidad de los metadatos de productos para fortalecer el modelo basado en contenido",METRIC_REC_MODEL_COLLAB_ACTION_1:"Ajustar los par\xe1metros de similitud entre usuarios/\xedtems",METRIC_REC_MODEL_COLLAB_ACTION_2:"Implementar t\xe9cnicas de factorizaci\xf3n matricial m\xe1s avanzadas",METRIC_REC_MODEL_COLLAB_ACTION_3:"Aumentar el n\xfamero de vecinos considerados en el algoritmo KNN",METRIC_REC_MODEL_COLLAB_ACTION_4:"Reducir el umbral de filtrado para interacciones m\xednimas",METRIC_REC_MODEL_CONTENT_ACTION_1:"Mejorar la calidad y cantidad de atributos de los productos",METRIC_REC_MODEL_CONTENT_ACTION_2:"Implementar t\xe9cnicas de procesamiento de lenguaje natural m\xe1s avanzadas",METRIC_REC_MODEL_CONTENT_ACTION_3:"Ajustar los pesos de los diferentes atributos en el c\xe1lculo de similitud",METRIC_REC_MODEL_CONTENT_ACTION_4:"Considerar la incorporaci\xf3n de embeddings pre-entrenados para representar productos",METRIC_REC_MODEL_HYBRID_ACTION_1:"Ajustar los pesos relativos de los modelos colaborativo y basado en contenido",METRIC_REC_MODEL_HYBRID_ACTION_2:"Implementar un meta-modelo para seleccionar din\xe1micamente el mejor enfoque",METRIC_REC_MODEL_HYBRID_ACTION_3:"Considerar factores contextuales adicionales en la combinaci\xf3n de modelos",METRIC_REC_MODEL_HYBRID_ACTION_4:"Experimentar con diferentes estrategias de ensemble",METRIC_REC_CATEGORIES_ACTION_1:'Recopilar m\xe1s datos de interacciones para la categor\xeda "{category}"',METRIC_REC_CATEGORIES_ACTION_2:"Crear modelos espec\xedficos por categor\xeda para las categor\xedas problem\xe1ticas",METRIC_REC_CATEGORIES_ACTION_3:"Mejorar los metadatos de productos en estas categor\xedas",METRIC_REC_CATEGORIES_ACTION_4:"Considerar reglas de negocio espec\xedficas para complementar el algoritmo",METRIC_REC_FACTOR_USER_HISTORY_ACTION_1:"Implementar estrategias para aumentar la recopilaci\xf3n de interacciones de usuarios",METRIC_REC_FACTOR_USER_HISTORY_ACTION_2:"Mejorar el onboarding para capturar preferencias iniciales",METRIC_REC_FACTOR_USER_HISTORY_ACTION_3:"Considerar t\xe9cnicas de cold-start para usuarios con poco historial",METRIC_REC_FACTOR_USER_HISTORY_ACTION_4:"Implementar recomendaciones basadas en sesi\xf3n para usuarios nuevos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1:"Balancear mejor las recomendaciones entre \xedtems populares y de nicho",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2:"Implementar un sistema de boosting temporal para nuevos productos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3:"Crear segmentos de recomendaci\xf3n espec\xedficos para diferentes niveles de popularidad",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4:"Mejorar la estrategia de exploraci\xf3n vs. explotaci\xf3n",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1:"Mejorar la taxonom\xeda de categor\xedas para capturar mejor las preferencias",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2:"Implementar an\xe1lisis de afinidad de categor\xeda m\xe1s sofisticado",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3:"Considerar la jerarqu\xeda completa de categor\xedas en el c\xe1lculo de afinidad",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4:"Crear modelos espec\xedficos para categor\xedas principales",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1:"Experimentar con diferentes arquitecturas de modelos",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2:"Implementar un sistema de selecci\xf3n din\xe1mica de modelos basado en contexto",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3:"Considerar modelos m\xe1s avanzados como deep learning para recomendaciones",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4:"Mejorar la estrategia de ensemble para combinar modelos",METRIC_REC_TREND_ACTION_1:"Investigar cambios recientes en datos o modelos que podr\xedan haber afectado la confianza",METRIC_REC_TREND_ACTION_2:"Verificar la calidad de las interacciones recientes",METRIC_REC_TREND_ACTION_3:"Considerar un rollback a una versi\xf3n anterior del modelo si la tendencia persiste",METRIC_REC_TREND_ACTION_4:"Implementar monitoreo en tiempo real para detectar cambios abruptos en m\xe9tricas clave"};function z(e,a){return e.replace(/{([^}]+)}/g,(e,i)=>{let r=i.match(/^([^,]+),\s*plural,\s*one\s*{([^}]+)}\s*other\s*{([^}]+)}$/);if(r){let e=r[1].trim(),i=r[2].trim(),t=r[3].trim();return 1===a[e]?i:t}return void 0!==a[i]?a[i]:e})}function k(e){var a;return null!=(a=G[e])?a:e}function V(e,a){return a.reduce((e,a)=>e&&"object"==typeof e&&null!==e&&a in e?e[a]:void 0,e)}function B(e){let{performanceData:a,confidenceData:i,isLoading:s=!1}=e,[n,c]=(0,t.useState)([]),[l,d]=(0,t.useState)(void 0);(0,t.useEffect)(()=>{a&&i&&c(function(e,a,i){let r=[];F.forEach(s=>{if(s.specialLogic){let e=function(e,a,i,r){if(!e.specialLogic)return null;let{type:s,params:n}=e.specialLogic;switch(s){case"avgConfidence":{if(!(null==n?void 0:n.paths)||!n.threshold)return null;let a=n.paths.map(e=>V(i,e)||0),s=a.reduce((e,a)=>e+a,0)/a.length;if(s<n.threshold)return{id:e.id,title:z(k(e.title),{}),description:z(k(e.description),{}),priority:e.priority,category:e.category,icon:t.createElement(r[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>k(e)),metrics:e.metrics.map(e=>({name:z(k(e.name),{}),value:s*(e.valueMultiplier||1),target:e.target,unit:e.unit}))};return null}case"worstModel":{if(!(null==n?void 0:n.models)||!n.threshold)return null;let a={};Object.entries(n.models).forEach(e=>{let[r,t]=e,s=V(i,t.path);a[r]={value:"number"==typeof s?s:0,name:G[t.name],actions:t.actions}});let s=Object.entries(a).sort((e,a)=>{let[,i]=e,[,r]=a;return i.value-r.value})[0];if(s&&s[1].value<n.threshold){let a=s[1].name;return{id:"".concat(e.id,"-").concat(s[0]),title:z(G[e.title],{model:a}),description:z(G[e.description],{model:a}),priority:e.priority,category:e.category,icon:t.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:s[1].actions.map(e=>G[e]),metrics:e.metrics.map(e=>({name:z(G[e.name],{model:a}),value:s[1].value*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"lowConfidenceCategories":{if(!(null==n?void 0:n.threshold)||!n.maxCategories||0===Object.keys(i.category_confidence).length)return null;let a=Object.entries(i.category_confidence).sort((e,a)=>{let[,i]=e,[,r]=a;return i-r}).slice(0,n.maxCategories);if(a[0][1]<n.threshold){let i=a[0][0];return{id:e.id,title:z(G[e.title],{}),description:z(G[e.description],{category:i}),priority:e.priority,category:e.category,icon:t.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:e.actions.map(e=>z(G[e],{category:i})),metrics:a.map(e=>({name:"Confianza en ".concat(e[0]),value:100*e[1],target:100*n.threshold,unit:"%"}))}}return null}case"lowestConfidenceFactor":{if(!(null==n?void 0:n.factors)||!n.threshold)return null;let a=Object.entries(i.confidence_factors).sort((e,a)=>{let[,i]=e,[,r]=a;return i-r})[0];if(a[1]<n.threshold){let i=a[0],s=n.factors[i];if(!s)return null;let o=G[s.name];return{id:"".concat(e.id,"-").concat(i),title:z(k(e.title),{factor:o}),description:z(k(e.description),{factor:o}),priority:e.priority,category:e.category,icon:t.createElement(r[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:s.actions.map(e=>k(e)),metrics:e.metrics.map(e=>({name:z(k(e.name),{factor:o}),value:a[1]*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"negativeTrend":{if(!(null==n?void 0:n.minDays)||!n.threshold||!n.path)return null;let a=V(i,n.path);if(a&&Array.isArray(a)&&a.length>=n.minDays){var o,c;let i=a.slice(-n.minDays);if((null==(o=i[i.length-1])?void 0:o.avg_confidence)<(null==(c=i[0])?void 0:c.avg_confidence)*n.threshold){let a=(i[i.length-1].avg_confidence/i[0].avg_confidence-1)*100;return{id:e.id,title:z(k(e.title),{}),description:z(k(e.description),{}),priority:e.priority,category:e.category,icon:t.createElement(r[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>k(e)),metrics:e.metrics.map(e=>({name:z(k(e.name),{}),value:a,target:e.target,unit:e.unit}))}}}return null}default:return null}}(s,0,a,i);e&&r.push(e);return}let n=V("summary"===s.metricPath[0]?e:a,s.metricPath);void 0!==n&&"number"==typeof n&&function(e,a,i){switch(i){case"lt":return e<a;case"gt":return e>a;case"eq":return e===a;case"lte":return e<=a;case"gte":return e>=a;default:return!1}}(n,s.threshold,s.comparison)&&r.push({id:s.id,title:z(k(s.title),{}),description:z(k(s.description),{}),priority:s.priority,category:s.category,icon:t.createElement(i[s.iconKey],{className:"h-5 w-5 ".concat("high"===s.priority?"text-red-500":"medium"===s.priority?"text-amber-500":"text-blue-500")}),actions:s.actions.map(e=>k(e)),metrics:s.metrics.map(i=>{let r=V("summary"===i.valuePath[0]?e:a,i.valuePath);return{name:z(k(i.name),{}),value:r*(i.valueMultiplier||1),target:i.target,unit:i.unit}})})});let s={high:0,medium:1,low:2};return r.sort((e,a)=>s[e.priority]-s[a.priority]),r}(a,i,{BarChart2Icon:M.A,ShuffleIcon:v.A,ZapIcon:y.A,AlertCircleIcon:j.A,DatabaseIcon:b.A,UsersIcon:D.A,TrendingUpIcon:L.A,LightbulbIcon:S.A}))},[a,i]);let m=n.reduce((e,a)=>(e[a.category]||(e[a.category]=[]),e[a.category].push(a),e),{}),_={accuracy:{name:G.CATEGORY_ACCURACY,icon:(0,r.jsx)(w.mm,{icon:M.A,size:"md",context:"metric"})},diversity:{name:G.CATEGORY_DIVERSITY,icon:(0,r.jsx)(w.mm,{icon:v.A,size:"md",context:"metric"})},confidence:{name:G.CATEGORY_CONFIDENCE,icon:(0,r.jsx)(w.mm,{icon:P.A,size:"md",context:"metric"})},performance:{name:G.CATEGORY_PERFORMANCE,icon:(0,r.jsx)(w.mm,{icon:y.A,size:"md",context:"metric"})}};return s?(0,r.jsxs)(o.Zp,{className:"w-full",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.mm,{icon:S.A,size:"md",context:"warning"}),G.COMPONENT_TITLE]}),(0,r.jsx)(o.BT,{children:G.LOADING_TEXT})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"})]},e))})})]}):a&&i&&0!==n.length?(0,r.jsxs)(o.Zp,{className:"w-full",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.mm,{icon:S.A,size:"md",context:"warning"}),G.COMPONENT_TITLE]}),(0,r.jsx)(o.BT,{children:z(G.COMPONENT_DESCRIPTION,{count:Object.values(m).flat().length})})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(O,{type:"single",collapsible:!0,value:l,onValueChange:d,className:"space-y-4",children:Object.entries(m).map(e=>{var a,i;let[t,s]=e;return(0,r.jsxs)(f,{value:t,className:"border rounded-lg overflow-hidden",children:[(0,r.jsx)(g,{className:"px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-left",children:[null==(a=_[t])?void 0:a.icon,(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:(null==(i=_[t])?void 0:i.name)||t}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:z(G.RECOMMENDATION_COUNT,{count:s.length})})]})]})}),(0,r.jsx)(A,{className:"px-0",children:(0,r.jsx)("div",{className:"space-y-4 pt-2",children:s.map(e=>(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-100 dark:border-gray-800",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"mt-1",children:e.icon}),(0,r.jsxs)("div",{className:"space-y-2 w-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.title}),(0,r.jsx)(p.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",className:"capitalize",children:e.priority})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 my-3",children:e.metrics.map((e,a)=>(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 p-2 rounded-md",children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"font-medium",children:[e.value.toFixed(1),e.unit]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:z(G.METRIC_TARGET,{value:e.target,unit:e.unit})})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1",children:(0,r.jsx)("div",{className:"h-1.5 rounded-full ".concat(e.value>=e.target?"bg-green-500":e.value>=.8*e.target?"bg-amber-500":"bg-red-500"),style:{width:"".concat(Math.min(100,e.value/e.target*100),"%")}})})]},a))}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"text-sm font-medium mb-2",children:G.ACTIONS_TITLE}),(0,r.jsx)("ul",{className:"space-y-1",children:e.actions.map((e,a)=>(0,r.jsxs)("li",{className:"text-sm flex items-start gap-2",children:[(0,r.jsx)(w.mm,{icon:Y.A,size:"sm",context:"muted",className:"mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},a))})]})]})]})},e.id))})})]},t)})})})]}):(0,r.jsxs)(o.Zp,{className:"w-full",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.mm,{icon:P.A,size:"md",context:"success"}),G.OPTIMIZED_TITLE]}),(0,r.jsx)(o.BT,{children:G.OPTIMIZED_DESCRIPTION})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("p",{className:"text-muted-foreground",children:G.OPTIMIZED_MESSAGE})})]})}var U=i(467),H=i(1169);function K(){let{token:e,apiKey:a}=(0,n.A)(),[i,C]=(0,t.useState)(!1),[E,p]=(0,t.useState)("performance"),[N,h]=(0,t.useState)("all"),[x,O]=(0,t.useState)("all"),{data:f,error:g,isLoading:A,mutate:M}=(0,s.Ay)(e&&a?["recommendation-performance",N,x]:null,async e=>{let[,a,i]=e;return await (0,U.S)("all"!==a?parseInt(a):void 0,"all"!==i?i:void 0)}),{data:v,error:y,isLoading:j,mutate:b}=(0,s.Ay)(e&&a?"confidence-metrics":null,async()=>await (0,U.K)()),D=async()=>{C(!0);try{await Promise.all([M(),b()]),m.o.success("M\xe9tricas actualizadas")}catch(e){(0,_.h)(e,"Error al actualizar las m\xe9tricas")}finally{C(!1)}},L=[];if(f&&"object"==typeof f){let e=f.offline_metrics;(null==e?void 0:e.models)&&Array.isArray(e.models)&&e.models.forEach(e=>{var a,i;L.push({id:(null==(a=e.model_id)?void 0:a.toString())||(null==(i=e.id)?void 0:i.toString())||Math.random().toString(),name:"".concat(e.model_type||e.type||"Model"," (").concat(e.version||"v1",")")})})}return(0,r.jsx)(H.A,{children:(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"M\xe9tricas de Recomendaci\xf3n"}),(0,r.jsx)(T,{content:(0,r.jsxs)("div",{className:"space-y-3 max-w-md",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Acerca de las M\xe9tricas de Recomendaci\xf3n"}),(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:"Esta p\xe1gina muestra m\xe9tricas detalladas sobre el rendimiento y la confianza de los modelos de recomendaci\xf3n."}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"M\xe9tricas de Rendimiento:"}),(0,r.jsxs)("ul",{className:"list-disc pl-4 text-gray-700 dark:text-gray-300",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Precisi\xf3n y Recall:"})," Miden la relevancia de las recomendaciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"NDCG y MAP:"})," Eval\xfaan la calidad del ranking de recomendaciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Diversidad y Novedad:"})," Miden la variedad y originalidad de las recomendaciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Serendipia:"})," Eval\xfaa la capacidad de sorprender positivamente al usuario"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"font-medium text-gray-800 dark:text-gray-200",children:"M\xe9tricas de Confianza:"}),(0,r.jsxs)("ul",{className:"list-disc pl-4 text-gray-700 dark:text-gray-300",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Distribuci\xf3n de Confianza:"})," Niveles de confianza por tipo de modelo"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Confianza por Categor\xeda:"})," Qu\xe9 categor\xedas generan recomendaciones m\xe1s confiables"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Factores de Confianza:"})," Qu\xe9 aspectos influyen m\xe1s en la confianza"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Tendencias:"})," Evoluci\xf3n de la confianza a lo largo del tiempo"]})]})]}),(0,r.jsx)("p",{className:"text-gray-700 dark:text-gray-300 italic",children:"Pase el cursor sobre cualquier m\xe9trica para obtener informaci\xf3n detallada sobre su significado y c\xe1lculo."})]}),iconSize:18,side:"right",align:"start",iconClassName:"mt-2"})]})}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"An\xe1lisis detallado del rendimiento y confianza de los modelos de recomendaci\xf3n"})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsxs)(c.Button,{variant:"outline",onClick:D,disabled:i||A||j,className:"flex items-center gap-2",children:[(0,r.jsx)(R.A,{className:"h-4 w-4 ".concat(i?"animate-spin":"")}),"Actualizar"]})})]}),f&&v&&(0,r.jsx)(B,{performanceData:f,confidenceData:v,isLoading:A||j}),(0,r.jsx)(d.tU,{value:E,onValueChange:p,className:"w-full",children:(0,r.jsxs)(d.j7,{className:"grid grid-cols-2 w-full max-w-md",children:[(0,r.jsxs)(d.Xi,{value:"performance",className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"Rendimiento"]}),(0,r.jsxs)(d.Xi,{value:"confidence",className:"flex items-center gap-2",children:[(0,r.jsx)(I.A,{className:"h-4 w-4"}),"Confianza"]})]})}),"performance"===E&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsxs)("div",{className:"w-full md:w-auto",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Modelo"}),(0,r.jsxs)(l.l6,{value:N,onValueChange:h,children:[(0,r.jsx)(l.bq,{className:"w-full md:w-[200px]",children:(0,r.jsx)(l.yv,{placeholder:"Todos los modelos"})}),(0,r.jsxs)(l.gC,{children:[(0,r.jsx)(l.eb,{value:"all",children:"Todos los modelos"}),L.map(e=>(0,r.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,r.jsxs)("div",{className:"w-full md:w-auto",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Tipo de M\xe9trica"}),(0,r.jsxs)(l.l6,{value:x,onValueChange:O,children:[(0,r.jsx)(l.bq,{className:"w-full md:w-[200px]",children:(0,r.jsx)(l.yv,{placeholder:"Todas las m\xe9tricas"})}),(0,r.jsxs)(l.gC,{children:[(0,r.jsx)(l.eb,{value:"all",children:"Todas las m\xe9tricas"}),[{id:"precision",name:"Precisi\xf3n"},{id:"recall",name:"Recall"},{id:"ndcg",name:"NDCG"},{id:"map",name:"MAP"},{id:"catalog_coverage",name:"Cobertura del Cat\xe1logo"},{id:"diversity",name:"Diversidad"},{id:"novelty",name:"Novedad"},{id:"serendipity",name:"Serendipia"}].map(e=>(0,r.jsx)(l.eb,{value:e.id,children:e.name},e.id))]})]})]})]}),f&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Precisi\xf3n"}),(0,r.jsx)(T,{content:(0,r.jsx)("div",{children:"Porcentaje de recomendaciones relevantes del total de recomendaciones mostradas"}),iconSize:14})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof f&&f){let e=f.offline_metrics,a=null==e?void 0:e.precision;return a?"".concat((100*a).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"NDCG"}),(0,r.jsx)(T,{content:(0,r.jsx)("div",{children:"Normalized Discounted Cumulative Gain - eval\xfaa la calidad del ranking"}),iconSize:14})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof f&&f){let e=f.offline_metrics,a=null==e?void 0:e.ndcg;return a?"".concat((100*a).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Diversidad"}),(0,r.jsx)(T,{content:(0,r.jsx)("div",{children:"Variedad en las recomendaciones mostradas"}),iconSize:14})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof f&&f){let e=f.offline_metrics,a=null==e?void 0:e.diversity;return a?"".concat((100*a).toFixed(1),"%"):"--"}return"--"})()})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{className:"pb-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.ZB,{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Cobertura"}),(0,r.jsx)(T,{content:(0,r.jsx)("div",{children:"Porcentaje del cat\xe1logo que se est\xe1 recomendando"}),iconSize:14})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:(()=>{if("object"==typeof f&&f){let e=f.offline_metrics,a=null==e?void 0:e.catalog_coverage;return a?"".concat((100*a).toFixed(1),"%"):"--"}return"--"})()})})]})]}),A&&(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),g&&(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de rendimiento"})})]}),"confidence"===E&&(0,r.jsxs)("div",{className:"space-y-6",children:[v&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{children:"Distribuci\xf3n de Confianza"})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:"object"==typeof v&&v?Object.entries(v.confidence_distribution||{}).map(e=>{let[a,i]=e;return(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"capitalize",children:a}),(0,r.jsx)("span",{className:"font-medium",children:i.avg?"".concat((100*i.avg).toFixed(1),"%"):"N/A"})]},a)}):(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Datos de confianza no disponibles"})})})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{children:"Confianza por Categor\xeda"})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:"object"==typeof v&&v?Object.entries(v.category_confidence||{}).slice(0,5).map(e=>{let[a,i]=e;return(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"capitalize",children:a}),(0,r.jsx)("span",{className:"font-medium",children:"number"==typeof i?"".concat((100*i).toFixed(1),"%"):"N/A"})]},a)}):(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Datos por categor\xeda no disponibles"})})})})]})]}),j&&(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),y&&(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-red-500",children:"Error al cargar m\xe9tricas de confianza"})})]})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,9352,1445,5674,3753,8034,3843,5813,9566,1414,2285,833,2092,3999,1833,8441,1684,7358],()=>a(3270)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5598],{1214:(e,a,s)=>{Promise.resolve().then(s.bind(s,9407))},4944:(e,a,s)=>{"use strict";s.d(a,{k:()=>i});var t=s(5155),r=s(2115),n=s(4472),l=s(9434);let i=r.forwardRef((e,a)=>{let{className:s,value:r,...i}=e;return(0,t.jsx)(n.bL,{ref:a,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...i,children:(0,t.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});i.displayName=n.bL.displayName},6102:(e,a,s)=>{"use strict";s.d(a,{Bc:()=>l,ZI:()=>d,k$:()=>o,m_:()=>i});var t=s(5155);s(2115);var r=s(3815),n=s(9434);function l(e){let{delayDuration:a=0,...s}=e;return(0,t.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...s})}function i(e){let{...a}=e;return(0,t.jsx)(l,{children:(0,t.jsx)(r.bL,{"data-slot":"tooltip",...a})})}function o(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"tooltip-trigger",...a})}function d(e){let{className:a,sideOffset:s=0,children:l,...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:s,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...i,children:[l,(0,t.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},7313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var t=s(5155),r=s(2115),n=s(6176),l=s(9434);let i=n.bL,o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=n.B8.displayName;let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:bg-background/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});d.displayName=n.l9.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=n.UC.displayName},7588:(e,a,s)=>{"use strict";s.d(a,{h:()=>c});var t=s(6671),r=s(3464);class n extends Error{static isApiError(e){return e instanceof n}static fromResponse(e){return new n(e.message,e.status_code,e.error_code,e.details)}constructor(e,a,s,t){super(e),this.status=a,this.errorCode=s,this.details=t,this.name="ApiError"}}let l=r.A.create({baseURL:"http://localhost:8001",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{var a;return e.token&&(e.headers.Authorization="Bearer ".concat(e.token)),!e.apiKey||(null==(a=e.url)?void 0:a.endsWith("/auth/token"))||(e.headers["X-API-Key"]=e.apiKey),delete e.token,delete e.apiKey,e}),l.interceptors.response.use(e=>e,e=>{if(e.response){let a=e.response.data;throw n.fromResponse(a)}if(e.request)throw new n("No se recibi\xf3 respuesta del servidor",0,"NETWORK_ERROR",null);throw new n(e.message,0,"REQUEST_ERROR",null)});var i=s(6874),o=s.n(i),d=s(2115);function c(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Ha ocurrido un error";return(console.group("API Error Handler"),console.error("Error details:",e),e instanceof n)?"RATE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(d.createElement("div",{},"Limite de tasa excedido. Intenta de nuevo mas tarde o ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"actualiza tu plan")," para aumentar tus limites.")):"RESOURCE_LIMIT_EXCEEDED"===e.errorCode?void t.o.error(d.createElement("div",{},"Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para continuar.")):"SUBSCRIPTION_LIMIT"===e.errorCode?void t.o.error(d.createElement("div",{},"Has alcanzado el limite de tu suscripcion. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para obtener mas capacidad.")):"TRAINING_FREQUENCY_LIMIT"===e.errorCode?void t.o.error(d.createElement("div",{},"Has alcanzado el limite de frecuencia de entrenamiento. ",d.createElement(o(),{href:"/billing",className:"underline font-medium"},"Actualiza tu plan")," para entrenar con mayor frecuencia.")):"UNAUTHORIZED"===e.errorCode||"INVALID_API_KEY"===e.errorCode?void t.o.error(d.createElement("div",{},"Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",d.createElement(o(),{href:"/api-keys",className:"underline font-medium"},"Regenerar API Key"))):"VALIDATION_ERROR"===e.errorCode?void t.o.error(d.createElement("div",{},"Error de validacion: "+e.message+". ",d.createElement("a",{href:"https://docs.rayuela.ai/api-reference",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Consultar documentacion"))):"INSUFFICIENT_DATA"===e.errorCode?void t.o.error(d.createElement("div",{},"Datos insuficientes para generar recomendaciones. ",d.createElement("a",{href:"https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",target:"_blank",rel:"noopener noreferrer",className:"underline font-medium"},"Cargar mas datos"))):"SERVICE_UNAVAILABLE"===e.errorCode?void t.o.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde."):(t.o.error(e.message||a),void console.log("Unhandled API error code:",e.errorCode)):e instanceof Error?void t.o.error(e.message||a):void(t.o.error(a),console.groupEnd())}},8338:(e,a,s)=>{"use strict";s.d(a,{KC:()=>h,ZV:()=>u,a3:()=>p,cR:()=>x,z3:()=>m});var t=s(5155);s(2115);var r=s(6126),n=s(646),l=s(3904),i=s(4186),o=s(4861),d=s(5690),c=s(1243);function m(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(a<0?0:a))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][s]}function u(e){return new Intl.NumberFormat().format(e)}function x(e){let a={className:"h-4 w-4"};switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(n.A,{...a,className:"h-4 w-4 text-green-500"});case"running":case"processing":case"in_progress":return(0,t.jsx)(l.A,{...a,className:"h-4 w-4 text-blue-500 animate-spin"});case"pending":case"queued":case"waiting":return(0,t.jsx)(i.A,{...a,className:"h-4 w-4 text-yellow-500"});case"failed":case"error":case"cancelled":return(0,t.jsx)(o.A,{...a,className:"h-4 w-4 text-red-500"});case"starting":case"initializing":return(0,t.jsx)(d.A,{...a,className:"h-4 w-4 text-blue-400"});case"warning":return(0,t.jsx)(c.A,{...a,className:"h-4 w-4 text-amber-500"});default:return(0,t.jsx)(i.A,{...a,className:"h-4 w-4 text-gray-400"})}}function h(e){switch(null==e?void 0:e.toLowerCase()){case"completed":case"success":case"finished":return(0,t.jsx)(r.E,{variant:"success",className:"text-xs",children:"Completado"});case"running":case"processing":case"in_progress":return(0,t.jsx)(r.E,{variant:"info",className:"text-xs",children:"En progreso"});case"pending":case"queued":case"waiting":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Pendiente"});case"failed":case"error":case"cancelled":return(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"Fallido"});case"starting":case"initializing":return(0,t.jsx)(r.E,{variant:"secondary",className:"text-xs",children:"Iniciando"});case"warning":return(0,t.jsx)(r.E,{variant:"warning",className:"text-xs",children:"Advertencia"});default:return(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:"Desconocido"})}}function p(e){let a=Math.floor(e/1e3),s=Math.floor(a/60),t=Math.floor(s/60),r=Math.floor(t/24);return r>0?"".concat(r,"d ").concat(t%24,"h ").concat(s%60,"m"):t>0?"".concat(t,"h ").concat(s%60,"m ").concat(a%60,"s"):s>0?"".concat(s,"m ").concat(a%60,"s"):"".concat(a,"s")}},9407:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>J});var t=s(5155),r=s(2115),n=s(9107),l=s(3008),i=s(8794),o=s(9859),d=s(6695),c=s(8856),m=s(285),u=s(4944),x=s(6126),h=s(5365),p=s(6671),f=s(7588),g=s(3904),j=s(5339),v=s(4186),N=s(2713),b=s(4213),y=s(4788),w=s(1284),E=s(3109),A=s(8534),z=s(7916),C=s(7018),_=s(2502),I=s(1516),M=s(7313);let B={blue:{primary:"rgb(59, 130, 246)",background:"rgba(59, 130, 246, 0.5)"},green:{primary:"rgb(16, 185, 129)",background:"rgba(16, 185, 129, 0.5)"},red:{primary:"rgb(239, 68, 68)",background:"rgba(239, 68, 68, 0.5)"}};function k(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return"".concat(e.toFixed(a),"%")}var P=s(8338),R=s(1243);function L(e){let{data:a=[],isLoading:s=!1,error:n=null,title:l="Uso de API",apiCallsLimit:i,storageLimit:o}=e,[m,u]=(0,r.useState)([]);(0,r.useEffect)(()=>{u(a&&a.length>0?a:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e&&0!==e.length?e.map(e=>({date:e.date,apiCalls:e.apiCalls,storage:e.storage})):function(){let e=[],a=new Date;for(let s=29;s>=0;s--){let t=new Date(a);t.setDate(t.getDate()-s),e.push({date:t.toISOString().split("T")[0],apiCalls:Math.floor(500*Math.random())+100,storage:Math.floor(1e7*Math.random())+1e6})}return e}()}([]))},[a]);let h={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{mode:"index",intersect:!1,callbacks:{title:e=>"Fecha: ".concat(e[0].label),label:e=>{let a=e.dataset.label||"";return a&&(a+=": "),null!==e.parsed.y&&(a.includes("Almacenamiento")?a+=(0,P.z3)(1024*e.parsed.y*1024):a+=(0,P.ZV)(e.parsed.y)),a},footer:e=>{let a=e[0].dataset.label||"";if(a.includes("Llamadas")&&i){let a=Math.min(e[0].parsed.y/i*100,100);return"".concat(k(a)," del l\xedmite (").concat((0,P.ZV)(i),")")}if(a.includes("Almacenamiento")&&o){let a=o/1048576,s=Math.min(e[0].parsed.y/a*100,100);return"".concat(k(s)," del l\xedmite (").concat((0,P.z3)(o),")")}return""}}}},scales:{y:{beginAtZero:!0,ticks:{font:{size:11},callback:function(e){return(0,P.ZV)(e)}}}}};return(m.map(e=>e.date.slice(5)),m.map(e=>e.apiCalls),B.blue.primary,B.blue.background,m.map(e=>e.date.slice(5)),m.map(e=>e.storage/1048576),B.green.primary,B.green.background,{...h,plugins:{...h.plugins,title:{display:!0,text:"Llamadas a la API por d\xeda",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:i?{annotations:{limitLine:{type:"line",yMin:i,yMax:i,borderColor:B.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:"L\xedmite: ".concat((0,P.ZV)(i)),position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},{...h,plugins:{...h.plugins,title:{display:!0,text:"Almacenamiento utilizado (MB)",font:{size:13,weight:"normal"},padding:{top:10,bottom:10}},annotation:o?{annotations:{limitLine:{type:"line",yMin:o/1048576,yMax:o/1048576,borderColor:B.red.primary,borderWidth:2,borderDash:[6,6],label:{display:!0,content:"L\xedmite: ".concat((0,P.z3)(o)),position:"end",backgroundColor:"rgba(239, 68, 68, 0.7)",font:{size:11}}}}}:void 0}},s)?(0,t.jsxs)(d.Zp,{className:"transition-all duration-300 hover:shadow-md",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:(0,t.jsx)(c.E,{className:"h-6 w-1/3"})}),(0,t.jsx)(d.BT,{children:(0,t.jsx)(c.E,{className:"h-4 w-1/2"})})]}),(0,t.jsx)(d.Wu,{className:"h-80",children:(0,t.jsx)(c.E,{className:"h-full w-full rounded-md"})})]}):n?(0,t.jsxs)(d.Zp,{className:"transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"text-destructive flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-5 w-5"}),"Error al cargar datos"]}),(0,t.jsx)(d.BT,{className:"text-destructive/70",children:"No se pudieron cargar los datos de uso"})]}),(0,t.jsxs)(d.Wu,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)("p",{className:"text-destructive mb-2",children:"Ocurri\xf3 un error al cargar los datos de uso."}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Intenta refrescar la p\xe1gina o contacta al soporte t\xe9cnico."})]})]}):(0,t.jsxs)(d.Zp,{className:"w-full",children:[(0,t.jsx)(d.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),l]}),(0,t.jsx)(d.BT,{children:"Monitoreo de uso en el rango seleccionado"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.E,{variant:"info",children:"Per\xedodo: \xdaltimos 30 d\xedas"}),m.some(e=>e.apiCalls>0)&&(0,t.jsx)(x.E,{variant:"success",children:"Datos disponibles"})]})]})}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)(M.tU,{defaultValue:"chart",className:"w-full",children:(0,t.jsxs)(M.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(M.Xi,{value:"chart",className:"transition-all duration-200 data-[state=active]:bg-info/10 data-[state=active]:text-info",children:"Gr\xe1fico"}),(0,t.jsx)(M.Xi,{value:"table",className:"transition-all duration-200 data-[state=active]:bg-success/10 data-[state=active]:text-success",children:"Tabla"})]})})})]})}_.t1.register(_.PP,_.kc,_.FN,_.No,_.E8,_.hE,_.m_,_.s$,I.A);var T=s(2355),Z=s(3052),D=s(1228),U=s(9434);function O(e){let{className:a,classNames:s,showOutsideDays:r=!0,...n}=e;return(0,t.jsx)(D.h,{showOutsideDays:r,className:(0,U.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,U.cn)((0,m.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,U.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===n.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,U.cn)((0,m.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{Chevron:e=>{let{orientation:a,...s}=e,r="left"===a?T.A:Z.A;return(0,t.jsx)(r,{className:"size-4",...s})}},...n})}var S=s(4410);function H(e){let{...a}=e;return(0,t.jsx)(S.bL,{"data-slot":"popover",...a})}function F(e){let{...a}=e;return(0,t.jsx)(S.l9,{"data-slot":"popover-trigger",...a})}function W(e){let{className:a,align:s="center",sideOffset:r=4,...n}=e;return(0,t.jsx)(S.ZL,{children:(0,t.jsx)(S.UC,{"data-slot":"popover-content",align:s,sideOffset:r,className:(0,U.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",a),...n})})}var V=s(9074),K=s(7716),G=s(3439);function q(e){let{onChange:a,className:s}=e,i=new Date,[o,d]=(0,r.useState)(30),[c,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)({from:(0,n.e)(i,30),to:i}),p=e=>{let s={from:(0,n.e)(i,e),to:i};h(s),d(e),a(s)};return(0,t.jsxs)("div",{className:(0,U.cn)("flex flex-wrap gap-2",s),children:[[{label:"\xdaltimos 7 d\xedas",days:7},{label:"\xdaltimos 14 d\xedas",days:14},{label:"\xdaltimos 30 d\xedas",days:30}].map(e=>(0,t.jsx)(m.Button,{variant:o===e.days?"default":"outline",size:"sm",onClick:()=>p(e.days),className:o===e.days?"bg-blue-600 hover:bg-blue-700":"",children:e.label},e.days)),(0,t.jsxs)(H,{open:c,onOpenChange:u,children:[(0,t.jsx)(F,{asChild:!0,children:(0,t.jsxs)(m.Button,{variant:0===o?"default":"outline",size:"sm",className:(0,U.cn)("flex items-center gap-1",0===o?"bg-blue-600 hover:bg-blue-700":""),children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),0===o?"".concat((0,l.GP)(x.from,"dd/MM/yy")," - ").concat((0,l.GP)(x.to,"dd/MM/yy")):"Personalizado"]})}),(0,t.jsx)(W,{className:"w-auto p-0",align:"end",children:(0,t.jsx)(O,{initialFocus:!0,mode:"range",defaultMonth:x.from,selected:x,onSelect:e=>{if((null==e?void 0:e.from)&&(null==e?void 0:e.to)){let s={from:e.from,to:e.to};h(s),d(0),a(s),u(!1)}},numberOfMonths:2,locale:G.es,disabled:e=>(0,K.d)(e,i)})})]})]})}var X=s(6102);function $(){var e,a,s,_,I,M,B,k,R,T;(0,o.As)();let[Z,D]=(0,r.useState)(!1),[U,O]=(0,r.useState)([]),[S,H]=(0,r.useState)({from:(0,n.e)(new Date,30),to:new Date}),{isLoading:F,error:W}=(0,o.A$)(),{accountData:V,error:K,isLoading:G}=(0,o.T4)(),{usageData:$,error:Y,isLoading:Q,mutate:J}=(0,o.TB)(),{data:ee,error:ea,isLoading:es,mutate:et}=(0,o.Xn)(S.from,S.to);(0,r.useEffect)(()=>{ee&&O(ee.map(e=>({date:e.date,apiCalls:e.api_calls,storage:e.storage})))},[ee]);let er=async()=>{D(!0);try{await Promise.all([J(),et()]),p.o.success("Datos de uso actualizados")}catch(e){(0,f.h)(e,"Error al actualizar los datos de uso")}finally{D(!1)}};if((()=>{var e;if(U.length)return U.reduce((e,a)=>e+a.apiCalls,0),null==(e=U[U.length-1])||e.storage})(),Q||G||es)return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,t.jsx)(c.E,{className:"h-9 w-24"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map(e=>(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{className:"pb-2",children:[(0,t.jsx)(c.E,{className:"h-5 w-24"}),(0,t.jsx)(c.E,{className:"h-4 w-16"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)(c.E,{className:"h-8 w-16 mx-auto mb-2"}),(0,t.jsx)(c.E,{className:"h-2 w-full"})]})]},e))}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(c.E,{className:"h-6 w-32"}),(0,t.jsx)(c.E,{className:"h-4 w-48"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)(c.E,{className:"h-64 w-full"})})]})]});let en=()=>{var e,a;return!!($&&((null==(e=$.apiCalls)?void 0:e.used)||(null==(a=$.storage)?void 0:a.usedBytes)))};return Y||K||ea?(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-display",children:"Uso de API"}),(0,t.jsxs)(m.Button,{onClick:er,disabled:Z,variant:"outline",size:"sm",className:"h-9",children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2 ".concat(Z?"animate-spin":"")}),"Actualizar"]})]}),(0,t.jsxs)(h.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)(h.XL,{children:"Error al cargar datos"}),(0,t.jsx)(h.TN,{children:("string"==typeof Y?Y:null==Y?void 0:Y.message)||("string"==typeof K?K:null==K?void 0:K.message)||("string"==typeof ea?ea:null==ea?void 0:ea.message)||"Ocurri\xf3 un error al cargar los datos de uso."})]})]}):(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-display mb-2",children:"Uso de API"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Monitorea el uso de tu API y almacenamiento en tiempo real"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(m.Button,{onClick:er,disabled:Z,variant:"outline",size:"sm",className:"h-9",children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2 ".concat(Z?"animate-spin":"")}),"Actualizar"]})})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)(q,{onChange:e=>{H(e)},className:"w-auto"})}),(0,t.jsxs)("div",{className:"mb-6 flex items-center text-sm text-muted-foreground",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"sm",context:"muted",className:"mr-1"}),(0,t.jsxs)("span",{children:["\xdaltima actualizaci\xf3n: ","Hace unos momentos"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)(d.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900",children:[(0,t.jsxs)(d.aR,{className:"pb-2",children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:N.A,size:"md",context:"primary",className:"mr-2"}),"Llamadas a la API"]}),(0,t.jsxs)(d.BT,{children:["Total en el periodo: ",S.from?(0,l.GP)(S.from,"dd/MM/yy"):"inicio"," - ",S.to?(0,l.GP)(S.to,"dd/MM/yy"):"fin"]})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"text-metric text-center py-2 text-primary",children:(0,P.ZV)((null==$||null==(e=$.apiCalls)?void 0:e.used)||0)}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Uso actual"}),(0,t.jsx)("span",{className:"font-medium",children:(0,P.ZV)((null==$||null==(a=$.apiCalls)?void 0:a.used)||0)})]}),(0,t.jsx)(u.k,{value:Math.min(((null==$||null==(s=$.apiCalls)?void 0:s.used)||0)/((null==$||null==(_=$.apiCalls)?void 0:_.limit)||1e4)*100,100),className:"h-1.5"}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"xs",context:"muted",className:"mr-1"}),(0,t.jsx)("span",{children:"Pr\xf3ximo reset:"})]}),(0,t.jsx)("span",{className:"font-medium",children:(null==$||null==(I=$.apiCalls)?void 0:I.resetDate)?(0,l.GP)((0,i.H)($.apiCalls.resetDate),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,t.jsxs)(d.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900",children:[(0,t.jsxs)(d.aR,{className:"pb-2",children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:b.A,size:"md",context:"primary",className:"mr-2"}),"Almacenamiento",(0,t.jsx)(X.Bc,{children:(0,t.jsxs)(X.m_,{children:[(0,t.jsx)(X.k$,{asChild:!0,children:(0,t.jsx)(A.mm,{icon:y.A,size:"sm",context:"muted",className:"ml-1 cursor-help"})}),(0,t.jsx)(X.ZI,{children:(0,t.jsx)("p",{className:"text-xs",children:"Datos almacenados en tu cuenta"})})]})})]}),(0,t.jsxs)(d.BT,{children:["Espacio utilizado al ",S.to?(0,l.GP)(S.to,"dd/MM/yy"):"final del periodo"]})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"text-metric text-center py-2 text-success",children:(0,P.z3)((null==$||null==(M=$.storage)?void 0:M.usedBytes)||0)}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Uso actual"}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium",children:(0,P.z3)((null==$||null==(B=$.storage)?void 0:B.usedBytes)||0)}),(0,t.jsxs)("span",{className:"ml-1",children:["/ ",(null==$||null==(k=$.storage)?void 0:k.limitBytes)?(0,P.z3)($.storage.limitBytes):"∞"]})]})]}),(0,t.jsx)(u.k,{value:(null==$||null==(R=$.storage)?void 0:R.percentage)||0,className:"h-1.5"}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:v.A,size:"xs",context:"muted",className:"mr-1"}),(0,t.jsx)("span",{children:"\xdaltima medici\xf3n:"})]}),(0,t.jsx)("span",{className:"font-medium",children:(null==$||null==(T=$.storage)?void 0:T.lastMeasured)?(0,l.GP)((0,i.H)($.storage.lastMeasured),"dd/MM/yy HH:mm"):"No disponible"})]})]})]})]}),(0,t.jsxs)(d.Zp,{className:"transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900",children:[(0,t.jsxs)(d.aR,{className:"pb-2",children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-lg",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"md",context:"primary",className:"mr-2"}),"Estado de la Cuenta"]}),(0,t.jsx)(d.BT,{children:"Informaci\xf3n general de tu cuenta"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-3 py-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Plan actual:"})]}),(0,t.jsx)(x.E,{variant:"default",className:"text-xs",children:(null==V?void 0:V.plan)||"B\xe1sico"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(A.mm,{icon:b.A,size:"sm",context:"primary",className:"mr-1.5"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Datos disponibles:"})]}),(0,t.jsx)("span",{className:"font-medium text-sm",children:en()?"S\xed":"No"})]})]}),!en()&&(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-4 text-center",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"xl",context:"muted",className:"mb-2"}),(0,t.jsx)("p",{className:"text-muted-foreground font-medium text-sm",children:"No hay datos disponibles"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground text-center max-w-md",children:"Los datos de uso se mostrar\xe1n aqu\xed una vez que comiences a usar la API."})]})]})]})]}),(0,t.jsxs)(d.Zp,{className:"mb-8",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center",children:[(0,t.jsx)(A.mm,{icon:E.A,size:"md",context:"primary",className:"mr-2"}),"Historial de Uso"]}),(0,t.jsx)(d.BT,{children:"Tendencias de uso de la API durante el periodo seleccionado"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("div",{className:"mb-4 p-3 bg-info-light rounded-lg border border-info/20",children:(0,t.jsxs)("p",{className:"text-sm flex items-start",children:[(0,t.jsx)(A.mm,{icon:w.A,size:"md",context:"info",className:"mr-2 shrink-0 mt-0.5"}),(0,t.jsx)("span",{children:"El gr\xe1fico muestra las tendencias de uso de tu API durante el periodo seleccionado. Los datos se actualizan autom\xe1ticamente y reflejan las llamadas realizadas y el almacenamiento utilizado."})]})}),(0,t.jsx)(L,{data:U,isLoading:es,error:ea})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(z.s,{priceId:"default_price_id",planName:"Pro"}),(0,t.jsx)(C.q,{})]})]})}var Y=s(8126),Q=s.n(Y);function J(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(Q(),{children:[(0,t.jsx)("title",{children:"Panel de Uso | Rayuela API"}),(0,t.jsx)("meta",{name:"description",content:"Monitorea el uso de tu API, visualiza estad\xedsticas y administra tus l\xedmites de consumo."})]}),(0,t.jsx)($,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[5647,6874,9352,1445,5674,3753,4214,8034,3843,9566,1414,2285,679,2092,3999,8696,8441,1684,7358],()=>a(1214)),_N_E=e.O()}]);
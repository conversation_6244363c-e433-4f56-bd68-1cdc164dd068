(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{106:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},347:()=>{},381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1684:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},4357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4927:(e,t,n)=>{"use strict";n.d(t,{C1:()=>O,bL:()=>E});var r=n(2115),a=n(6101),l=n(6081),i=n(5185),o=n(5845),s=n(5503),u=n(1275),c=n(2712),d=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[a,l]=r.useState(),i=r.useRef(null),o=r.useRef(e),s=r.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(i.current);s.current="mounted"===u?e:"none"},[u]),(0,c.N)(()=>{let t=i.current,n=o.current;if(n!==e){let r=s.current,a=f(t);e?d("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==a?d("ANIMATION_OUT"):d("UNMOUNT"),o.current=e}},[e,d]),(0,c.N)(()=>{if(a){var e;let t,n=null!=(e=a.ownerDocument.defaultView)?e:window,r=e=>{let r=f(i.current).includes(e.animationName);if(e.target===a&&r&&(d("ANIMATION_END"),!o.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},l=e=>{e.target===a&&(s.current=f(i.current))};return a.addEventListener("animationstart",l),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{n.clearTimeout(t),a.removeEventListener("animationstart",l),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}d("ANIMATION_END")},[a,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:r.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),i="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),o=(0,a.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=r&&"isReactWarning"in r&&r.isReactWarning;return a?e.ref:(a=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||l.isPresent?r.cloneElement(i,{ref:o}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}d.displayName="Presence";var m=n(3540),p=n(5155),y="Checkbox",[v,h]=(0,l.A)(y),[k,N]=v(y);function b(e){let{__scopeCheckbox:t,checked:n,children:a,defaultChecked:l,disabled:i,form:s,name:u,onCheckedChange:c,required:d,value:f="on",internal_do_not_use_render:m}=e,[v,h]=(0,o.i)({prop:n,defaultProp:null!=l&&l,onChange:c,caller:y}),[N,b]=r.useState(null),[x,g]=r.useState(null),E=r.useRef(!1),w=!N||!!s||!!N.closest("form"),O={checked:v,disabled:i,setChecked:h,control:N,setControl:b,name:u,form:s,value:f,hasConsumerStoppedPropagationRef:E,required:d,defaultChecked:!M(l)&&l,isFormControl:w,bubbleInput:x,setBubbleInput:g};return(0,p.jsx)(k,{scope:t,...O,children:"function"==typeof m?m(O):a})}var x="CheckboxTrigger",g=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:l,onClick:o,...s}=e,{control:u,value:c,disabled:d,checked:f,required:y,setControl:v,setChecked:h,hasConsumerStoppedPropagationRef:k,isFormControl:b,bubbleInput:g}=N(x,n),E=(0,a.s)(t,v),w=r.useRef(f);return r.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>h(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,h]),(0,p.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":M(f)?"mixed":f,"aria-required":y,"data-state":T(f),"data-disabled":d?"":void 0,disabled:d,value:c,...s,ref:E,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(o,e=>{h(e=>!!M(e)||!e),g&&b&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})})});g.displayName=x;var E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:a,defaultChecked:l,required:i,disabled:o,value:s,onCheckedChange:u,form:c,...d}=e;return(0,p.jsx)(b,{__scopeCheckbox:n,checked:a,defaultChecked:l,disabled:o,required:i,onCheckedChange:u,name:r,form:c,value:s,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(g,{...d,ref:t,__scopeCheckbox:n}),r&&(0,p.jsx)(A,{__scopeCheckbox:n})]})}})});E.displayName=y;var w="CheckboxIndicator",O=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...a}=e,l=N(w,n);return(0,p.jsx)(d,{present:r||M(l.checked)||!0===l.checked,children:(0,p.jsx)(m.sG.span,{"data-state":T(l.checked),"data-disabled":l.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});O.displayName=w;var _="CheckboxBubbleInput",A=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...l}=e,{control:i,hasConsumerStoppedPropagationRef:o,checked:c,defaultChecked:d,required:f,disabled:y,name:v,value:h,form:k,bubbleInput:b,setBubbleInput:x}=N(_,n),g=(0,a.s)(t,x),E=(0,s.Z)(c),w=(0,u.X)(i);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(E!==c&&e){let n=new Event("click",{bubbles:t});b.indeterminate=M(c),e.call(b,!M(c)&&c),b.dispatchEvent(n)}},[b,E,c,o]);let O=r.useRef(!M(c)&&c);return(0,p.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=d?d:O.current,required:f,disabled:y,name:v,value:h,form:k,...l,tabIndex:-1,ref:g,style:{...l.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function M(e){return"indeterminate"===e}function T(e){return M(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=_},7292:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,106,23)),Promise.resolve().then(n.bind(n,6671)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,3999))}},e=>{var t=t=>e(e.s=t);e.O(0,[488,7690,9352,1445,5674,3753,2092,3999,8441,1684,7358],()=>t(7292)),_N_E=e.O()}]);
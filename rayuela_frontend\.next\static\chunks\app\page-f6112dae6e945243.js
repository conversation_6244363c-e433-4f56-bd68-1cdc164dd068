(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1684:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},3792:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});var r=n(5155),a=n(2115),i=n(5695),l=n(3999);function s(){let{user:e,isLoading:t}=(0,l.A)(),n=(0,i.useRouter)();return(0,a.useEffect)(()=>{t||(e?n.push("/dashboard"):n.push("/home"))},[e,t,n]),(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Loading..."}),(0,r.jsx)("p",{children:"Please wait while we redirect you to the appropriate page."})]})})}},4357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4927:(e,t,n)=>{"use strict";n.d(t,{C1:()=>O,bL:()=>w});var r=n(2115),a=n(6101),i=n(6081),l=n(5185),s=n(5845),o=n(5503),u=n(1275),d=n(2712),c=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[a,i]=r.useState(),l=r.useRef(null),s=r.useRef(e),o=r.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(l.current);o.current="mounted"===u?e:"none"},[u]),(0,d.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=o.current,a=f(t);e?c("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==a?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),(0,d.N)(()=>{if(a){var e;let t,n=null!=(e=a.ownerDocument.defaultView)?e:window,r=e=>{let r=f(l.current).includes(e.animationName);if(e.target===a&&r&&(c("ANIMATION_END"),!s.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},i=e=>{e.target===a&&(o.current=f(l.current))};return a.addEventListener("animationstart",i),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{n.clearTimeout(t),a.removeEventListener("animationstart",i),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}c("ANIMATION_END")},[a,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,a.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=r&&"isReactWarning"in r&&r.isReactWarning;return a?e.ref:(a=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:s}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}c.displayName="Presence";var p=n(3540),m=n(5155),y="Checkbox",[h,v]=(0,i.A)(y),[N,k]=h(y);function x(e){let{__scopeCheckbox:t,checked:n,children:a,defaultChecked:i,disabled:l,form:o,name:u,onCheckedChange:d,required:c,value:f="on",internal_do_not_use_render:p}=e,[h,v]=(0,s.i)({prop:n,defaultProp:null!=i&&i,onChange:d,caller:y}),[k,x]=r.useState(null),[b,g]=r.useState(null),w=r.useRef(!1),E=!k||!!o||!!k.closest("form"),O={checked:h,disabled:l,setChecked:v,control:k,setControl:x,name:u,form:o,value:f,hasConsumerStoppedPropagationRef:w,required:c,defaultChecked:!M(i)&&i,isFormControl:E,bubbleInput:b,setBubbleInput:g};return(0,m.jsx)(N,{scope:t,...O,children:"function"==typeof p?p(O):a})}var b="CheckboxTrigger",g=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:i,onClick:s,...o}=e,{control:u,value:d,disabled:c,checked:f,required:y,setControl:h,setChecked:v,hasConsumerStoppedPropagationRef:N,isFormControl:x,bubbleInput:g}=k(b,n),w=(0,a.s)(t,h),E=r.useRef(f);return r.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>v(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,v]),(0,m.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":M(f)?"mixed":f,"aria-required":y,"data-state":T(f),"data-disabled":c?"":void 0,disabled:c,value:d,...o,ref:w,onKeyDown:(0,l.m)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(s,e=>{v(e=>!!M(e)||!e),g&&x&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})})});g.displayName=b;var w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:a,defaultChecked:i,required:l,disabled:s,value:o,onCheckedChange:u,form:d,...c}=e;return(0,m.jsx)(x,{__scopeCheckbox:n,checked:a,defaultChecked:i,disabled:s,required:l,onCheckedChange:u,name:r,form:d,value:o,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(g,{...c,ref:t,__scopeCheckbox:n}),r&&(0,m.jsx)(j,{__scopeCheckbox:n})]})}})});w.displayName=y;var E="CheckboxIndicator",O=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...a}=e,i=k(E,n);return(0,m.jsx)(c,{present:r||M(i.checked)||!0===i.checked,children:(0,m.jsx)(p.sG.span,{"data-state":T(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});O.displayName=E;var A="CheckboxBubbleInput",j=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...i}=e,{control:l,hasConsumerStoppedPropagationRef:s,checked:d,defaultChecked:c,required:f,disabled:y,name:h,value:v,form:N,bubbleInput:x,setBubbleInput:b}=k(A,n),g=(0,a.s)(t,b),w=(0,o.Z)(d),E=(0,u.X)(l);r.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!s.current;if(w!==d&&e){let n=new Event("click",{bubbles:t});x.indeterminate=M(d),e.call(x,!M(d)&&d),x.dispatchEvent(n)}},[x,w,d,s]);let O=r.useRef(!M(d)&&d);return(0,m.jsx)(p.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=c?c:O.current,required:f,disabled:y,name:h,value:v,form:N,...i,tabIndex:-1,ref:g,style:{...i.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function M(e){return"indeterminate"===e}function T(e){return M(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=A},9924:(e,t,n)=>{Promise.resolve().then(n.bind(n,3792))}},e=>{var t=t=>e(e.s=t);e.O(0,[9352,1445,5674,3753,2092,3999,8441,1684,7358],()=>t(9924)),_N_E=e.O()}]);
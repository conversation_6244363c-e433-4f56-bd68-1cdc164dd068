// src/app/(dashboard)/api-keys/page.tsx
"use client";

import { useState, useMemo } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from '@/lib/auth';
import { useApiKeys } from '@/lib/useApiKeys';
import { ApiKey } from '@/lib/api';
import { toast } from "sonner";
import { handleApiError } from "@/lib/error-handler";
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AlertCircle, Plus, Edit, Trash2, Key, Copy, Search, Filter, BarChart3, Info, Shield, Target, Users } from 'lucide-react';
import Link from 'next/link';
import InitialApiKeyModal from '@/components/auth/InitialApiKeyModal';
import { DensePageLayout, DenseCard, DenseTableRow } from "@/components/ui/layout";
import { SemanticIcon } from "@/components/ui/icon";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Types for filtering
type FilterStatus = 'all' | 'active' | 'revoked';

export default function ApiKeysPage() {
  const { token, user, isLoading: isAuthLoading } = useAuth();
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingApiKey, setEditingApiKey] = useState<ApiKey | null>(null);
  const [newKeyName, setNewKeyName] = useState('');
  const [editKeyName, setEditKeyName] = useState('');

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<FilterStatus>('all');

  // Using the consolidated useApiKeys hook
  const {
    data: apiKeysData,
    error: apiKeysError,
    isLoading: isApiKeysLoading,
    createApiKey,
    updateApiKey,
    revokeApiKey,
    isCreating,
    isUpdating,
    isRevoking,
    getFormattedApiKey
  } = useApiKeys({
    revalidateOnFocus: true,
    refreshInterval: 30000, // Actualizar cada 30 segundos
    dedupingInterval: 5000, // Evitar peticiones duplicadas en 5 segundos
    errorRetryCount: 3
  });

  // Filtered and searched API keys
  const filteredApiKeys = useMemo(() => {
    if (!apiKeysData?.api_keys) return [];

    return apiKeysData.api_keys.filter((apiKey) => {
      // Status filter
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && apiKey.is_active) ||
        (statusFilter === 'revoked' && !apiKey.is_active);

      // Search filter
      const matchesSearch = searchQuery === '' || 
        (apiKey.name && apiKey.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        apiKey.id.toString().includes(searchQuery);

      return matchesStatus && matchesSearch;
    });
  }, [apiKeysData?.api_keys, statusFilter, searchQuery]);

  // Enhanced API key formatting for better display
  const getFormattedApiKeyForDisplay = (apiKey: ApiKey): string => {
    if (!apiKey.prefix || !apiKey.last_chars) return 'ray_••••••••••••••••••••';
    
    // Show more characters for better identification: prefix + 8 dots + last 6 chars
    const middleDots = '••••••••••••••••';
    return `${apiKey.prefix}${middleDots}${apiKey.last_chars}`;
  };

  // Full API key for copying (when available)
  const getFullApiKeyForCopy = (apiKey: ApiKey): string | null => {
    return getFormattedApiKey(apiKey);
  };

  const handleCreateApiKey = async () => {
    try {
      const result = await createApiKey({
        name: newKeyName.trim() || `API Key ${new Date().toLocaleDateString('es-ES')}`
      });
      if (result?.api_key) {
        setNewApiKey(result.api_key);
        setShowNewKeyModal(true);
        setShowCreateDialog(false);
        setNewKeyName('');
        toast.success("API Key creada con éxito");
      }
    } catch (error: unknown) {
      handleApiError(error, "Error al crear la API Key");
    }
  };

  const handleEditApiKey = async () => {
    if (!editingApiKey) return;

    try {
      const result = await updateApiKey(editingApiKey.id, {
        name: editKeyName.trim()
      });
      if (result) {
        setShowEditDialog(false);
        setEditingApiKey(null);
        setEditKeyName('');
        toast.success("API Key actualizada con éxito");
      }
    } catch (error: unknown) {
      handleApiError(error, "Error al actualizar la API Key");
    }
  };

  const handleRevokeApiKey = async (apiKeyId: number, apiKeyName?: string) => {
    try {
      await revokeApiKey(apiKeyId);
      toast.success(`API Key ${apiKeyName ? `"${apiKeyName}"` : ''} revocada con éxito`);
    } catch (error: unknown) {
      handleApiError(error, "Error al revocar la API Key");
    }
  };

  const handleCopyApiKey = (apiKey: ApiKey) => {
    const fullKey = getFullApiKeyForCopy(apiKey);
    if (fullKey) {
      navigator.clipboard.writeText(fullKey);
      toast.success("API Key copiada al portapapeles");
    }
  };

  const openEditDialog = (apiKey: ApiKey) => {
    setEditingApiKey(apiKey);
    setEditKeyName(apiKey.name || '');
    setShowEditDialog(true);
  };

  const handleModalClose = () => {
    setShowNewKeyModal(false);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
  };

  // Estado de carga combinado
  const isLoading = isAuthLoading || isApiKeysLoading;

  // Estado de Error (Auth o API Key)
  if (apiKeysError) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error al cargar las API Keys</AlertTitle>
          <AlertDescription>
            {apiKeysError.message}
            <br />
            Intenta refrescar la página o contacta a soporte.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Estado: No autenticado
  if (!token || !user) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="warning">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Sesión no verificada</AlertTitle>
          <AlertDescription>
            No se pudo verificar tu sesión. Por favor, intenta <Link href="/login" className="underline">iniciar sesión</Link> de nuevo.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Mostrar loading mientras se cargan los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        {/* Header Section with subtle background grouping */}
        <div className="bg-card/30 border border-border/50 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">API Keys</h1>
              <p className="text-muted-foreground mt-2">
                Gestiona múltiples claves para tus proyectos y entornos
              </p>
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Skeleton className="h-6 w-6" />
                <Skeleton className="h-6 w-48" />
              </CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-64" />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const totalKeys = apiKeysData?.api_keys?.length || 0;
  const activeKeys = apiKeysData?.api_keys?.filter(key => key.is_active)?.length || 0;
  const revokedKeys = totalKeys - activeKeys;

  return (
    <DensePageLayout
      title="API Keys"
      description="Gestiona múltiples claves de API para acceder a los servicios de Rayuela"
      actions={
        <>
          {/* Stats summary in header */}
          <div className="flex gap-6 text-sm text-muted-foreground mr-4">
            <span className="flex items-center gap-2">
              <SemanticIcon icon={Key} size="sm" context="success" />
              {activeKeys} activas
            </span>
            {revokedKeys > 0 && (
              <span className="flex items-center gap-2">
                <SemanticIcon icon={AlertCircle} size="sm" context="error" />
                {revokedKeys} revocadas
              </span>
            )}
            <span className="font-medium">Total: {totalKeys}</span>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <SemanticIcon icon={Plus} size="sm" context="neutral" />
                Nueva API Key
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Crear Nueva API Key</DialogTitle>
                <DialogDescription>
                  Crea una nueva API Key para acceder a los servicios de Rayuela. Puedes asignarle un nombre descriptivo.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="newKeyName">Nombre de la API Key (opcional)</Label>
                  <Input
                    id="newKeyName"
                    placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreateApiKey} disabled={isCreating}>
                  {isCreating ? 'Creando...' : 'Crear API Key'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      }
    >

      {/* Search and Filter Section */}
      <DenseCard
        title="Filtros y Búsqueda"
        icon={<SemanticIcon icon={Filter} size="md" context="primary" />}
        description="Encuentra rápidamente las API Keys que necesitas"
      >
        <div className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <SemanticIcon icon={Search} size="sm" context="muted" className="absolute left-3 top-1/2 transform -translate-y-1/2" />
                <Input
                  placeholder="Buscar por nombre o ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Label htmlFor="status-filter" className="text-sm font-medium whitespace-nowrap">Estado:</Label>
              <Select value={statusFilter} onValueChange={(value: FilterStatus) => setStatusFilter(value)}>
                <SelectTrigger id="status-filter" className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas</SelectItem>
                  <SelectItem value="active">Activas</SelectItem>
                  <SelectItem value="revoked">Revocadas</SelectItem>
                </SelectContent>
              </Select>
              {(searchQuery || statusFilter !== 'all') && (
                <Button variant="outline" size="sm" onClick={clearFilters}>
                  Limpiar
                </Button>
              )}
            </div>
          </div>
          {(searchQuery || statusFilter !== 'all') && (
            <div className="mt-2 text-sm text-muted-foreground">
              Mostrando {filteredApiKeys.length} de {totalKeys} API Keys
            </div>
          )}
        </div>
      </DenseCard>

      {/* Main Content Section */}
      <DenseCard
        title="Tus API Keys"
        icon={<SemanticIcon icon={Key} size="md" context="primary" />}
        description="Cada clave funciona independientemente y puede ser usada para diferentes proyectos o entornos."
      >
        {/* Enhanced Table Section */}
        <div className="overflow-hidden">
          <Table>
            <TableHeader className="bg-muted/10">
              <TableRow className="border-b border-border/30">
                <TableHead className="font-semibold">Nombre</TableHead>
                <TableHead className="font-semibold">Clave</TableHead>
                <TableHead className="font-semibold">Estado</TableHead>
                <TableHead className="font-semibold">Creación</TableHead>
                <TableHead className="font-semibold">Último uso</TableHead>
                <TableHead className="font-semibold">Uso <span className="text-xs font-normal">(próximamente)</span></TableHead>
                <TableHead className="text-right font-semibold">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
                {filteredApiKeys && filteredApiKeys.length > 0 ? (
                  filteredApiKeys.map((apiKey: ApiKey, index: number) => (
                    <DenseTableRow key={apiKey.id} index={index} className={!apiKey.is_active ? 'opacity-60' : ''}>
                      <TableCell className="font-medium py-4">
                        <div className="flex items-center gap-2">
                          <SemanticIcon 
                            icon={apiKey.is_active ? Key : AlertCircle} 
                            size="xs" 
                            context={apiKey.is_active ? "success" : "error"}
                          />
                          {apiKey.name || `API Key ${apiKey.id}`}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          <code className="text-sm bg-muted/50 px-2 py-1 rounded border font-mono">
                            {getFormattedApiKeyForDisplay(apiKey)}
                          </code>
                          {apiKey.is_active && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyApiKey(apiKey)}
                              className="h-6 w-6 p-0 hover:bg-muted/50"
                              title="Copiar API Key completa"
                            >
                              <SemanticIcon icon={Copy} size="xs" context="muted" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge variant={apiKey.is_active ? "success" : "destructive"}>
                          {apiKey.is_active ? "Activa" : "Revocada"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground py-4">
                        {apiKey.created_at ? format(new Date(apiKey.created_at), "d 'de' MMMM, yyyy", { locale: es }) : 'N/A'}
                      </TableCell>
                      <TableCell className="text-muted-foreground py-4">
                        {apiKey.last_used
                          ? format(new Date(apiKey.last_used), "d 'de' MMMM, yyyy", { locale: es })
                          : 'Nunca'
                        }
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <SemanticIcon icon={BarChart3} size="xs" context="muted" />
                          <span className="text-xs">--</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          {apiKey.is_active && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(apiKey)}
                              className="h-8 w-8 p-0 hover:bg-muted/50"
                              title="Editar nombre"
                            >
                              <SemanticIcon icon={Edit} size="sm" context="muted" />
                            </Button>
                          )}
                          {apiKey.is_active && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                                  title="Revocar API Key"
                                >
                                  <SemanticIcon icon={Trash2} size="sm" context="error" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Esta acción no se puede deshacer. Esto revocará permanentemente la API Key
                                    "{apiKey.name || `API Key ${apiKey.id}`}" y cualquier aplicación que la use dejará de funcionar.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleRevokeApiKey(apiKey.id, apiKey.name || undefined)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    disabled={isRevoking}
                                  >
                                    {isRevoking ? 'Revocando...' : 'Revocar API Key'}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </TableCell>
                    </DenseTableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        {totalKeys === 0 ? (
                          <>
                            <SemanticIcon icon={AlertCircle} size="2xl" context="muted" />
                            <p>No tienes API Keys creadas aún</p>
                            <p className="text-sm">Crea tu primera API Key para comenzar a usar la API</p>
                          </>
                        ) : (
                          <>
                            <SemanticIcon icon={Search} size="2xl" context="muted" />
                            <p>No se encontraron API Keys con los filtros aplicados</p>
                            <Button variant="outline" size="sm" onClick={clearFilters}>
                              Limpiar filtros
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </DenseCard>

      {/* Enhanced Information Section with Multi-API Key Benefits */}
      <Alert variant="info">
        <SemanticIcon icon={Info} size="sm" context="info" />
        <AlertTitle>Sistema Multi-API Key: Gestión Profesional</AlertTitle>
        <AlertDescription>
          <div className="space-y-4 text-sm mt-3">
            <p>
              Rayuela soporta múltiples API Keys para ofrecerte máxima flexibilidad y seguridad. 
              Cada clave funciona de forma independiente y las claves completas <strong>solo se muestran una vez</strong> al crearlas.
            </p>
            
            <div className="grid gap-3 mt-4">
              <div className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                <SemanticIcon icon={Shield} size="md" context="success" className="mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-foreground">Seguridad Avanzada</p>
                  <p className="text-muted-foreground">Revoca claves específicas sin afectar otras integraciones. Cada clave actúa independientemente.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                <SemanticIcon icon={Target} size="md" context="info" className="mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-foreground">Organización por Entorno</p>
                  <p className="text-muted-foreground">Asigna nombres descriptivos: "Producción", "Desarrollo", "Testing". Identifica fácilmente el propósito de cada clave.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                <SemanticIcon icon={Users} size="md" context="primary" className="mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-foreground">Gestión por Equipos</p>
                  <p className="text-muted-foreground">Crea claves separadas para diferentes equipos o aplicaciones. Control granular sobre el acceso.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                <SemanticIcon icon={BarChart3} size="md" context="metric" className="mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-foreground">Métricas Futuras</p>
                  <p className="text-muted-foreground">Próximamente: análisis de uso individual por API Key, incluyendo requests, errores y patrones de consumo.</p>
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-info-light rounded-lg border border-info/20">
              <p className="text-sm text-info">
                <strong>Tip profesional:</strong> Mantén claves separadas para diferentes entornos. 
                Si comprometes una clave en desarrollo, tu producción seguirá segura.
              </p>
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Modal para mostrar la nueva API Key */}
      {showNewKeyModal && newApiKey && (
        <InitialApiKeyModal
          apiKey={newApiKey}
          onClose={handleModalClose}
        />
      )}

      {/* Dialog para editar API Key */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar API Key</DialogTitle>
            <DialogDescription>
              Actualiza el nombre descriptivo de tu API Key. La clave en sí no se puede modificar.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editKeyName">Nombre de la API Key</Label>
              <Input
                id="editKeyName"
                placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                value={editKeyName}
                onChange={(e) => setEditKeyName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEditApiKey} disabled={isUpdating}>
              {isUpdating ? 'Actualizando...' : 'Actualizar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DensePageLayout>
  );
}

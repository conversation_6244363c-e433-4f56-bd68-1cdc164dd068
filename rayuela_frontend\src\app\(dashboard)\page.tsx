// Ruta: src/app/(dashboard)/page.tsx
"use client";

import { useState, useEffect } from 'react';
import { useAuth, useAccountInfo, useUsageSummary } from '@/lib/hooks';
import { getMyAccount } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { KeyIcon, BookOpenIcon, XIcon, BarChart3Icon, CreditCardIcon } from 'lucide-react';
import Link from 'next/link';

import { ApiStatus } from '@/components/dashboard/ApiStatus';
// PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
import GettingStartedChecklist from '@/components/dashboard/GettingStartedChecklist';
import { SandboxResetButton } from '@/components/dashboard/SandboxResetButton';
import { formatBytes, formatNumber, formatDate } from '@/lib/utils/format';



export default function DashboardPage() {
  const { token, apiKey } = useAuth();
  const [isNewUser, setIsNewUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [dismissedOnboarding, setDismissedOnboarding] = useState(false);

  useEffect(() => {
    // Verificar si es un nuevo usuario basado en la fecha de creación de la API Key
    const checkIfNewUser = async () => {
      if (!token || !apiKey) {
        setIsLoading(false);
        return;
      }

      try {
        const accountData = await getMyAccount();

        // Verificar si la cuenta es reciente (menos de 24 horas)
        const accountCreatedAt = new Date(accountData.createdAt);
        const now = new Date();
        const hoursSinceCreation = (now.getTime() - accountCreatedAt.getTime()) / (1000 * 60 * 60);

        setIsNewUser(hoursSinceCreation < 24);

        // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist
      } catch (error) {
        console.error("Error al obtener datos de la cuenta:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkIfNewUser();

    // Verificar si el usuario ya ha descartado el onboarding
    const hasUserDismissedOnboarding = localStorage.getItem('dismissedOnboarding');
    if (hasUserDismissedOnboarding === 'true') {
      setDismissedOnboarding(true);
    }
  }, [token, apiKey]);

  const handleDismissOnboarding = () => {
    setDismissedOnboarding(true);
    localStorage.setItem('dismissedOnboarding', 'true');
  };

  // PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist

  // Obtener datos de la cuenta usando el hook personalizado
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading
  } = useAccountInfo();

  // Obtener datos de uso usando el hook personalizado
  const {
    usageData,
    error: usageError,
    isLoading: isUsageLoading
  } = useUsageSummary();

  // Estado de carga combinado
  const isDataLoading = isAccountLoading || isUsageLoading;
  return (
    <div className="rayuela-fade-in">
      {/* PostModalHighlight eliminado - funcionalidad integrada en GettingStartedChecklist */}

      {/* Checklist de Primeros Pasos */}
      <div className="rayuela-scale-in rayuela-stagger-1">
        <GettingStartedChecklist />
      </div>

      {/* Sandbox Tools para usuarios FREE */}
      <div className="rayuela-scale-in rayuela-stagger-2">
        <SandboxResetButton currentPlan={accountData?.subscription?.plan} />
      </div>

      {/* Banner de Onboarding para nuevos usuarios */}
      {isNewUser && !dismissedOnboarding && !isLoading && (
        <div className="rayuela-slide-up rayuela-stagger-2">
          <Alert className="mb-6" variant="info">
          <div className="flex justify-between items-start">
            <div>
              <AlertTitle className="flex items-center">
                ¡Bienvenido a Rayuela.ai!
              </AlertTitle>
              <AlertDescription className="mt-2">
                Para comenzar a utilizar nuestra API, te recomendamos seguir estos pasos:
                <ol className="list-none mt-3 space-y-3">
                  <li className="flex items-center">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm">1</span>
                    <Link href="/api-keys" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2">
                      Gestiona tus API Keys
                    </Link>
                    <span className="ml-2">para generar o ver tus claves de acceso</span>
                  </li>
                  <li className="flex items-center">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm">2</span>
                    <a
                      href="https://docs.rayuela.ai/quickstart"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80 font-medium underline underline-offset-2"
                    >
                      Consulta nuestra guía de inicio rápido
                    </a>
                    <span className="ml-2">para aprender a integrar la API</span>
                  </li>
                  <li className="flex items-center">
                    <span className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-6 h-6 mr-2 font-bold text-sm">3</span>
                    <Link href="/usage" className="text-primary hover:text-primary/80 font-medium underline underline-offset-2">
                      Monitorea tu uso
                    </Link>
                    <span className="ml-2">para ver estadísticas y límites de tu cuenta</span>
                  </li>
                </ol>
              </AlertDescription>
              <div className="mt-5 flex flex-wrap gap-3">
                <Button asChild size="sm">
                  <Link href="/api-keys" className="flex items-center">
                    <KeyIcon className="mr-2 h-4 w-4" />
                    Gestionar API Keys
                  </Link>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <a
                    href="https://docs.rayuela.ai/quickstart"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center"
                  >
                    <BookOpenIcon className="mr-2 h-4 w-4" />
                    Ver Guía de Inicio
                  </a>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <Link href="/usage" className="flex items-center">
                    <BarChart3Icon className="mr-2 h-4 w-4" />
                    Ver Uso
                  </Link>
                </Button>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground hover:bg-muted hover:text-foreground"
              onClick={handleDismissOnboarding}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </Alert>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Dashboard Overview
          </h1>
          <p className="text-muted-foreground mt-2">
            Welcome to your dashboard. Here you'll find an overview
            of your API usage, billing information, and manage your API keys.
          </p>
        </div>
        <ApiStatus className="hidden md:flex" />
      </div>

      {/* Widgets del Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6">
        {/* Widget de Uso Rápido - Versión simplificada */}
        <Card className="col-span-1 md:col-span-2 lg:col-span-2 border-2 border-blue-50 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-all duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <BarChart3Icon className="h-5 w-5 mr-2 text-info" />
              Resumen de Uso
            </CardTitle>
            <CardDescription>
              Vista rápida de tu consumo actual
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isDataLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : usageError ? (
              <p className="text-red-500 text-sm">Error al cargar datos de uso</p>
            ) : usageData && accountData ? (
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-info-light rounded-lg">
                  <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Llamadas API</div>
                  <div className="text-xl font-bold mt-1 text-blue-800 dark:text-blue-200">
                    {formatNumber(usageData.apiCalls?.used || 0)}
                  </div>
                  <div className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">
                    Uso actual
                  </div>
                </div>
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Almacenamiento</div>
                  <div className="text-xl font-bold mt-1 text-green-800 dark:text-green-200">
                    {formatBytes(usageData.storage?.usedBytes || 0)}
                  </div>
                  <div className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">
                    Uso actual
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">No hay datos de uso disponibles</p>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link href="/usage" className="flex items-center justify-center">
                <BarChart3Icon className="mr-2 h-4 w-4" />
                Ver Panel de Uso Completo
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Widget de API Key Actual */}
        <Card className="border-2 border-orange-50 dark:border-orange-900/30 shadow-sm hover:shadow-md transition-all duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <KeyIcon className="h-5 w-5 mr-2 text-warning" />
              API Key Actual
            </CardTitle>
            <CardDescription>
              Información de tu clave activa
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isDataLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            ) : accountError ? (
              <p className="text-red-500 text-sm">Error al cargar datos de la cuenta</p>
            ) : apiKey ? (
              <div className="space-y-2">
                <div className="text-sm font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded">
                  {apiKey.slice(0, 4)}...{apiKey.slice(-4)}
                </div>
                <div className="text-xs text-muted-foreground">
                  API Key activa
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No hay API Key disponible</p>
            )}
          </CardContent>
        </Card>

        {/* Widget de Estado de Cuenta */}
        <Card className="border-2 border-green-50 dark:border-green-900/30 shadow-sm hover:shadow-md transition-all duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <CreditCardIcon className="h-5 w-5 mr-2 text-success" />
              Estado de Cuenta
            </CardTitle>
            <CardDescription>
              Estado actual de tu suscripción
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isDataLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : accountError ? (
              <p className="text-red-500 text-sm">Error al cargar datos de suscripción</p>
            ) : accountData?.subscription ? (
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    accountData.subscription.isActive ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm font-medium">
                    {accountData.subscription.plan || 'Free'}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {accountData.subscription.isActive ? 'Activa' : 'Inactiva'}
                  {accountData.subscription.expiresAt && (
                    <span> • Expira {formatDate(accountData.subscription.expiresAt)}</span>
                  )}
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium">Plan Free</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  Plan básico activo
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Dashboard Principal */}
      {/* TODO: Import UsageDashboard component */}
      {/* <div className="rayuela-fade-in rayuela-stagger-3">
        <UsageDashboard />
      </div> */}
    </div>
  );
}

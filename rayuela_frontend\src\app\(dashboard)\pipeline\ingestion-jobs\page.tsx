"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Database, 
  ChevronLeft, 
  AlertCircle, 
  RotateCcw,
  Eye,
  Filter,
  Search,
  Upload,
  Download
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useIngestionJobs } from '@/lib/hooks/useIngestionJobs';
import { DataIngestionModal } from '@/components/pipeline/DataIngestionModal';
import { formatBytes, getStatusIcon, getStatusBadge, formatDuration } from '@/lib/utils/format';

// Types for ingestion jobs
interface IngestionJob {
  id: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration?: number;
  error_message?: string;
  file_path?: string;
  records_processed?: {
    users?: number;
    products?: number;
    interactions?: number;
    total?: number;
  };
  file_size?: number;
  task_id?: string;
}

type FilterStatus = 'all' | 'pending' | 'processing' | 'completed' | 'failed';

export default function IngestionJobsPage() {
  const { jobs, isLoading, error, startBatchIngestion } = useIngestionJobs();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<FilterStatus>('all');
  const [selectedJob, setSelectedJob] = useState<IngestionJob | null>(null);

  const filteredJobs = jobs.filter(job => {
    const matchesStatus = statusFilter === 'all' || job.status.toLowerCase() === statusFilter;
    const matchesSearch = searchQuery === '' || 
      job.id.toString().includes(searchQuery) ||
      (job.file_path && job.file_path.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesStatus && matchesSearch;
  });



  // Use formatBytes for file size formatting
  const formatFileSize = (bytes: number) => formatBytes(bytes);

  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
  };

  const canRetryJob = (job: IngestionJob) => {
    return job.status === 'FAILED';
  };

  const handleRetryJob = (jobId: number) => {
    // TODO: Implement retry logic
    console.log('Retrying job:', jobId);
  };

  const handleDownloadFile = (filePath: string) => {
    // TODO: Implement file download logic
    console.log('Downloading file:', filePath);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        <div className="bg-card/50 border border-border/50 rounded-lg p-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section */}
      <div className="bg-card/50 border border-border/50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <Link href="/pipeline" className="text-muted-foreground hover:text-foreground">
                <ChevronLeft className="h-5 w-5" />
              </Link>
              <h1 className="text-3xl font-bold flex items-center gap-3">
                <Database className="h-8 w-8 text-green-500" />
                Historial de Ingesta de Datos
              </h1>
            </div>
            <p className="text-muted-foreground">
              Seguimiento completo de todos tus procesos de carga de datos
            </p>
            <div className="flex gap-4 mt-4 text-sm text-muted-foreground">
              <span>Total: {jobs.length}</span>
              <span>Completados: {jobs.filter(j => j.status === 'COMPLETED').length}</span>
              <span>En proceso: {jobs.filter(j => j.status === 'PROCESSING').length}</span>
              <span>Fallidos: {jobs.filter(j => j.status === 'FAILED').length}</span>
            </div>
          </div>
          <DataIngestionModal 
            onIngestionStart={startBatchIngestion}
            trigger={
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Nueva Ingesta
              </Button>
            }
          />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters Section */}
      <Card>
        <CardHeader className="border-b border-border/20 bg-muted/20">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Buscar por ID o archivo..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Label htmlFor="statusFilter" className="text-sm whitespace-nowrap">Estado:</Label>
              <Select value={statusFilter} onValueChange={(value: FilterStatus) => setStatusFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendiente</SelectItem>
                  <SelectItem value="processing">Procesando</SelectItem>
                  <SelectItem value="completed">Completado</SelectItem>
                  <SelectItem value="failed">Fallido</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              Limpiar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Jobs Table */}
      <Card>
        <CardHeader className="border-b border-border/20 bg-muted/20">
          <CardTitle>Trabajos de Ingesta</CardTitle>
          <CardDescription>
            Lista completa de procesos de carga de datos con detalles y estadísticas
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-hidden">
            <Table>
              <TableHeader className="bg-muted/10">
                <TableRow className="border-b border-border/30">
                  <TableHead className="font-semibold">Job ID</TableHead>
                  <TableHead className="font-semibold">Estado</TableHead>
                  <TableHead className="font-semibold">Fecha Inicio</TableHead>
                  <TableHead className="font-semibold">Duración</TableHead>
                  <TableHead className="font-semibold">Registros Procesados</TableHead>
                  <TableHead className="font-semibold">Archivo</TableHead>
                  <TableHead className="text-right font-semibold">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredJobs.length > 0 ? (
                  filteredJobs.map((job, index) => (
                    <TableRow key={job.id} className={index % 2 === 0 ? "bg-muted/20" : ""}>
                      <TableCell className="font-medium py-4">#{job.id}</TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          {getStatusBadge(job.status)}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="text-sm">
                          <div>{format(new Date(job.created_at), 'dd/MM/yyyy', { locale: es })}</div>
                          <div className="text-muted-foreground">{format(new Date(job.created_at), 'HH:mm', { locale: es })}</div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        {job.duration ? (
                          <span className="text-sm font-medium">{formatDuration(job.duration)}</span>
                        ) : job.status === 'PROCESSING' ? (
                          <span className="text-sm text-muted-foreground">En curso</span>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        {job.records_processed ? (
                          <div className="text-sm">
                            <div className="font-medium">
                              Total: {job.records_processed.total?.toLocaleString() || '—'}
                            </div>
                            <div className="text-muted-foreground text-xs">
                              {job.records_processed.users && `${job.records_processed.users.toLocaleString()} usuarios`}
                              {job.records_processed.products && `, ${job.records_processed.products.toLocaleString()} productos`}
                              {job.records_processed.interactions && `, ${job.records_processed.interactions.toLocaleString()} interacciones`}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        {job.file_path ? (
                          <div className="text-sm">
                            <div className="font-medium truncate max-w-32" title={job.file_path}>
                              {job.file_path.split('/').pop()}
                            </div>
                            {job.file_size && (
                              <div className="text-muted-foreground text-xs">
                                {formatFileSize(job.file_size)}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedJob(job)}
                                className="h-8 w-8 p-0 hover:bg-muted/50"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Detalles del Job #{job.id}</DialogTitle>
                                <DialogDescription>
                                  Información completa del trabajo de ingesta de datos
                                </DialogDescription>
                              </DialogHeader>
                              {selectedJob && (
                                <div className="space-y-4 max-h-96 overflow-y-auto">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium">Estado</Label>
                                      <div className="mt-1">{getStatusBadge(selectedJob.status)}</div>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Duración</Label>
                                      <p className="text-sm">{selectedJob.duration ? formatDuration(selectedJob.duration) : 'En curso'}</p>
                                    </div>
                                  </div>
                                  
                                  {selectedJob.file_path && (
                                    <div>
                                      <Label className="text-sm font-medium">Archivo</Label>
                                      <div className="mt-1 flex items-center gap-2">
                                        <code className="text-xs bg-muted p-2 rounded flex-1">
                                          {selectedJob.file_path}
                                        </code>
                                        <Button 
                                          size="sm" 
                                          variant="outline"
                                          onClick={() => handleDownloadFile(selectedJob.file_path!)}
                                          disabled
                                        >
                                          <Download className="h-4 w-4" />
                                        </Button>
                                      </div>
                                      {selectedJob.file_size && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                          Tamaño: {formatFileSize(selectedJob.file_size)}
                                        </p>
                                      )}
                                    </div>
                                  )}
                                  
                                  {selectedJob.records_processed && (
                                    <div>
                                      <Label className="text-sm font-medium">Registros Procesados</Label>
                                      <div className="grid grid-cols-4 gap-2 mt-2">
                                        {selectedJob.records_processed.users && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Usuarios</div>
                                            <div className="text-sm font-bold">{selectedJob.records_processed.users.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.records_processed.products && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Productos</div>
                                            <div className="text-sm font-bold">{selectedJob.records_processed.products.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.records_processed.interactions && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Interacciones</div>
                                            <div className="text-sm font-bold">{selectedJob.records_processed.interactions.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.records_processed.total && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Total</div>
                                            <div className="text-sm font-bold">{selectedJob.records_processed.total.toLocaleString()}</div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  
                                  {selectedJob.error_message && (
                                    <div>
                                      <Label className="text-sm font-medium text-destructive">Error</Label>
                                      <Alert variant="destructive" className="mt-1">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription className="text-sm">
                                          {selectedJob.error_message}
                                        </AlertDescription>
                                      </Alert>
                                    </div>
                                  )}
                                  
                                  {selectedJob.task_id && (
                                    <div>
                                      <Label className="text-sm font-medium">Task ID</Label>
                                      <code className="text-xs bg-muted p-1 rounded block mt-1">
                                        {selectedJob.task_id}
                                      </code>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          
                          {canRetryJob(job) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRetryJob(job.id)}
                              className="h-8 w-8 p-0 hover:bg-muted/50"
                              disabled
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        {jobs.length === 0 ? (
                          <>
                            <Database className="h-8 w-8" />
                            <p>No hay trabajos de ingesta aún</p>
                            <p className="text-sm">Los trabajos aparecerán aquí cuando subas datos</p>
                          </>
                        ) : (
                          <>
                            <Search className="h-8 w-8" />
                            <p>No se encontraron trabajos con los filtros aplicados</p>
                            <Button variant="outline" size="sm" onClick={clearFilters}>
                              Limpiar filtros
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información sobre ingesta de datos</AlertTitle>
        <AlertDescription>
          <div className="space-y-2 text-sm mt-2">
            <p>
              Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar 
              usuarios, productos e interacciones en el sistema.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li><strong>Formatos soportados:</strong> CSV, JSON</li>
              <li><strong>Tipos de datos:</strong> Usuarios, Productos, Interacciones</li>
              <li><strong>Validación:</strong> Se valida formato y campos obligatorios</li>
              <li><strong>Procesamiento:</strong> Los datos se procesan de forma asíncrona</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
} 
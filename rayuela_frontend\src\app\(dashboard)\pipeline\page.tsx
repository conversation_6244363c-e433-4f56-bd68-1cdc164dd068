"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  Database, 
  Brain, 
  Play, 
  Upload, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  XCircle,

  History,
  Settings,
  Activity,
  Cpu,
  Server
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { useAuth } from '@/lib/auth';
import { getStatusBadge } from '@/lib/utils/format';

// Mock data types - replace with actual API types when available
interface TrainingJob {
  id: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  progress?: number;
  model_name?: string;
  metrics?: Record<string, number>;
}

interface IngestionJob {
  id: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_at: string;
  records_processed?: {
    users?: number;
    products?: number;
    interactions?: number;
  };
  file_path?: string;
}

interface ModelInfo {
  id: number;
  name: string;
  version: string;
  training_date: string;
  status: 'ACTIVE' | 'INACTIVE';
  performance_metrics?: Record<string, number>;
}

// Mock functions - replace with actual API calls
const fetchLastTrainingJob = async (): Promise<TrainingJob | null> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  return {
    id: 123,
    status: 'PROCESSING',
    created_at: new Date().toISOString(),
    started_at: new Date().toISOString(),
    progress: 75,
    model_name: 'Recommendation Model v2.1',
  };
};

const fetchLastIngestionJob = async (): Promise<IngestionJob | null> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  return {
    id: 456,
    status: 'COMPLETED',
    created_at: new Date(Date.now() - 3600000).toISOString(),
    records_processed: {
      users: 1250,
      products: 5430,
      interactions: 15670
    }
  };
};

const fetchActiveModels = async (): Promise<ModelInfo[]> => {
  await new Promise(resolve => setTimeout(resolve, 600));
  return [
    {
      id: 1,
      name: 'Recommendation Model',
      version: 'v2.0',
      training_date: new Date(Date.now() - 86400000 * 7).toISOString(),
      status: 'ACTIVE',
      performance_metrics: {
        accuracy: 0.85,
        precision: 0.82
      }
    },
    {
      id: 2,
      name: 'Trending Model',
      version: 'v1.5',
      training_date: new Date(Date.now() - 86400000 * 14).toISOString(),
      status: 'ACTIVE'
    }
  ];
};

export default function PipelinePage() {
  const [lastTraining, setLastTraining] = useState<TrainingJob | null>(null);
  const [lastIngestion, setLastIngestion] = useState<IngestionJob | null>(null);
  const [activeModels, setActiveModels] = useState<ModelInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPipelineData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const [training, ingestion, models] = await Promise.all([
          fetchLastTrainingJob(),
          fetchLastIngestionJob(),
          fetchActiveModels()
        ]);

        setLastTraining(training);
        setLastIngestion(ingestion);
        setActiveModels(models);
      } catch (err) {
        setError('Error loading pipeline data');
        console.error('Error loading pipeline data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadPipelineData();
  }, []);

  /* Función para obtener iconos de estado (no usada actualmente)
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-success" />;
      case 'PROCESSING':
        return <Activity className="h-4 w-4 text-info animate-pulse" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-warning" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-destructive" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };
  */

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        <div className="bg-card/50 border border-border/50 rounded-lg p-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section */}
      <div className="bg-card/50 border border-border/50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Cpu className="h-8 w-8 text-primary" />
              Pipeline & Modelos
            </h1>
            <p className="text-muted-foreground mt-2">
              Monitorea tus pipelines de datos y modelos de recomendación
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        
        {/* Last Training Job Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="h-5 w-5 text-purple-500" />
              Último Entrenamiento
            </CardTitle>
            <CardDescription>Estado del entrenamiento más reciente</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {lastTraining ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Job #{lastTraining.id}</span>
                  {getStatusBadge(lastTraining.status)}
                </div>
                
                {lastTraining.model_name && (
                  <div className="text-sm text-muted-foreground">
                    {lastTraining.model_name}
                  </div>
                )}

                {lastTraining.status === 'PROCESSING' && lastTraining.progress && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progreso</span>
                      <span>{lastTraining.progress}%</span>
                    </div>
                    <Progress value={lastTraining.progress} className="h-2" />
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  Inicio: {format(new Date(lastTraining.created_at), "d 'de' MMM, HH:mm", { locale: es })}
                </div>

                {lastTraining.status === 'FAILED' && lastTraining.error_message && (
                  <Alert variant="destructive" className="mt-3">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {lastTraining.error_message}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay entrenamientos recientes</p>
              </div>
            )}
            
            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/pipeline/training-jobs">
                <History className="h-4 w-4 mr-2" />
                Ver Historial
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Last Data Ingestion Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Database className="h-5 w-5 text-green-500" />
              Ingesta de Datos Reciente
            </CardTitle>
            <CardDescription>Último proceso de carga de datos</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {lastIngestion ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Job #{lastIngestion.id}</span>
                  {getStatusBadge(lastIngestion.status)}
                </div>

                {lastIngestion.records_processed && (
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">Registros procesados:</div>
                    <div className="grid grid-cols-1 gap-1 text-sm">
                      {lastIngestion.records_processed.users && (
                        <div className="flex justify-between">
                          <span>Usuarios:</span>
                          <span className="font-medium">{lastIngestion.records_processed.users.toLocaleString()}</span>
                        </div>
                      )}
                      {lastIngestion.records_processed.products && (
                        <div className="flex justify-between">
                          <span>Productos:</span>
                          <span className="font-medium">{lastIngestion.records_processed.products.toLocaleString()}</span>
                        </div>
                      )}
                      {lastIngestion.records_processed.interactions && (
                        <div className="flex justify-between">
                          <span>Interacciones:</span>
                          <span className="font-medium">{lastIngestion.records_processed.interactions.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  {format(new Date(lastIngestion.created_at), "d 'de' MMM, HH:mm", { locale: es })}
                </div>
              </>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay ingestas recientes</p>
              </div>
            )}

            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/pipeline/ingestion-jobs">
                <History className="h-4 w-4 mr-2" />
                Ver Historial
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Active Models Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Server className="h-5 w-5 text-blue-500" />
              Modelos Activos
            </CardTitle>
            <CardDescription>Modelos actualmente en producción</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeModels.length > 0 ? (
              <div className="space-y-3">
                {activeModels.slice(0, 2).map((model) => (
                  <div key={model.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{model.name}</span>
                      <Badge variant="success" className="text-xs">v{model.version}</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Entrenado: {format(new Date(model.training_date), "d MMM yyyy", { locale: es })}
                    </div>
                    {model.performance_metrics && (
                      <div className="flex gap-2 text-xs">
                        {Object.entries(model.performance_metrics).map(([key, value]) => (
                          <span key={key} className="bg-muted px-2 py-1 rounded">
                            {key}: {(value * 100).toFixed(1)}%
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {activeModels.length > 2 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{activeModels.length - 2} modelos más
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Server className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay modelos activos</p>
              </div>
            )}

            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/models">
                <Settings className="h-4 w-4 mr-2" />
                Gestionar Modelos
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Quick Actions Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Zap className="h-5 w-5 text-yellow-500" />
              Acciones Rápidas
            </CardTitle>
            <CardDescription>Operaciones comunes del pipeline</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button size="sm" className="w-full justify-start" disabled>
              <Play className="h-4 w-4 mr-2" />
              Iniciar Nuevo Entrenamiento
            </Button>
            
            <Button variant="outline" size="sm" className="w-full justify-start" disabled>
              <Upload className="h-4 w-4 mr-2" />
              Subir Datos
            </Button>
            
            <Button variant="outline" size="sm" className="w-full justify-start" disabled>
              <RefreshCw className="h-4 w-4 mr-2" />
              Invalidar Caché
            </Button>

            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Las acciones del pipeline estarán disponibles próximamente. 
                Por ahora puedes monitorear el estado actual.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Information Section */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información sobre el Pipeline</AlertTitle>
        <AlertDescription>
          <div className="space-y-2 text-sm mt-2">
            <p>
              Esta sección te permite monitorear el estado de tus procesos de machine learning:
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li><strong>Entrenamientos:</strong> Seguimiento de jobs de entrenamiento de modelos</li>
              <li><strong>Ingesta:</strong> Monitoreo de procesos de carga de datos</li>
              <li><strong>Modelos:</strong> Estado de modelos activos en producción</li>
              <li><strong>Métricas:</strong> Rendimiento y estado de salud del pipeline</li>
            </ul>
            <p className="mt-2 text-muted-foreground">
              <strong>Próximamente:</strong> Interfaces para iniciar entrenamientos, 
              gestionar datos y configurar parámetros de modelo.
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
} 
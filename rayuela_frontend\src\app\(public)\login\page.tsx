// src/app/(public)/login/page.tsx
"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import LoginForm from '@/components/auth/LoginForm';

export default function LoginPage() {

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Iniciar Sesión</CardTitle>
          <CardDescription>Accede a tu panel de Rayuela.</CardDescription>
        </CardHeader>
        <CardContent>
          <LoginForm showHeader={false} />
        </CardContent>
      </Card>
    </div>
  );
}

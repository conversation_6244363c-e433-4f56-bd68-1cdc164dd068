"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/lib/auth";
import { MailIcon, AlertCircleIcon } from 'lucide-react';

// Esquema de validación
const loginSchema = z.object({
  email: z.string().email({ message: "Email inválido" }),
  password: z.string().min(8, { message: "Contraseña debe tener al menos 8 caracteres" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

interface LoginFormProps {
  showHeader?: boolean;
  onSuccess?: () => void;
  initialEmail?: string;
  initialPassword?: string;
}

export default function LoginForm({ 
  showHeader = true, 
  onSuccess,
  initialEmail = "",
  initialPassword = ""
}: LoginFormProps) {
  const { login, emailVerificationError, requestNewVerificationEmail } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);

  // Inicializar el formulario
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: initialEmail,
      password: initialPassword,
    },
  });

  // Actualizar los valores del formulario si cambian las props iniciales o emailVerificationError
  useEffect(() => {
    if (emailVerificationError) {
      form.setValue('email', emailVerificationError.email);
      form.setValue('password', emailVerificationError.password);
    } else if (initialEmail || initialPassword) {
      form.setValue('email', initialEmail);
      form.setValue('password', initialPassword);
    }
  }, [emailVerificationError, initialEmail, initialPassword, form]);

  // Función para manejar el reenvío del email de verificación
  const handleResendVerificationEmail = async () => {
    setIsResendingEmail(true);
    try {
      const success = await requestNewVerificationEmail();
      if (success) {
        toast.success("Email de verificación enviado. Por favor, revisa tu bandeja de entrada.");
      }
    } catch (error: unknown) {
      console.error("Error al reenviar email de verificación:", error);
      toast.error((error as Error).message || "Error al reenviar email de verificación.");
    } finally {
      setIsResendingEmail(false);
    }
  };

  // Manejar envío del formulario
  const onSubmit = async (values: LoginFormValues) => {
    try {
      setIsLoading(true);
      const success = await login(values.email, values.password);

      if (success) {
        if (onSuccess) {
          onSuccess();
        } else {
          // Redirigir al dashboard
          router.push("/dashboard");
          toast.success("¡Inicio de sesión exitoso!");
        }
      }
    } catch (error: unknown) {
      console.error("Login failed:", error);
      toast.error((error as Error).message || "Error al iniciar sesión. Verifica tus credenciales.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {showHeader && (
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Iniciar Sesión</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Ingresa tus credenciales para acceder a tu cuenta
          </p>
        </div>
      )}

      {/* Alerta de verificación de email */}
      {emailVerificationError && (
        <Alert variant="warning" className="mb-6">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>Verificación de email requerida</AlertTitle>
          <AlertDescription>
            {emailVerificationError.message}
            <Button
              variant="outline"
              size="sm"
              className="mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
              onClick={handleResendVerificationEmail}
              disabled={isResendingEmail}
            >
              <MailIcon className="mr-2 h-4 w-4" />
              {isResendingEmail ? 'Enviando...' : 'Reenviar email de verificación'}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    type="email"
                    autoComplete="email"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contraseña</FormLabel>
                <FormControl>
                  <Input
                    placeholder="••••••••"
                    type="password"
                    autoComplete="current-password"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isLoading || isResendingEmail}>
            {isLoading ? "Iniciando sesión..." : "Iniciar Sesión"}
          </Button>
        </form>
      </Form>

      <div className="text-center text-sm">
        ¿No tienes una cuenta?{" "}
        <Link
          href="/register"
          className="underline underline-offset-4 hover:text-primary"
        >
          Registrarse
        </Link>
      </div>
    </div>
  );
} 

"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BillingButton } from '@/components/dashboard/BillingButton';
import { PlanInfo } from '@/lib/api';
import {
  getPlanDisplayName,
  getPlanActionType,
  getPlanButtonVariant,
  getFormattedPlanFeatures,
  getPlanPriceDisplay
} from '@/lib/utils/billing';
import { CheckIcon, StarIcon, PhoneIcon } from 'lucide-react';
import { SemanticIcon, IconWithText } from '@/components/ui/icon';
import { cn } from '@/lib/utils';

interface PlanCardProps {
  planId: string;
  plan: PlanInfo;
  currentPlan?: string | null;
  isPopular?: boolean;
  className?: string;
}

export function PlanCard({ planId, plan, currentPlan, isPopular, className }: PlanCardProps) {
  const actionType = getPlanActionType(planId, currentPlan);
  const buttonVariant = getPlanButtonVariant(planId, currentPlan, plan.recommended);
  const features = getFormattedPlanFeatures(plan);
  const priceDisplay = getPlanPriceDisplay(plan);
  const isCurrentPlan = planId === currentPlan;
  const isEnterprise = planId === 'ENTERPRISE';

  // Determine if this plan should be highlighted
  const isHighlighted = plan.recommended || isPopular;

  return (
    <Card
      className={cn(
        "relative transition-all duration-200 hover:shadow-lg",
        isHighlighted && "border-blue-500 shadow-md",
        isCurrentPlan && "ring-2 ring-green-500 ring-offset-2",
        className
      )}
    >
      {/* Popular/Recommended Badge */}
      {isHighlighted && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge
            variant="default"
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1"
          >
            <SemanticIcon icon={StarIcon} size="xs" context="primary" className="mr-1" />
            {plan.recommended ? 'Recomendado' : 'Popular'}
          </Badge>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 right-4">
          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Plan Actual
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <CardTitle className="text-xl font-bold">
          {getPlanDisplayName(planId)}
        </CardTitle>
        <CardDescription className="text-sm">
          {plan.description}
        </CardDescription>

        {/* Price */}
        <div className="pt-4">
          <div className="text-3xl font-bold">
            {priceDisplay}
          </div>
          {priceDisplay !== 'Gratis' && priceDisplay !== 'Contactar' && (
            <div className="text-sm text-muted-foreground">por mes</div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Features List */}
        <div className="space-y-3">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start space-x-2">
              <SemanticIcon icon={CheckIcon} size="sm" context="success" className="mt-0.5 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>

        {/* Additional Plan Details */}
        {plan.limits && (
          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              <div>
                <div className="font-medium">Límite de velocidad</div>
                <div>{String(plan.limits.max_requests_per_minute || 'N/A')}/min</div>
              </div>
              <div>
                <div className="font-medium">Concurrencia</div>
                <div>{String(plan.limits.max_concurrent_requests || 'N/A')} req</div>
              </div>
              {plan.limits.training_frequency ? (
                <div className="col-span-2">
                  <div className="font-medium">Entrenamiento</div>
                  <div>
                    {String(plan.limits.training_frequency) === 'manual'
                      ? 'Manual'
                      : `Cada ${String(plan.limits.training_frequency)}`
                    }
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-6">
        {isCurrentPlan ? (
          <Button
            variant="secondary"
            className="w-full"
            disabled
          >
            Plan Actual
          </Button>
        ) : isEnterprise ? (
          <Button
            variant={buttonVariant}
            className="w-full"
            onClick={() => window.location.href = '/contact-sales'}
          >
            <IconWithText icon={PhoneIcon} size="sm" context="primary" spacing="normal">
              Contactar con Ventas
            </IconWithText>
          </Button>
        ) : (
          <BillingButton
            priceId={plan.stripe_price_id || ''}
            planName={getPlanDisplayName(planId)}
            actionType={actionType === 'current' ? 'subscribe' : actionType}
            variant={buttonVariant}
            className="w-full"
          />
        )}
      </CardFooter>
    </Card>
  );
}

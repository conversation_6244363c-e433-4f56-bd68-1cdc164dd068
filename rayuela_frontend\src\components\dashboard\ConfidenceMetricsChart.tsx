"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200 } from '@/lib/generated/rayuelaAPI';

interface ConfidenceMetricsChartProps {
  data?: GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200;
  isLoading?: boolean;
  error?: Error | null;
  title?: string;
  description?: string;
}

interface ConfidenceDataPoint {
  name: string;
  avg: number;
  low: number;
  medium: number;
  high: number;
}

export default function ConfidenceMetricsChart({
  data,
  isLoading = false,
  error = null,
  title = "Métricas de Confianza",
  description = "Análisis de confianza en las recomendaciones"
}: ConfidenceMetricsChartProps) {
  
  // Transform API data to chart-friendly format
  const getChartData = (): ConfidenceDataPoint[] => {
    if (!data || typeof data !== 'object') return [];
    
    const apiData = data as Record<string, unknown>;
    const confidenceDistribution = apiData.confidenceDistribution as Record<string, Record<string, number>> | undefined;
    
    if (!confidenceDistribution) return [];
    
    const models = ['collaborative', 'content', 'hybrid'];
    
    return models.map(model => {
      const distribution = confidenceDistribution[model] || {};
      return {
        name: model.charAt(0).toUpperCase() + model.slice(1),
        avg: distribution.avg || 0,
        low: distribution.low || 0,
        medium: distribution.medium || 0,
        high: distribution.high || 0
      };
    }).filter(item => item.avg > 0);
  };

  const chartData = getChartData();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-muted-foreground">Cargando métricas de confianza...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-destructive">Error al cargar métricas: {error.message}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-muted-foreground">
              No hay datos de confianza disponibles
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {chartData.map((item) => (
            <div key={item.name} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex flex-col">
                <span className="font-medium">{item.name}</span>
                <div className="flex space-x-4 text-sm text-muted-foreground mt-1">
                  <span>Bajo: {(item.low * 100).toFixed(1)}%</span>
                  <span>Medio: {(item.medium * 100).toFixed(1)}%</span>
                  <span>Alto: {(item.high * 100).toFixed(1)}%</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">{(item.avg * 100).toFixed(1)}%</div>
                <div className="text-sm text-muted-foreground">Promedio</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 
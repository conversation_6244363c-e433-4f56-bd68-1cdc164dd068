"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format, subDays, isAfter } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';

export type DateRange = {
  from: Date;
  to: Date;
};

interface DateRangeSelectorProps {
  onChange: (range: DateRange) => void;
  className?: string;
}

export function DateRangeSelector({ onChange, className }: DateRangeSelectorProps) {
  const today = new Date();
  
  // Predefined ranges
  const ranges = [
    { label: 'Últimos 7 días', days: 7 },
    { label: 'Últimos 14 días', days: 14 },
    { label: 'Últimos 30 días', days: 30 },
  ];

  const [selectedRange, setSelectedRange] = useState<number>(30); // Default to 30 days
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(today, 30),
    to: today
  });

  // Handle predefined range selection
  const handleRangeClick = (days: number) => {
    const newRange = {
      from: subDays(today, days),
      to: today
    };
    setDateRange(newRange);
    setSelectedRange(days);
    onChange(newRange);
  };

  // Handle custom date selection
  const handleDateSelect = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      const selectedRange = { from: range.from, to: range.to };
      setDateRange(selectedRange);
      setSelectedRange(0); // Custom range
      onChange(selectedRange);
      setIsCalendarOpen(false);
    }
  };

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {ranges.map((range) => (
        <Button
          key={range.days}
          variant={selectedRange === range.days ? "default" : "outline"}
          size="sm"
          onClick={() => handleRangeClick(range.days)}
          className={selectedRange === range.days ? "bg-blue-600 hover:bg-blue-700" : ""}
        >
          {range.label}
        </Button>
      ))}
      
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={selectedRange === 0 ? "default" : "outline"}
            size="sm"
            className={cn(
              "flex items-center gap-1",
              selectedRange === 0 ? "bg-blue-600 hover:bg-blue-700" : ""
            )}
          >
            <CalendarIcon className="h-4 w-4" />
            {selectedRange === 0 
              ? `${format(dateRange.from, 'dd/MM/yy')} - ${format(dateRange.to, 'dd/MM/yy')}` 
              : "Personalizado"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange.from}
            selected={dateRange}
            onSelect={handleDateSelect}
            numberOfMonths={2}
            locale={es}
            disabled={(date) => isAfter(date, today)}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

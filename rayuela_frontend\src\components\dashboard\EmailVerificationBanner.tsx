"use client";

import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useAuth } from '@/lib/auth';
import { toast } from 'sonner';
import { MailIcon, XIcon } from 'lucide-react';

interface EmailVerificationBannerProps {
  onClose?: () => void;
}

export default function EmailVerificationBanner({ onClose }: EmailVerificationBannerProps) {
  const { requestNewVerificationEmail } = useAuth();
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  if (isDismissed) {
    return null;
  }

  const handleResendVerificationEmail = async () => {
    setIsResendingEmail(true);
    try {
      const success = await requestNewVerificationEmail();
      if (success) {
        toast.success("Email de verificación enviado. Por favor, revisa tu bandeja de entrada.");
      }
          } catch (error: unknown) {
        console.error("Error al reenviar email de verificación:", error);
        const errorMessage = error instanceof Error ? error.message : "Error al reenviar email de verificación.";
        toast.error(errorMessage);
      } finally {
      setIsResendingEmail(false);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    if (onClose) {
      onClose();
    }
  };

  return (
          <Alert variant="warning" className="mb-6 relative">
              <AlertTitle className="flex items-center">
        Verificación de email pendiente
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto absolute top-2 right-2 text-amber-500 hover:text-amber-700 hover:bg-transparent"
          onClick={handleDismiss}
        >
          <XIcon className="h-4 w-4" />
          <span className="sr-only">Cerrar</span>
        </Button>
      </AlertTitle>
              <AlertDescription>
        <p>Tu email aún no ha sido verificado. Por favor, verifica tu email para acceder a todas las funcionalidades.</p>
        <Button 
          variant="outline" 
          size="sm" 
          className="mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
          onClick={handleResendVerificationEmail}
          disabled={isResendingEmail}
        >
          <MailIcon className="mr-2 h-4 w-4" />
          {isResendingEmail ? 'Enviando...' : 'Reenviar email de verificación'}
        </Button>
      </AlertDescription>
    </Alert>
  );
}

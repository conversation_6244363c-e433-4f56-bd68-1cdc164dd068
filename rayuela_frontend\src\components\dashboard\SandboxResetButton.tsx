'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RefreshCw, AlertTriangle, TestTube, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/lib/auth';

interface SandboxResetButtonProps {
  currentPlan?: string | null;
}

export function SandboxResetButton({ currentPlan }: SandboxResetButtonProps) {
  const [isResetting, setIsResetting] = useState(false);
  const { token } = useAuth();

  // Only show for FREE plan users
  if (currentPlan !== 'FREE') return null;

  const handleResetSandbox = async () => {
    setIsResetting(true);

    try {
      // Call the reset API endpoint
      const response = await fetch('/api/v1/sandbox/reset', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast.success('🧪 Datos de sandbox limpiados exitosamente');
        // Optionally refresh the page or update local state
        window.location.reload();
      } else {
        throw new Error('Error al limpiar el sandbox');
      }
    } catch (error) {
      console.error('Error resetting sandbox:', error);
      toast.error('Error al limpiar los datos de sandbox');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Card className="border-orange-200 dark:border-orange-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
          <TestTube className="h-5 w-5" />
          Developer Sandbox Tools
        </CardTitle>
        <CardDescription>
          Herramientas especiales para desarrollo y experimentación
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Entorno de Desarrollo</AlertTitle>
          <AlertDescription>
            Estás en el plan <strong>Developer Sandbox</strong>. Puedes experimentar libremente 
            sin preocuparte por costos o afectar datos de producción.
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <h4 className="font-medium">Reset de Datos Experimentales</h4>
          <p className="text-sm text-muted-foreground">
            Limpia todos tus datos de prueba (productos, usuarios, interacciones, modelos) 
            para empezar fresh con nuevos experimentos.
          </p>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-700 dark:text-orange-300 dark:hover:bg-orange-900/20"
                disabled={isResetting}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {isResetting ? 'Limpiando...' : 'Limpiar Datos de Sandbox'}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Limpiar todos los datos de sandbox?</AlertDialogTitle>
                <AlertDialogDescription className="space-y-2">
                  <p>Esta acción eliminará permanentemente:</p>
                  <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                    <li>Todos los productos de prueba</li>
                    <li>Todos los usuarios de prueba</li>
                    <li>Todas las interacciones de prueba</li>
                    <li>Todos los modelos entrenados</li>
                    <li>Historial de jobs de entrenamiento e ingesta</li>
                  </ul>
                  <p className="font-medium text-orange-600 dark:text-orange-400">
                    ⚠️ Esta acción no se puede deshacer.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    🔒 Tus API Keys y configuración de cuenta se mantendrán intactas.
                  </p>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleResetSandbox}
                  className="bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800"
                  disabled={isResetting}
                >
                  {isResetting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Limpiando...
                    </>
                  ) : (
                    'Sí, limpiar sandbox'
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <div className="text-xs text-muted-foreground space-y-1">
            <p>💡 <strong>Tip:</strong> Limpia tus datos regularmente para probar diferentes escenarios</p>
            <p>🚀 <strong>Workflow recomendado:</strong> Reset → Cargar datos test → Entrenar → Evaluar</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 
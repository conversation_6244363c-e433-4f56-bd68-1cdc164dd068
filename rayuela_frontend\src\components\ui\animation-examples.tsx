// Example component showing how to use Rayuela entrance animations
// This file serves as documentation and examples for the animation system
//
// USAGE GUIDELINES FOR RAYUELA ANIMATIONS:
//
// 1. Page-level entrance: Use `rayuela-fade-in` on the main container
//    <div className="rayuela-fade-in">{/* Your page content */}</div>
//
// 2. Card/component entrance: Use `rayuela-scale-in` for individual components
//    <Card className="rayuela-scale-in">{/* Card content */}</Card>
//
// 3. Staggered animations: Combine with `rayuela-stagger-{1-4}` for sequential appearance
//    <Card className="rayuela-scale-in rayuela-stagger-1">...</Card>
//    <Card className="rayuela-scale-in rayuela-stagger-2">...</Card>
//
// 4. Text/button animations: Use `rayuela-slide-up` for content that should slide in from below
//    <div className="rayuela-slide-up"><h2>Heading</h2></div>
//
// 5. Loading states: Skeletons already have shimmer animation built-in
//    <Skeleton className="h-4 w-1/2" />
//
// PERFORMANCE NOTES:
// - Animations are lightweight (0.3-0.4s duration)
// - Use sparingly to avoid overwhelming users
// - Stagger delays are minimal (0.1s increments)
// - All animations respect prefers-reduced-motion

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';

// Example 1: Basic page entrance animation
export function PageWithFadeIn({ children }: { children: React.ReactNode }) {
  return (
    <div className="rayuela-fade-in">
      {children}
    </div>
  );
}

// Example 2: Staggered card animations
export function StaggeredCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="rayuela-scale-in rayuela-stagger-1">
        <CardHeader>
          <CardTitle>API Calls</CardTitle>
          <CardDescription>Monthly usage</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">1,234</p>
        </CardContent>
      </Card>
      
      <Card className="rayuela-scale-in rayuela-stagger-2">
        <CardHeader>
          <CardTitle>Storage</CardTitle>
          <CardDescription>Data stored</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">2.5 GB</p>
        </CardContent>
      </Card>
      
      <Card className="rayuela-scale-in rayuela-stagger-3">
        <CardHeader>
          <CardTitle>Training</CardTitle>
          <CardDescription>Model status</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">Ready</p>
        </CardContent>
      </Card>
    </div>
  );
}

// Example 3: Loading state with improved skeletons
export function LoadingCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="rayuela-fade-in">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <Skeleton className="h-5 w-5 mr-2 rounded-md" />
              <Skeleton className="h-6 w-3/4" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-1/2" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-10 w-1/2 mx-auto" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Example 4: Content appearing with slide-up animation
export function SlideUpContent() {
  return (
    <div className="space-y-4">
      <div className="rayuela-slide-up">
        <h2 className="text-2xl font-bold">Welcome to Rayuela</h2>
        <p className="text-muted-foreground">
          Your AI-powered recommendation system
        </p>
      </div>
      
      <div className="rayuela-slide-up rayuela-stagger-1">
        <Button>Get Started</Button>
      </div>
    </div>
  );
}

// Example 5: Mixed animations for rich content
export function RichContentExample() {
  return (
    <div className="rayuela-fade-in">
      <div className="rayuela-slide-up">
        <h1 className="text-3xl font-bold mb-4">Dashboard</h1>
      </div>
      
      <div className="rayuela-scale-in rayuela-stagger-1 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button size="sm">Create API Key</Button>
              <Button size="sm" variant="outline">View Docs</Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="rayuela-slide-up rayuela-stagger-2">
        <StaggeredCards />
      </div>
    </div>
  );
} 
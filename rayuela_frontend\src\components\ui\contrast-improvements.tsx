import * as React from "react"
import { Badge } from "./badge"
import { Alert, AlertDescription, AlertTitle } from "./alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card"
import { AlertTriangle, CheckCircle, Info, XCircle } from "lucide-react"

// Componente mejorado para badges con mejor contraste
interface ContrastBadgeProps {
  variant: 'success' | 'warning' | 'info' | 'destructive'
  children: React.ReactNode
  className?: string
}

function ContrastBadge({ variant, children, className }: ContrastBadgeProps) {
  // Mapeo mejorado de variantes con mejor contraste
  const contrastVariants = {
    success: "bg-success/15 text-success border-success/30 hover:bg-success/25 dark:bg-success/20 dark:text-success-foreground dark:border-success/40",
    warning: "bg-warning/15 text-warning border-warning/30 hover:bg-warning/25 dark:bg-warning/20 dark:text-warning-foreground dark:border-warning/40",
    info: "bg-info/15 text-info border-info/30 hover:bg-info/25 dark:bg-info/20 dark:text-info-foreground dark:border-info/40",
    destructive: "bg-destructive/15 text-destructive border-destructive/30 hover:bg-destructive/25 dark:bg-destructive/20 dark:text-destructive-foreground dark:border-destructive/40"
  }

  return (
    <Badge 
      className={`
        ${contrastVariants[variant]} 
        transition-colors duration-200
        ${className}
      `}
    >
      {children}
    </Badge>
  )
}

// Alerta con contraste mejorado
interface ContrastAlertProps {
  variant: 'success' | 'warning' | 'info' | 'destructive'
  title: string
  children: React.ReactNode
}

function ContrastAlert({ variant, title, children }: ContrastAlertProps) {
  const icons = {
    success: CheckCircle,
    warning: AlertTriangle,
    info: Info,
    destructive: XCircle
  }

  const Icon = icons[variant]

  return (
    <Alert variant={variant}>
      <Icon className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>
        {children}
      </AlertDescription>
    </Alert>
  )
}

// Card con indicadores de estado mejorados
interface StatusCardProps {
  title: string
  description?: string
  status: 'active' | 'warning' | 'inactive' | 'error'
  value?: string | number
  children?: React.ReactNode
}

function StatusCard({ title, description, status, value, children }: StatusCardProps) {
  const statusStyles = {
    active: {
      indicator: "bg-green-500",
      badge: "success" as const,
      badgeText: "Activo"
    },
    warning: {
      indicator: "bg-amber-500",
      badge: "warning" as const,
      badgeText: "Advertencia"
    },
    inactive: {
      indicator: "bg-gray-400",
      badge: "info" as const,
      badgeText: "Inactivo"
    },
    error: {
      indicator: "bg-red-500",
      badge: "destructive" as const,
      badgeText: "Error"
    }
  }

  const style = statusStyles[status]

  return (
    <Card className="relative">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${style.indicator}`} />
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
          </div>
          <ContrastBadge variant={style.badge} className="text-xs">
            {style.badgeText}
          </ContrastBadge>
        </div>
        {description && (
          <CardDescription className="text-sm">{description}</CardDescription>
        )}
      </CardHeader>
      {(value || children) && (
        <CardContent>
          {value && (
            <div className="text-2xl font-bold tabular-nums">{value}</div>
          )}
          {children}
        </CardContent>
      )}
    </Card>
  )
}

// Showcase de mejoras de contraste
function ContrastImprovementsShowcase() {
  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Mejoras de Contraste Implementadas</h2>
        <p className="text-muted-foreground mb-6">
          Componentes mejorados para cumplir con estándares WCAG AA/AAA en modo claro y oscuro.
        </p>
      </div>

      {/* Badges mejorados */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Badges con Contraste Mejorado</h3>
        <div className="flex flex-wrap gap-2">
          <ContrastBadge variant="success">Éxito</ContrastBadge>
          <ContrastBadge variant="warning">Advertencia</ContrastBadge>
          <ContrastBadge variant="info">Información</ContrastBadge>
          <ContrastBadge variant="destructive">Error</ContrastBadge>
        </div>
      </div>

      {/* Alertas mejoradas */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Alertas con Mejor Legibilidad</h3>
        <div className="space-y-3">
          <ContrastAlert variant="success" title="Operación exitosa">
            Los cambios se han guardado correctamente con contraste AA compliant.
          </ContrastAlert>
          <ContrastAlert variant="warning" title="Verificación pendiente">
            Tu email aún no ha sido verificado. Revisa tu bandeja de entrada.
          </ContrastAlert>
          <ContrastAlert variant="info" title="Información importante">
            Esta función utiliza el sistema de colores oklch para mejor contraste.
          </ContrastAlert>
        </div>
      </div>

      {/* Cards de estado */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Cards con Indicadores Mejorados</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatusCard 
            title="API Keys" 
            description="Claves activas"
            status="active" 
            value="3"
          />
          <StatusCard 
            title="Verificación" 
            description="Email pendiente"
            status="warning" 
            value="1"
          />
          <StatusCard 
            title="Sesiones" 
            description="Inactivas"
            status="inactive" 
            value="0"
          />
          <StatusCard 
            title="Errores" 
            description="Últimas 24h"
            status="error" 
            value="0"
          />
        </div>
      </div>
    </div>
  )
}

export { ContrastBadge, ContrastAlert, StatusCard, ContrastImprovementsShowcase } 

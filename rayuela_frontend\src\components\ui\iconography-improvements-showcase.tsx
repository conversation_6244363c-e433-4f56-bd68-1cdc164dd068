"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import {
  CheckCircle,
  AlertTriangle,
  Info,
  LightbulbIcon,
  BarChart3Icon,
  KeyIcon,
  BookOpenIcon,
  DatabaseIcon,
  SparklesIcon,
  ChevronRightIcon,
  ExternalLinkIcon,
  CreditCardIcon
} from 'lucide-react';
import { SemanticIcon, IconWithText, iconSizes, iconContexts } from '@/components/ui/icon';

export default function IconographyImprovementsShowcase() {
  return (
    <div className="space-y-8 max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-display-md font-bold">Mejoras de Iconografía</h1>
        <p className="text-body-lg text-muted-foreground max-w-3xl mx-auto">
          Implementación de un sistema semántico de iconos que mejora la consistencia visual, 
          facilita el mantenimiento y garantiza una mejor adaptación al modo oscuro.
        </p>
      </div>

      {/* Problema y Solución */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="border-destructive/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <SemanticIcon icon={AlertTriangle} size="md" context="error" />
              Problemas Identificados
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="destructive" className="text-xs">Colores Directos</Badge>
                <span className="text-sm">text-info, text-green-500</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="destructive" className="text-xs">Tamaños Inconsistentes</Badge>
                <span className="text-sm">h-3 w-3, h-4 w-4, h-5 w-5...</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="destructive" className="text-xs">Mantenimiento</Badge>
                <span className="text-sm">Repetición de código</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-success/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-success">
              <SemanticIcon icon={CheckCircle} size="md" context="success" />
              Soluciones Implementadas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs bg-success/10 text-success border-success/20">Colores Semánticos</Badge>
                <span className="text-sm">text-primary, text-success</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs bg-success/10 text-success border-success/20">Sistema de Tamaños</Badge>
                <span className="text-sm">xs, sm, md, lg, xl, 2xl</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs bg-success/10 text-success border-success/20">Componentes Reutilizables</Badge>
                <span className="text-sm">SemanticIcon, IconWithText</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Comparación Antes vs Después */}
      <Card>
        <CardHeader>
          <CardTitle>Comparación: Antes vs Después</CardTitle>
          <CardDescription>
            Ejemplos reales de mejoras implementadas en los componentes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Ejemplo 1: Iconos de Estado */}
          <div>
            <h3 className="font-semibold mb-3">Iconos de Estado y Feedback</h3>
            <div className="grid md:grid-cols-2 gap-4">
              
              {/* Antes */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">❌ Antes (Colores Directos)</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Operación exitosa</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <span>Advertencia importante</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-5 w-5 text-info" />
                    <span>Información adicional</span>
                  </div>
                </div>
              </div>

              {/* Después */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">✅ Después (Colores Semánticos)</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={CheckCircle} size="md" context="success" />
                    <span>Operación exitosa</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={AlertTriangle} size="md" context="warning" />
                    <span>Advertencia importante</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={Info} size="md" context="info" />
                    <span>Información adicional</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-border" />

          {/* Ejemplo 2: Navegación y Acciones */}
          <div>
            <h3 className="font-semibold mb-3">Navegación y Acciones</h3>
            <div className="grid md:grid-cols-2 gap-4">
              
              {/* Antes */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">❌ Antes</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <KeyIcon className="h-4 w-4 text-info" />
                    <span>Gestionar API Keys</span>
                    <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpenIcon className="h-4 w-4 text-green-500" />
                    <span>Documentación</span>
                    <ExternalLinkIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCardIcon className="h-4 w-4 text-purple-500" />
                    <span>Facturación</span>
                  </div>
                </div>
              </div>

              {/* Después */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">✅ Después</h4>
                <div className="space-y-2">
                  <IconWithText icon={KeyIcon} size="sm" context="primary">
                    <span>Gestionar API Keys</span>
                    <SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" />
                  </IconWithText>
                  <IconWithText icon={BookOpenIcon} size="sm" context="primary">
                    <span>Documentación</span>
                    <SemanticIcon icon={ExternalLinkIcon} size="sm" context="muted" />
                  </IconWithText>
                  <IconWithText icon={CreditCardIcon} size="sm" context="primary">
                    <span>Facturación</span>
                  </IconWithText>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-border" />

          {/* Ejemplo 3: Lista de Tareas */}
          <div>
            <h3 className="font-semibold mb-3">Lista de Tareas con Progreso</h3>
            <div className="grid md:grid-cols-2 gap-4">
              
              {/* Antes */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">❌ Antes</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <KeyIcon className="h-4 w-4 text-info mt-0.5" />
                    <div>
                      <p className="font-medium">Generar API Key</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Configurar autenticación</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <DatabaseIcon className="h-4 w-4 text-purple-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Cargar Datos</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Importar productos</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <SparklesIcon className="h-4 w-4 text-orange-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Entrenar Modelo</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Generar recomendaciones</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Después */}
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">✅ Después</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <SemanticIcon icon={KeyIcon} size="sm" context="primary" className="mt-0.5" />
                    <div>
                      <p className="font-medium">Generar API Key</p>
                      <p className="text-sm text-muted-foreground">Configurar autenticación</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <SemanticIcon icon={DatabaseIcon} size="sm" context="primary" className="mt-0.5" />
                    <div>
                      <p className="font-medium">Cargar Datos</p>
                      <p className="text-sm text-muted-foreground">Importar productos</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <SemanticIcon icon={SparklesIcon} size="sm" context="primary" className="mt-0.5" />
                    <div>
                      <p className="font-medium">Entrenar Modelo</p>
                      <p className="text-sm text-muted-foreground">Generar recomendaciones</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sistema de Tamaños */}
      <Card>
        <CardHeader>
          <CardTitle>Sistema de Tamaños Semánticos</CardTitle>
          <CardDescription>
            Tamaños consistentes y predecibles para diferentes contextos de uso
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(iconSizes).map(([size, className]) => (
              <div key={size} className="flex items-center gap-3 p-3 border rounded-lg">
                <SemanticIcon icon={BarChart3Icon} size={size as any} context="primary" />
                <div>
                  <p className="font-medium">{size}</p>
                  <p className="text-xs text-muted-foreground">{className}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Contextos Semánticos */}
      <Card>
        <CardHeader>
          <CardTitle>Contextos Semánticos de Color</CardTitle>
          <CardDescription>
            Colores que se adaptan automáticamente al tema y mantienen consistencia
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(iconContexts).map(([context, className]) => (
              <div key={context} className="flex items-center gap-3 p-3 border rounded-lg">
                <SemanticIcon icon={LightbulbIcon} size="md" context={context as any} />
                <div>
                  <p className="font-medium">{context}</p>
                  <p className="text-xs text-muted-foreground">{className}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Código de Implementación */}
      <Card>
        <CardHeader>
          <CardTitle>Implementación</CardTitle>
          <CardDescription>
            Ejemplos de código para migrar a iconos semánticos
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Uso Básico</h4>
            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
              <code>{`// Antes
<CheckCircle className="h-5 w-5 text-green-500" />

// Después
<SemanticIcon icon={CheckCircle} size="md" context="success" />`}</code>
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Iconos con Texto</h4>
            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
              <code>{`// Antes
<div className="flex items-center gap-2">
  <BookOpenIcon className="h-4 w-4 text-info" />
  <span>Documentación</span>
</div>

// Después
<IconWithText icon={BookOpenIcon} size="sm" context="primary">
  Documentación
</IconWithText>`}</code>
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Componentes de Conveniencia</h4>
            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
              <code>{`// Componentes especializados disponibles
<SuccessIcon icon={CheckCircle} size="md" />
<WarningIcon icon={AlertTriangle} size="md" />
<ErrorIcon icon={XCircle} size="md" />
<InfoIcon icon={Info} size="md" />
<ActionIcon icon={ArrowRight} size="sm" />
<NavigationIcon icon={ChevronRight} size="sm" />`}</code>
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* Beneficios */}
      <Card>
        <CardHeader>
          <CardTitle>Beneficios de la Implementación</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Consistencia Visual
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Tamaños estandarizados para todo el sitio</li>
                <li>• Colores semánticos que se adaptan al tema</li>
                <li>• Peso visual consistente con el texto</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Mantenibilidad
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Cambios centralizados de color y tamaño</li>
                <li>• Menos repetición de código</li>
                <li>• Migración más fácil de temas</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Accesibilidad
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Soporte automático para aria-label</li>
                <li>• Contraste adecuado en modo oscuro</li>
                <li>• Iconos decorativos marcados correctamente</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Experiencia de Desarrollo
              </h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• API intuitiva y bien documentada</li>
                <li>• Autocompletado con TypeScript</li>
                <li>• Componentes reutilizables</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 

import { useState, useEffect } from 'react';
import { getRayuela } from '@/lib/generated/rayuelaAPI';
import type { ModelMetadataResponse } from '@/lib/generated/rayuelaAPI';

export interface ModelInfo {
  id: number;
  artifact_name: string;
  artifact_version: string;
  description?: string;
  training_date: string;
  performance_metrics?: Record<string, number>;
  parameters?: Record<string, unknown>;
}

export function useModels() {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchModels = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Use the real API to fetch models
      const response = await getRayuela().listModelsApiV1PipelineModelsGet();
      
      // Map the API response to our interface
      const mappedModels: ModelInfo[] = response.data.map((model: ModelMetadataResponse) => ({
        id: model.id,
        artifact_name: model.artifact_name,
        artifact_version: model.artifact_version,
        description: model.description || undefined,
        training_date: model.training_date,
        performance_metrics: model.performance_metrics as Record<string, number> || undefined,
        parameters: model.parameters as Record<string, unknown> || undefined,
      }));
      
      setModels(mappedModels);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading models');
      console.error('Error loading models:', err);
      
      // If API fails, fall back to empty array for now
      setModels([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getModelMetrics = async (modelId: number) => {
    try {
      const response = await getRayuela().getModelMetricsApiV1PipelineModelsModelIdMetricsGet(modelId);
      return response.data;
    } catch (err) {
      console.error('Error fetching model metrics:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchModels();
  }, []);

  return {
    models,
    isLoading,
    error,
    fetchModels,
    getModelMetrics
  };
} 
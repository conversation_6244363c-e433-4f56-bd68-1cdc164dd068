/**
 * Configuration file for recommendation rules
 * 
 * This file defines the rules used to generate recommendations based on metrics.
 * Each rule specifies conditions, thresholds, and actions for a specific metric.
 */

/**
 * Interface for a recommendation rule
 */
export interface RecommendationRule {
  id: string;
  metricPath: string[];  // Path to the metric in the data object (e.g., ['summary', 'precision'])
  threshold: number;     // Threshold value for triggering the rule
  comparison: 'lt' | 'gt' | 'eq' | 'lte' | 'gte'; // Comparison operator (less than, greater than, etc.)
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'accuracy' | 'diversity' | 'confidence' | 'performance';
  iconKey: string;       // Key for the icon to use (mapped to actual icon in the component)
  actions: string[];
  metrics: {
    name: string;
    valuePath: string[]; // Path to the value in the data object
    valueMultiplier?: number; // Optional multiplier for the value (e.g., to convert to percentage)
    target: number;
    unit: string;
  }[];
  // For rules that need special handling
  specialLogic?: {
    type: string;
    params?: Record<string, any>;
  };
}

/**
 * Rules for performance metrics
 */
export const performanceRules: RecommendationRule[] = [
  {
    id: 'improve-precision',
    metricPath: ['summary', 'precision'],
    threshold: 0.5,
    comparison: 'lt',
    title: 'METRIC_REC_PRECISION_TITLE',
    description: 'METRIC_REC_PRECISION_DESC',
    priority: 'high',
    category: 'accuracy',
    iconKey: 'BarChart2Icon',
    actions: [
      'METRIC_REC_PRECISION_ACTION_1',
      'METRIC_REC_PRECISION_ACTION_2',
      'METRIC_REC_PRECISION_ACTION_3'
    ],
    metrics: [
      {
        name: 'METRIC_PRECISION',
        valuePath: ['summary', 'precision'],
        valueMultiplier: 100,
        target: 50,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-diversity',
    metricPath: ['summary', 'diversity'],
    threshold: 0.4,
    comparison: 'lt',
    title: 'METRIC_REC_DIVERSITY_TITLE',
    description: 'METRIC_REC_DIVERSITY_DESC',
    priority: 'medium',
    category: 'diversity',
    iconKey: 'ShuffleIcon',
    actions: [
      'METRIC_REC_DIVERSITY_ACTION_1',
      'METRIC_REC_DIVERSITY_ACTION_2',
      'METRIC_REC_DIVERSITY_ACTION_3',
      'METRIC_REC_DIVERSITY_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_DIVERSITY',
        valuePath: ['summary', 'diversity'],
        valueMultiplier: 100,
        target: 40,
        unit: '%'
      },
      {
        name: 'METRIC_CATALOG_COVERAGE',
        valuePath: ['summary', 'catalog_coverage'],
        valueMultiplier: 100,
        target: 30,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-novelty',
    metricPath: ['summary', 'novelty'],
    threshold: 0.3,
    comparison: 'lt',
    title: 'METRIC_REC_NOVELTY_TITLE',
    description: 'METRIC_REC_NOVELTY_DESC',
    priority: 'medium',
    category: 'diversity',
    iconKey: 'ZapIcon',
    actions: [
      'METRIC_REC_NOVELTY_ACTION_1',
      'METRIC_REC_NOVELTY_ACTION_2',
      'METRIC_REC_NOVELTY_ACTION_3',
      'METRIC_REC_NOVELTY_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_NOVELTY',
        valuePath: ['summary', 'novelty'],
        valueMultiplier: 100,
        target: 30,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-ranking',
    metricPath: ['summary', 'ndcg'],
    threshold: 0.45,
    comparison: 'lt',
    title: 'METRIC_REC_RANKING_TITLE',
    description: 'METRIC_REC_RANKING_DESC',
    priority: 'high',
    category: 'accuracy',
    iconKey: 'BarChart2Icon',
    actions: [
      'METRIC_REC_RANKING_ACTION_1',
      'METRIC_REC_RANKING_ACTION_2',
      'METRIC_REC_RANKING_ACTION_3',
      'METRIC_REC_RANKING_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_NDCG',
        valuePath: ['summary', 'ndcg'],
        valueMultiplier: 100,
        target: 45,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-serendipity',
    metricPath: ['summary', 'serendipity'],
    threshold: 0.25,
    comparison: 'lt',
    title: 'METRIC_REC_SERENDIPITY_TITLE',
    description: 'METRIC_REC_SERENDIPITY_DESC',
    priority: 'low',
    category: 'diversity',
    iconKey: 'LightbulbIcon',
    actions: [
      'METRIC_REC_SERENDIPITY_ACTION_1',
      'METRIC_REC_SERENDIPITY_ACTION_2',
      'METRIC_REC_SERENDIPITY_ACTION_3',
      'METRIC_REC_SERENDIPITY_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_SERENDIPITY',
        valuePath: ['summary', 'serendipity'],
        valueMultiplier: 100,
        target: 25,
        unit: '%'
      }
    ]
  }
];

/**
 * Rules for confidence metrics
 */
export const confidenceRules: RecommendationRule[] = [
  {
    id: 'improve-confidence',
    specialLogic: {
      type: 'avgConfidence',
      params: {
        threshold: 0.6,
        paths: [
          ['confidence_distribution', 'collaborative', 'avg'],
          ['confidence_distribution', 'content', 'avg'],
          ['confidence_distribution', 'hybrid', 'avg']
        ]
      }
    },
    metricPath: [], // Not used for special logic rules
    threshold: 0, // Not used for special logic rules
    comparison: 'lt',
    title: 'METRIC_REC_CONFIDENCE_TITLE',
    description: 'METRIC_REC_CONFIDENCE_DESC',
    priority: 'high',
    category: 'confidence',
    iconKey: 'AlertCircleIcon',
    actions: [
      'METRIC_REC_CONFIDENCE_ACTION_1',
      'METRIC_REC_CONFIDENCE_ACTION_2',
      'METRIC_REC_CONFIDENCE_ACTION_3',
      'METRIC_REC_CONFIDENCE_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_AVG_CONFIDENCE',
        valuePath: [], // Will be calculated in the special logic
        valueMultiplier: 100,
        target: 60,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-model-type',
    specialLogic: {
      type: 'worstModel',
      params: {
        threshold: 0.5,
        models: {
          collaborative: {
            path: ['confidence_distribution', 'collaborative', 'avg'],
            name: 'METRIC_MODEL_COLLABORATIVE',
            actions: [
              'METRIC_REC_MODEL_COLLAB_ACTION_1',
              'METRIC_REC_MODEL_COLLAB_ACTION_2',
              'METRIC_REC_MODEL_COLLAB_ACTION_3',
              'METRIC_REC_MODEL_COLLAB_ACTION_4'
            ]
          },
          content: {
            path: ['confidence_distribution', 'content', 'avg'],
            name: 'METRIC_MODEL_CONTENT',
            actions: [
              'METRIC_REC_MODEL_CONTENT_ACTION_1',
              'METRIC_REC_MODEL_CONTENT_ACTION_2',
              'METRIC_REC_MODEL_CONTENT_ACTION_3',
              'METRIC_REC_MODEL_CONTENT_ACTION_4'
            ]
          },
          hybrid: {
            path: ['confidence_distribution', 'hybrid', 'avg'],
            name: 'METRIC_MODEL_HYBRID',
            actions: [
              'METRIC_REC_MODEL_HYBRID_ACTION_1',
              'METRIC_REC_MODEL_HYBRID_ACTION_2',
              'METRIC_REC_MODEL_HYBRID_ACTION_3',
              'METRIC_REC_MODEL_HYBRID_ACTION_4'
            ]
          }
        }
      }
    },
    metricPath: [], // Not used for special logic rules
    threshold: 0, // Not used for special logic rules
    comparison: 'lt',
    title: 'METRIC_REC_MODEL_TITLE', // Will be formatted with model name
    description: 'METRIC_REC_MODEL_DESC', // Will be formatted with model name
    priority: 'medium',
    category: 'confidence',
    iconKey: 'DatabaseIcon',
    actions: [], // Will be set based on the worst model
    metrics: [
      {
        name: 'METRIC_MODEL_CONFIDENCE', // Will be formatted with model name
        valuePath: [], // Will be set based on the worst model
        valueMultiplier: 100,
        target: 50,
        unit: '%'
      }
    ]
  },
  {
    id: 'improve-low-confidence-categories',
    specialLogic: {
      type: 'lowConfidenceCategories',
      params: {
        threshold: 0.5,
        maxCategories: 3
      }
    },
    metricPath: [], // Not used for special logic rules
    threshold: 0, // Not used for special logic rules
    comparison: 'lt',
    title: 'METRIC_REC_CATEGORIES_TITLE',
    description: 'METRIC_REC_CATEGORIES_DESC', // Will be formatted with category name
    priority: 'medium',
    category: 'confidence',
    iconKey: 'DatabaseIcon',
    actions: [
      'METRIC_REC_CATEGORIES_ACTION_1', // Will be formatted with category name
      'METRIC_REC_CATEGORIES_ACTION_2',
      'METRIC_REC_CATEGORIES_ACTION_3',
      'METRIC_REC_CATEGORIES_ACTION_4'
    ],
    metrics: [] // Will be populated with category metrics
  },
  {
    id: 'improve-confidence-factors',
    specialLogic: {
      type: 'lowestConfidenceFactor',
      params: {
        threshold: 0.4,
        factors: {
          user_history_size: {
            name: 'METRIC_FACTOR_USER_HISTORY',
            actions: [
              'METRIC_REC_FACTOR_USER_HISTORY_ACTION_1',
              'METRIC_REC_FACTOR_USER_HISTORY_ACTION_2',
              'METRIC_REC_FACTOR_USER_HISTORY_ACTION_3',
              'METRIC_REC_FACTOR_USER_HISTORY_ACTION_4'
            ]
          },
          item_popularity: {
            name: 'METRIC_FACTOR_ITEM_POPULARITY',
            actions: [
              'METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1',
              'METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2',
              'METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3',
              'METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4'
            ]
          },
          category_strength: {
            name: 'METRIC_FACTOR_CATEGORY_STRENGTH',
            actions: [
              'METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1',
              'METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2',
              'METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3',
              'METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4'
            ]
          },
          model_type: {
            name: 'METRIC_FACTOR_MODEL_TYPE',
            actions: [
              'METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1',
              'METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2',
              'METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3',
              'METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4'
            ]
          }
        }
      }
    },
    metricPath: [], // Not used for special logic rules
    threshold: 0, // Not used for special logic rules
    comparison: 'lt',
    title: 'METRIC_REC_FACTOR_TITLE', // Will be formatted with factor name
    description: 'METRIC_REC_FACTOR_DESC', // Will be formatted with factor name
    priority: 'medium',
    category: 'confidence',
    iconKey: 'UsersIcon',
    actions: [], // Will be set based on the lowest factor
    metrics: [
      {
        name: 'METRIC_FACTOR', // Will be formatted with factor name
        valuePath: [], // Will be set based on the lowest factor
        valueMultiplier: 100,
        target: 40,
        unit: '%'
      }
    ]
  },
  {
    id: 'address-negative-trend',
    specialLogic: {
      type: 'negativeTrend',
      params: {
        minDays: 3,
        threshold: 0.9, // Current value should be less than 90% of previous value
        path: ['confidence_trends', 'last_7_days']
      }
    },
    metricPath: [], // Not used for special logic rules
    threshold: 0, // Not used for special logic rules
    comparison: 'lt',
    title: 'METRIC_REC_TREND_TITLE',
    description: 'METRIC_REC_TREND_DESC',
    priority: 'high',
    category: 'confidence',
    iconKey: 'TrendingUpIcon',
    actions: [
      'METRIC_REC_TREND_ACTION_1',
      'METRIC_REC_TREND_ACTION_2',
      'METRIC_REC_TREND_ACTION_3',
      'METRIC_REC_TREND_ACTION_4'
    ],
    metrics: [
      {
        name: 'METRIC_CONFIDENCE_CHANGE',
        valuePath: [], // Will be calculated in the special logic
        valueMultiplier: 100,
        target: 0,
        unit: '%'
      }
    ]
  }
];

/**
 * All recommendation rules
 */
export const allRecommendationRules: RecommendationRule[] = [
  ...performanceRules,
  ...confidenceRules
];

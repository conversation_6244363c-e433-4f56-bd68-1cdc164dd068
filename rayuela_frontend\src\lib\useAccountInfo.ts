// src/lib/useAccountInfo.ts
import useSWR from 'swr';
import { getCurrentAccount as getMyAccount, AccountInfo } from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { getRayuela } from '@/lib/generated/rayuelaAPI';

/**
 * Hook para obtener y gestionar la información de la cuenta del usuario.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 * 
 * Nota: Las funciones relacionadas con API Keys han sido movidas al hook useApiKeys
 * para una mejor separación de responsabilidades.
 * 
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de la cuenta, estado de carga, errores y funciones de utilidad
 */
export function useAccountInfo(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}) {
  const { token, apiKey } = useAuth();

  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR<AccountInfo>(
    token && apiKey ? ['account-info', token, apiKey] : null,
    async () => {
      return await getMyAccount();
    },
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000, // 1 minuto por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para actualizar el estado del checklist de onboarding
  const updateChecklistStatus = async (checklistStatus: Record<string, boolean | string | number>) => {
    if (!token || !apiKey) {
      throw new Error('No token or API key available');
    }

    try {
      // Call the backend API to update the onboarding checklist status
      const response = await getRayuela().patchCurrentAccountApiV1AccountsCurrentPatch({
        onboardingChecklistStatus: checklistStatus
      });

      // Update the local cache with the new data
      const updatedData = response.data;
      
      // Optimistically update the SWR cache
      await mutate(updatedData, { revalidate: false });

      return updatedData;
    } catch (error) {
      console.error('Error updating checklist status:', error);
      
      // If the API call fails, at least update the local cache optimistically
      // This ensures UI responsiveness even if the backend is temporarily unavailable
      const updatedData = data ? {
        ...data,
        onboardingChecklistStatus: checklistStatus
      } as AccountInfo : undefined;

      // Update the cache local
      await mutate(updatedData, { revalidate: false });

      throw error;
    }
  };

  // Función para obtener la fecha de creación de la cuenta
  const getCreationDate = () => {
    if (!data) return null;
    return data.createdAt ? new Date(data.createdAt) : null;
  };

  // Función para verificar si la cuenta está activa
  const isActive = () => {
    if (!data) return false;
    return data.isActive;
  };

  // Función para obtener el plan de suscripción directamente de la información de cuenta
  const getSubscriptionPlan = () => {
    if (!data || !data.subscription) return null;
    return data.subscription.plan;
  };

  // Función para verificar si la suscripción está activa
  const isSubscriptionActive = () => {
    if (!data || !data.subscription) return false;
    return data.subscription.isActive;
  };

  // Función para obtener la fecha de expiración de la suscripción
  const getSubscriptionExpiryDate = () => {
    if (!data || !data.subscription || !data.subscription.expiresAt) return null;
    return new Date(data.subscription.expiresAt);
  };

  // Función para obtener el estado del checklist de onboarding
  const getChecklistStatus = () => {
    if (!data || !data.onboardingChecklistStatus) return {};
    return data.onboardingChecklistStatus;
  };

  return {
    accountData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: null,
    getCreationDate,
    isActive,
    getSubscriptionPlan,
    isSubscriptionActive,
    getSubscriptionExpiryDate,
    getChecklistStatus,
    updateChecklistStatus
  };
}

// src/lib/useApiKeys.ts

import use<PERSON><PERSON> from 'swr';
import {
  getApi<PERSON><PERSON>s,
  create<PERSON>pi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>,
  revokeApiKey,
  NewApiKeyResponse,
  ApiKey,
  ApiKeyListResponse,
  ApiKeyCreate,
  ApiKeyUpdate,
  ApiError
} from '@/lib/api';
import { useState } from 'react';

// Unified interface for API key state
interface ApiKeyState {
  // For multi-key mode (default)
  data: ApiKeyListResponse | null;
  // For legacy single-key mode compatibility
  primaryKey: ApiKey | null;
  error: ApiError | null;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => Promise<ApiKeyListResponse | undefined>;
  dataUpdatedAt: number;
}

// Unified interface for API key operations
interface ApiKeyOperations {
  // Multi-key operations
  createApiKey: (data: ApiKeyCreate) => Promise<NewApiKeyResponse | null>;
  updateApiKey: (id: number, data: ApiKeyUpdate) => Promise<ApiKey | null>;
  revokeApiKey: (id: number) => Promise<boolean>;
  
  // Legacy operation for backward compatibility
  regenerateApiKey: () => Promise<NewApiKeyResponse | null>;
  
  // Operation states
  isCreating: boolean;
  isUpdating: boolean;
  isRevoking: boolean;
  isRegenerating: boolean;
  operationError: ApiError | null;
  
  // Utility functions
  getFormattedApiKey: (apiKey?: ApiKey) => string | null;
}

// Hook options interface
interface UseApiKeysOptions {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
  /**
   * @deprecated Use the unified hook response instead. 
   * This option is kept for backward compatibility.
   */
  legacyMode?: boolean;
}

/**
 * Unified hook for managing API Keys.
 * 
 * This hook consolidates the previous useApiKeys and useMultiApiKeys functionality
 * into a single, more maintainable interface that supports both single and multiple
 * API key management patterns.
 *
 * @param options Configuration options for the hook
 * @returns Object with API key data, states, and operations
 */
export function useApiKeys(options: UseApiKeysOptions = {}): ApiKeyState & ApiKeyOperations {
  // Operation states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRevoking, setIsRevoking] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [operationError, setOperationError] = useState<ApiError | null>(null);

  // Unified SWR call for fetching API keys
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  } = useSWR<ApiKeyListResponse, ApiError>(
    'api-keys',
    async () => await getApiKeys(),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? true,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000,
      errorRetryCount: options.errorRetryCount ?? 3,
      onError: (err) => {
        console.error('Error fetching API keys:', err);
      }
    }
  );

  // Get primary API key for legacy compatibility (first active key, or first key if none active)
  const primaryKey = data?.api_keys && data.api_keys.length > 0 
    ? data.api_keys.find(key => key.is_active) || data.api_keys[0]
    : null;

  /**
   * Creates a new API Key
   * @param keyData Data for creating the API Key
   * @returns The newly created API Key response or null if error occurs
   */
  const createNewApiKey = async (keyData: ApiKeyCreate): Promise<NewApiKeyResponse | null> => {
    setIsCreating(true);
    setOperationError(null);

    try {
      const transformedData = {
        name: keyData.name || '',
        permissions: [] as string[]
      };
      const response = await createApiKey(transformedData);
      await mutate();
      return response;
    } catch (err) {
      const apiError = err instanceof ApiError ? err : new ApiError(
        'Error al crear API Key',
        500
      );
      setOperationError(apiError);
      throw apiError;
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Updates an existing API Key
   * @param id ID of the API Key to update
   * @param keyData Data to update
   * @returns The updated API Key or null if error occurs
   */
  const updateExistingApiKey = async (id: number, keyData: ApiKeyUpdate): Promise<ApiKey | null> => {
    setIsUpdating(true);
    setOperationError(null);

    try {
      const transformedData = {
        name: keyData.name || undefined,
        permissions: [] as string[]
      };
      const response = await updateApiKey(id.toString(), transformedData);
      await mutate();
      return response;
    } catch (err) {
      const apiError = err instanceof ApiError ? err : new ApiError(
        'Error al actualizar API Key',
        500
      );
      setOperationError(apiError);
      throw apiError;
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Revokes a specific API Key
   * @param id ID of the API Key to revoke
   * @returns true if successfully revoked, false if error occurs
   */
  const revokeExistingApiKey = async (id: number): Promise<boolean> => {
    setIsRevoking(true);
    setOperationError(null);

    try {
      await revokeApiKey(id);
      await mutate();
      return true;
    } catch (err) {
      const apiError = err instanceof ApiError ? err : new ApiError(
        'Error al revocar API Key',
        500
      );
      setOperationError(apiError);
      throw apiError;
    } finally {
      setIsRevoking(false);
    }
  };

  /**
   * Legacy method: Regenerates (creates a new) API Key
   * @deprecated Use createApiKey instead
   * @returns The newly created API Key response or null if error occurs
   */
  const regenerateKey = async (): Promise<NewApiKeyResponse | null> => {
    setIsRegenerating(true);
    setOperationError(null);

    try {
      const response = await createApiKey({
        name: `API Key ${new Date().toLocaleDateString('es-ES')}`
      });
      await mutate();
      return response;
    } catch (err) {
      const apiError = err instanceof ApiError ? err : new ApiError(
        'Error al regenerar API Key',
        500
      );
      setOperationError(apiError);
      return null;
    } finally {
      setIsRegenerating(false);
    }
  };

  /**
   * Formats an API Key for display (prefix + asterisks + suffix)
   * @param apiKey The API Key to format (optional, uses primary key if not provided)
   * @returns Formatted API Key or null if cannot format
   */
  const getFormattedApiKey = (apiKey?: ApiKey): string | null => {
    const keyToFormat = apiKey || primaryKey;
    if (!keyToFormat?.prefix || !keyToFormat?.last_chars) return null;
    return `${keyToFormat.prefix}••••••••${keyToFormat.last_chars}`;
  };

  return {
    // State data
    data: data ?? null,
    primaryKey: primaryKey ?? null,
    error: error ?? null,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt: 0, // SWR doesn't expose this in the current version

    // Operations
    createApiKey: createNewApiKey,
    updateApiKey: updateExistingApiKey,
    revokeApiKey: revokeExistingApiKey,
    regenerateApiKey: regenerateKey, // Legacy compatibility

    // Operation states
    isCreating,
    isUpdating,
    isRevoking,
    isRegenerating,
    operationError,

    // Utilities
    getFormattedApiKey
  };
}

// Legacy export for backward compatibility
/**
 * @deprecated Use useApiKeys instead. This export is kept for backward compatibility.
 */
export const useMultiApiKeys = useApiKeys;


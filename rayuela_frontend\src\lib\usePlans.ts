// src/lib/usePlans.ts
import useSWR from 'swr';
import { PlanInfo, getPlans } from '@/lib/api';
import { useAuth } from '@/lib/auth';

/**
 * Hook para obtener y gestionar la información de los planes disponibles.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 */
export function usePlans() {
  const { token, apiKey } = useAuth();

  const {
    data: plans,
    error,
    isLoading,
    mutate
  } = useSWR<Record<string, PlanInfo>>(
    token && apiKey ? ['plans', token, apiKey] : null,
    async () => await getPlans(),
    {
      refreshInterval: 300000, // Refresh every 5 minutes
      revalidateOnFocus: false,
    }
  );

  // Función para obtener los límites de un plan específico
  const getPlanLimits = (planId: string) => {
    if (!plans) return null;
    const plan = plans[planId];
    return plan?.limits || null;
  };

  // Función para obtener un plan específico por su ID
  const getPlanById = (planId: string) => {
    if (!plans) return null;
    return plans[planId] || null;
  };

  // Función para obtener todos los planes como un array
  const getAllPlans = () => {
    if (!plans) return [];
    return Object.values(plans);
  };

  // Función para obtener el nombre de un plan
  const getPlanName = (planId: string) => {
    if (!plans) return planId;
    const plan = plans[planId];
    return plan?.name || planId;
  };

  return {
    plans: plans || {},
    error,
    isLoading,
    refresh: mutate,
    getPlanLimits,
    getPlanById,
    getAllPlans,
    getPlanName,
  };
}

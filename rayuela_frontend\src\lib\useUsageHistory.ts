// src/lib/useUsageHistory.ts
import useSWR from 'swr';
import { getUsageHistory } from '@/lib/api';
import { GetUsageHistoryApiV1UsageHistoryGet200Item } from '@/lib/generated/rayuelaAPI';
import { ApiError } from '@/lib/api';
import { useAuth } from '@/lib/auth';

export interface UsageHistoryPoint {
  date: Date;
  apiCalls: number;
  storage: number;
}

export interface UsageHistoryState {
  data: GetUsageHistoryApiV1UsageHistoryGet200Item[] | undefined;
  error: ApiError | null;
  isLoading: boolean;
  mutate: () => void;
}

export interface UsageHistoryMetrics {
  getTotalApiCalls: () => number;
  getPeakUsageDay: () => { date: Date; apiCalls: number } | null;
  getLatestStorageUsage: () => number;
  getChartData: () => Array<{
    date: Date;
    apiCalls: number;
    storage: number;
  }>;
  getGrowthRate: (metric: 'apiCalls' | 'storage') => number;
}

// Type guard for usage history data
function isUsageHistoryItem(item: unknown): item is { date: string; api_calls: number; storage: number } {
  if (!item || typeof item !== 'object') return false;
  const i = item as Record<string, unknown>;
  return typeof i.date === 'string' && typeof i.api_calls === 'number' && typeof i.storage === 'number';
}

/**
 * Hook para obtener y gestionar el historial de uso de la cuenta.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 * 
 * @param startDate Fecha de inicio para el historial (formato ISO string o Date)
 * @param endDate Fecha de fin para el historial (formato ISO string o Date)
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos históricos, estado de carga, errores y funciones de utilidad
 */
export function useUsageHistory(
  startDate?: Date,
  endDate?: Date
): UsageHistoryState & UsageHistoryMetrics {
  const { token, apiKey } = useAuth();

  // Construir las fechas para la consulta
  const start = startDate?.toISOString().split('T')[0];
  const end = endDate?.toISOString().split('T')[0];

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<GetUsageHistoryApiV1UsageHistoryGet200Item[]>(
    token && apiKey && start && end 
      ? ['usage-history', token, apiKey, start, end] 
      : null,
    async () => await getUsageHistory() as GetUsageHistoryApiV1UsageHistoryGet200Item[],
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
      onError: (err) => {
        if (err instanceof ApiError) {
          console.error('Error fetching usage history:', err.message, err.body);
        } else {
          console.error('Unexpected error:', err);
        }
      }
    }
  );

  // Métrica: Total de llamadas API
  const getTotalApiCalls = (): number => {
    if (!data) return 0;
    return data.reduce((sum, point) => {
      if (isUsageHistoryItem(point)) {
        return sum + point.api_calls;
      }
      return sum;
    }, 0);
  };

  // Métrica: Día de mayor uso
  const getPeakUsageDay = (): { date: Date; apiCalls: number } | null => {
    if (!data || data.length === 0) return null;
    
    const validData = data.filter(isUsageHistoryItem);
    if (validData.length === 0) return null;

    const peakDay = validData.reduce((max, point) => 
      point.api_calls > max.api_calls ? point : max, validData[0]);

    return {
      date: new Date(peakDay.date),
      apiCalls: peakDay.api_calls
    };
  };

  // Métrica: Último uso de almacenamiento
  const getLatestStorageUsage = (): number => {
    if (!data || data.length === 0) return 0;
    
    const validData = data.filter(isUsageHistoryItem);
    if (validData.length === 0) return 0;

    const sortedData = validData.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime());
    
    return sortedData[0].storage;
  };

  // Función para obtener datos formateados para gráficos
  const getChartData = (): Array<{
    date: Date;
    apiCalls: number;
    storage: number;
  }> => {
    if (!data) return [];
    
    return data
      .filter(isUsageHistoryItem)
      .sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime())
      .map(point => ({
        date: new Date(point.date),
        apiCalls: point.api_calls,
        storage: point.storage
      }));
  };

  // Función para calcular tasa de crecimiento
  const getGrowthRate = (metric: 'apiCalls' | 'storage'): number => {
    if (!data || data.length < 2) return 0;
    
    const validData = data.filter(isUsageHistoryItem);
    if (validData.length < 2) return 0;

    const sortedData = validData.sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime());
    
    const firstValue = metric === 'apiCalls' ? sortedData[0].api_calls : sortedData[0].storage;
    const lastValue = metric === 'apiCalls' ? 
      sortedData[sortedData.length - 1].api_calls : 
      sortedData[sortedData.length - 1].storage;
    
    if (firstValue === 0) return lastValue > 0 ? 100 : 0;
    
    return ((lastValue - firstValue) / firstValue) * 100;
  };

  return {
    data,
    error: error as ApiError | null,
    isLoading,
    mutate,
    getTotalApiCalls,
    getPeakUsageDay,
    getLatestStorageUsage,
    getChartData,
    getGrowthRate,
  };
}

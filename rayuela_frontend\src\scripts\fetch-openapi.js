#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// URL del documento OpenAPI
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
const OPENAPI_URL = `${API_BASE_URL}/api/openapi.json`;
const OUTPUT_DIR = path.resolve(process.cwd(), 'src/lib/openapi');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'openapi.json');

// Flags para controlar el comportamiento
const FORCE_FETCH = process.argv.includes('--force');
const SKIP_VALIDATION = process.argv.includes('--skip-validation');
const VERBOSE = process.argv.includes('--verbose');

/**
 * <PERSON><PERSON> que la especificación OpenAPI tenga contenido útil
 */
function validateOpenAPISpec(data) {
  const issues = [];

  if (!data.openapi) {
    issues.push('Missing openapi version');
  }

  if (!data.info || !data.info.title) {
    issues.push('Missing API info/title');
  }

  if (!data.paths || Object.keys(data.paths).length === 0) {
    issues.push('No API paths defined - this will generate an empty client');
  }

  if (VERBOSE) {
    console.log(`📊 OpenAPI Validation Results:`);
    console.log(`   - OpenAPI Version: ${data.openapi || 'Missing'}`);
    console.log(`   - API Title: ${data.info?.title || 'Missing'}`);
    console.log(`   - API Version: ${data.info?.version || 'Missing'}`);
    console.log(`   - Number of paths: ${Object.keys(data.paths || {}).length}`);
    console.log(`   - Number of components: ${Object.keys(data.components?.schemas || {}).length}`);
  }

  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * Verifica si el servidor backend está disponible
 */
async function checkServerHealth() {
  try {
    const healthUrl = `${API_BASE_URL}/health`;
    if (VERBOSE) {
      console.log(`🔍 Checking server health at: ${healthUrl}`);
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(healthUrl, {
      headers: { Accept: 'application/json' },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const health = await response.json();
      if (VERBOSE) {
        console.log(`✅ Server health check passed:`, health);
      }
      return true;
    }

    if (VERBOSE) {
      console.log(`⚠️ Server health check failed: ${response.status} ${response.statusText}`);
    }
    
    // Treat 503 as server unavailable for fallback logic
    if (response.status === 503) {
      return false;
    }
    
    return false;
  } catch (error) {
    if (VERBOSE) {
      console.log(`❌ Server health check error:`, error.message);
    }
    return false;
  }
}

async function fetchOpenAPI() {
  try {
    console.log(`🔄 Fetching OpenAPI specification from: ${OPENAPI_URL}`);

    // Check if server is available first
    const serverAvailable = await checkServerHealth();
    if (!serverAvailable && !FORCE_FETCH) {
      throw new Error('BACKEND_UNAVAILABLE');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(OPENAPI_URL, {
      headers: { Accept: 'application/json' },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      // Treat 503 (Service Unavailable) as backend unavailable during build
      if (response.status === 503) {
        throw new Error('BACKEND_UNAVAILABLE');
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Validate the OpenAPI specification
    if (!SKIP_VALIDATION) {
      const validation = validateOpenAPISpec(data);

      if (!validation.isValid) {
        console.warn(`⚠️ OpenAPI specification has issues:`);
        validation.issues.forEach((issue) => console.warn(`   - ${issue}`));

        if (!FORCE_FETCH) {
          throw new Error(`INVALID_OPENAPI: ${validation.issues.join(', ')}`);
        } else {
          console.warn(`⚠️ Proceeding anyway due to --force flag`);
        }
      } else {
        console.log(`✅ OpenAPI specification validation passed`);
      }
    }

    // Asegurarse de que el directorio existe
    await mkdir(OUTPUT_DIR, { recursive: true });

    // Guardar el archivo JSON
    await writeFile(OUTPUT_FILE, JSON.stringify(data, null, 2));

    console.log(`✅ OpenAPI specification saved to: ${OUTPUT_FILE}`);
    console.log(
      `📊 Summary: ${Object.keys(data.paths || {}).length} endpoints, ${Object.keys(data.components?.schemas || {}).length} schemas`,
    );

    return true;
  } catch (error) {
    const errorMessage = error.message || String(error);
    console.error(`❌ Error fetching OpenAPI specification:`, errorMessage);

    // Handle specific error cases
    if (errorMessage.includes('BACKEND_UNAVAILABLE')) {
      console.error(`\n🚨 CRITICAL: Backend server is not available at ${API_BASE_URL}`);
      console.error(`\n💡 To fix this issue:`);
      console.error(`   1. Start the backend server: cd rayuela_backend && python main.py`);
      console.error(`   2. Verify the server is running: curl ${API_BASE_URL}/health`);
      console.error(`   3. Then run: npm run fetch-openapi --verbose`);
      console.error(`   4. Or use --force to skip server check: npm run fetch-openapi -- --force`);
    } else if (errorMessage.includes('INVALID_OPENAPI')) {
      console.error(`\n🚨 CRITICAL: OpenAPI specification is invalid or empty`);
      console.error(`\n💡 This usually means:`);
      console.error(`   1. Backend server is running but has no API endpoints defined`);
      console.error(`   2. FastAPI router configuration is incorrect`);
      console.error(`   3. OpenAPI generation is disabled in FastAPI`);
      console.error(`\n🔧 Check the backend configuration in main.py and api/v1/api.py`);
    }

    // Durante el build, si no podemos conectar al servidor, usar un archivo de fallback
    const isConnectionError =
      error.code === 'ECONNREFUSED' ||
      error.errno === 'ECONNREFUSED' ||
      errorMessage.includes('BACKEND_UNAVAILABLE') ||
      errorMessage.includes('AbortError');

    if (isConnectionError && (process.env.NODE_ENV === 'production' || process.env.ORVAL_USE_STATIC === 'true')) {
      console.log(
        `\n⚠️ Server unavailable during build. Checking for existing OpenAPI file...`,
      );

      // Verificar si ya existe un archivo OpenAPI válido
      if (fs.existsSync(OUTPUT_FILE)) {
        try {
          const existingData = JSON.parse(fs.readFileSync(OUTPUT_FILE, 'utf8'));
          const validation = validateOpenAPISpec(existingData);

          if (validation.isValid || SKIP_VALIDATION) {
            console.log(`✅ Using existing valid OpenAPI file: ${OUTPUT_FILE}`);
            console.log(`📊 Summary: ${Object.keys(existingData.paths || {}).length} endpoints, ${Object.keys(existingData.components?.schemas || {}).length} schemas`);
            return true;
          } else {
            console.warn(`⚠️ Existing OpenAPI file is invalid: ${validation.issues.join(', ')}`);
          }
        } catch (parseError) {
          console.warn(`⚠️ Could not parse existing OpenAPI file: ${parseError.message}`);
        }
      }

      console.error(`\n❌ CRITICAL: No valid OpenAPI specification available for production build`);
      console.error(`\n💡 For production builds, you must:`);
      console.error(`   1. Generate a valid OpenAPI spec in development first`);
      console.error(`   2. Commit the generated openapi.json file to version control`);
      console.error(`   3. Or ensure the backend is available during build`);

      process.exit(1);
    }

    // For development, provide helpful guidance
    if (!FORCE_FETCH) {
      console.error(`\n💡 Development workflow:`);
      console.error(`   1. Start backend: cd rayuela_backend && python main.py`);
      console.error(`   2. Generate OpenAPI: npm run fetch-openapi`);
      console.error(`   3. Generate client: npm run generate-api`);
      console.error(`\n💡 Alternative for this build:`);
      console.error(`   1. Use force mode: npm run fetch-openapi -- --force`);
      console.error(`   2. Skip validation: npm run fetch-openapi -- --skip-validation`);
    }

    return false;
  }
}

async function main() {
  console.log('🚀 OpenAPI Client Generator');
  console.log('===========================');
  console.log(`📡 Backend: ${API_BASE_URL}`);
  console.log(`📄 Output: ${OUTPUT_FILE}`);
  console.log(`🔧 Force: ${FORCE_FETCH}`);
  console.log(`🚫 Skip Validation: ${SKIP_VALIDATION}`);
  console.log(`🔍 Verbose: ${VERBOSE}`);
  console.log('');

  // Check if we should use static OpenAPI file
  if (process.env.ORVAL_USE_STATIC === 'true') {
    console.log(`📋 ORVAL_USE_STATIC=true detected - using existing OpenAPI file if available`);
    
    if (fs.existsSync(OUTPUT_FILE)) {
      try {
        const existingData = JSON.parse(fs.readFileSync(OUTPUT_FILE, 'utf8'));
        const validation = validateOpenAPISpec(existingData);
        
        if (validation.isValid || SKIP_VALIDATION || FORCE_FETCH) {
          console.log(`✅ Using existing valid OpenAPI file: ${OUTPUT_FILE}`);
          console.log(`📊 Summary: ${Object.keys(existingData.paths || {}).length} endpoints, ${Object.keys(existingData.components?.schemas || {}).length} schemas`);
          console.log('\n🎉 OpenAPI client generation completed successfully (using static file)!');
          process.exit(0);
          return;
        } else {
          console.warn(`⚠️ Existing OpenAPI file is invalid: ${validation.issues.join(', ')}`);
          console.log(`🔄 Fallback to fetching from backend...`);
        }
      } catch (parseError) {
        console.warn(`⚠️ Could not parse existing OpenAPI file: ${parseError.message}`);
        console.log(`🔄 Fallback to fetching from backend...`);
      }
    } else {
      console.warn(`⚠️ ORVAL_USE_STATIC=true but no existing OpenAPI file found at ${OUTPUT_FILE}`);
      console.log(`🔄 Fallback to fetching from backend...`);
    }
  }

  const success = await fetchOpenAPI();

  if (success) {
    console.log('\n🎉 OpenAPI client generation completed successfully!');
    process.exit(0);
  } else {
    console.error('\n💥 OpenAPI client generation failed!');
    process.exit(1);
  }
}

// Execute main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
} 
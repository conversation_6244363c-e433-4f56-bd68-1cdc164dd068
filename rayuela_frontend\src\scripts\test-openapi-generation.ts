#!/usr/bin/env node
/**
 * Test script to verify OpenAPI generation is working correctly
 *
 * This script performs comprehensive tests to ensure the OpenAPI
 * specification can be fetched and the API client can be generated.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import fetch from 'node-fetch';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
const OPENAPI_FILE = path.resolve(process.cwd(), 'src/lib/openapi/openapi.json');
const GENERATED_DIR = path.resolve(process.cwd(), 'src/lib/generated');

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

// Type for OpenAPI specification
interface OpenAPISpec {
  openapi: string;
  paths?: Record<string, unknown>;
  components?: {
    schemas?: Record<string, unknown>;
  };
}

class OpenAPITester {
  private results: TestResult[] = [];

  private addResult(name: string, passed: boolean, message: string, details?: any) {
    this.results.push({ name, passed, message, details });
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${name}: ${message}`);
    if (details && process.argv.includes('--verbose')) {
      console.log(`   Details:`, details);
    }
  }

  async testBackendConnectivity(): Promise<void> {
    try {
      const healthUrl = `${API_BASE_URL}/health`;
      console.log(`Checking: ${healthUrl}`);
      
      // Use AbortController for timeout instead of timeout property
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(healthUrl, { signal: controller.signal });
      clearTimeout(timeoutId);

      if (response.ok) {
        const health = await response.json();
        this.addResult(
          'Backend Connectivity',
          true,
          `Backend server is running at ${API_BASE_URL}`,
          health,
        );
      } else {
        this.addResult(
          'Backend Connectivity',
          false,
          `Backend server returned ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error) {
      this.addResult(
        'Backend Connectivity',
        false,
        `Cannot connect to backend server at ${API_BASE_URL}`,
        error,
      );
    }
  }

  async testOpenAPIEndpoint(): Promise<void> {
    try {
      const openApiUrl = `${API_BASE_URL}/api/openapi.json`;
      console.log(`Fetching: ${openApiUrl}`);
      
      // Use AbortController for timeout instead of timeout property
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);
      
      const response = await fetch(openApiUrl, { signal: controller.signal });
      clearTimeout(timeoutId);

      if (response.ok) {
        const openApiSpec = await response.json() as OpenAPISpec;
        
        if (openApiSpec.paths && openApiSpec.components?.schemas) {
          const pathCount = Object.keys(openApiSpec.paths).length;
          const schemaCount = Object.keys(openApiSpec.components.schemas).length;
          
          console.log('✅ OpenAPI specification fetched successfully');
          console.log('📊 OpenAPI Statistics:', 
            { pathCount, schemaCount, version: openApiSpec.openapi });
          
          if (pathCount > 0) {
            this.addResult(
              'OpenAPI Endpoint',
              true,
              `OpenAPI spec available with ${pathCount} paths and ${schemaCount} schemas`,
              { pathCount, schemaCount, version: openApiSpec.openapi },
            );
          } else {
            this.addResult(
              'OpenAPI Endpoint',
              false,
              'OpenAPI spec is empty (no paths defined)',
              openApiSpec,
            );
          }
        } else {
          this.addResult(
            'OpenAPI Endpoint',
            false,
            'OpenAPI spec exists but appears incomplete',
            openApiSpec,
          );
        }
      } else {
        this.addResult(
          'OpenAPI Endpoint',
          false,
          `OpenAPI endpoint returned ${response.status}: ${response.statusText}`,
        );
      }
    } catch (error) {
      this.addResult('OpenAPI Endpoint', false, 'Failed to fetch OpenAPI specification', error);
    }
  }

  testFetchScript(): void {
    try {
      console.log('\n🔄 Testing OpenAPI fetch script...');
      const output = execSync('npm run fetch-openapi:verbose', {
        encoding: 'utf8',
        timeout: 30000,
      });

      this.addResult(
        'Fetch Script',
        true,
        'OpenAPI fetch script executed successfully',
        { output: output.slice(-200) }, // Last 200 chars
      );
    } catch (error) {
      this.addResult('Fetch Script', false, 'OpenAPI fetch script failed', error);
    }
  }

  testOpenAPIFile(): void {
    try {
      if (!fs.existsSync(OPENAPI_FILE)) {
        this.addResult('OpenAPI File', false, `OpenAPI file not found at ${OPENAPI_FILE}`);
        return;
      }

      const content = fs.readFileSync(OPENAPI_FILE, 'utf8');
      const spec = JSON.parse(content);

      const pathCount = Object.keys(spec.paths || {}).length;
      const schemaCount = Object.keys(spec.components?.schemas || {}).length;

      if (pathCount > 0) {
        this.addResult(
          'OpenAPI File',
          true,
          `Valid OpenAPI file with ${pathCount} paths and ${schemaCount} schemas`,
          {
            size: content.length,
            version: spec.openapi,
            title: spec.info?.title,
          },
        );
      } else {
        this.addResult('OpenAPI File', false, 'OpenAPI file exists but has no paths defined', spec);
      }
    } catch (error) {
      this.addResult('OpenAPI File', false, 'Failed to read or parse OpenAPI file', error);
    }
  }

  testOrvalGeneration(): void {
    try {
      console.log('\n🔄 Testing Orval API client generation...');
      const output = execSync('npx orval --config orval.config.ts', {
        encoding: 'utf8',
        timeout: 30000,
      });

      this.addResult('Orval Generation', true, 'Orval API client generation completed', {
        output: output.slice(-200),
      });
    } catch (error) {
      this.addResult('Orval Generation', false, 'Orval API client generation failed', error);
    }
  }

  testGeneratedFiles(): void {
    try {
      if (!fs.existsSync(GENERATED_DIR)) {
        this.addResult(
          'Generated Files',
          false,
          `Generated directory not found at ${GENERATED_DIR}`,
        );
        return;
      }

      const files = fs.readdirSync(GENERATED_DIR, { recursive: true });
      const tsFiles = files.filter((f) => String(f).endsWith('.ts'));

      // Check for key files
      const hasApiClient = files.some((f) => String(f).includes('api-client'));
      const hasSchemas = files.some((f) => String(f).includes('schema'));

      if (tsFiles.length > 0 && hasApiClient) {
        this.addResult(
          'Generated Files',
          true,
          `Generated ${tsFiles.length} TypeScript files including API client`,
          {
            totalFiles: files.length,
            tsFiles: tsFiles.length,
            hasApiClient,
            hasSchemas,
          },
        );
      } else {
        this.addResult('Generated Files', false, 'Generated files are missing or incomplete', {
          files: files.slice(0, 10),
        });
      }
    } catch (error) {
      this.addResult('Generated Files', false, 'Failed to check generated files', error);
    }
  }

  testTypeScriptCompilation(): void {
    try {
      console.log('\n🔄 Testing TypeScript compilation...');
      const output = execSync('npx tsc --noEmit --skipLibCheck', {
        encoding: 'utf8',
        timeout: 30000,
      });

      this.addResult('TypeScript Compilation', true, 'TypeScript compilation successful', {
        output: output || 'No errors',
      });
    } catch (error) {
      const errorOutput = (error as any).stdout || (error as any).stderr || error;
      this.addResult('TypeScript Compilation', false, 'TypeScript compilation failed', errorOutput);
    }
  }

  printSummary(): void {
    console.log('\n📊 TEST SUMMARY');
    console.log('================');

    const passed = this.results.filter((r) => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);

    console.log(`\n✅ Passed: ${passed}/${total} (${percentage}%)`);
    console.log(`❌ Failed: ${total - passed}/${total}`);

    const failed = this.results.filter((r) => !r.passed);
    if (failed.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      failed.forEach((result) => {
        console.log(`   - ${result.name}: ${result.message}`);
      });

      console.log('\n💡 TROUBLESHOOTING:');
      if (failed.some((r) => r.name === 'Backend Connectivity')) {
        console.log('   1. Start backend: cd rayuela_backend && python main.py');
      }
      if (failed.some((r) => r.name === 'OpenAPI Endpoint')) {
        console.log('   2. Check FastAPI router configuration in main.py');
      }
      if (failed.some((r) => r.name === 'Fetch Script')) {
        console.log('   3. Run: npm run fetch-openapi:verbose');
      }
      if (failed.some((r) => r.name === 'Orval Generation')) {
        console.log('   4. Check orval.config.ts configuration');
      }
    }

    console.log('\n📋 NEXT STEPS:');
    if (percentage === 100) {
      console.log('   🎉 All tests passed! OpenAPI generation is working correctly.');
      console.log('   📝 You can now use the generated API client in your frontend code.');
      console.log('   🔄 Run this test after any backend API changes.');
    } else if (percentage >= 70) {
      console.log('   ⚠️ Most tests passed, but some issues need attention.');
      console.log('   🔧 Fix the failed tests and run again.');
    } else {
      console.log('   🚨 Major issues detected. OpenAPI generation is not working.');
      console.log('   📖 See OPENAPI_DEVELOPMENT_GUIDE.md for detailed troubleshooting.');
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 OpenAPI Generation Test Suite');
    console.log('=================================\n');

    await this.testBackendConnectivity();
    await this.testOpenAPIEndpoint();
    this.testFetchScript();
    this.testOpenAPIFile();
    this.testOrvalGeneration();
    this.testGeneratedFiles();
    this.testTypeScriptCompilation();

    this.printSummary();

    // Exit with error code if tests failed
    const failedCount = this.results.filter((r) => !r.passed).length;
    if (failedCount > 0) {
      process.exit(1);
    }
  }
}

// Help text
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🧪 OpenAPI Generation Test Suite

Usage: npm run test-openapi [options]

Options:
  --verbose    Show detailed test output
  --help, -h   Show this help message

This script tests the complete OpenAPI generation pipeline:
1. Backend server connectivity
2. OpenAPI endpoint availability
3. OpenAPI fetch script execution
4. Generated OpenAPI file validation
5. Orval API client generation
6. Generated files verification
7. TypeScript compilation

Prerequisites:
- Backend server running at ${API_BASE_URL}
- Node.js dependencies installed
- TypeScript configured

Examples:
  npm run test-openapi           # Run all tests
  npm run test-openapi --verbose # Detailed output
`);
  process.exit(0);
}

// Main execution
const tester = new OpenAPITester();
tester.runAllTests().catch((error) => {
  console.error('\n💥 Test suite failed with unexpected error:', error);
  process.exit(1);
});

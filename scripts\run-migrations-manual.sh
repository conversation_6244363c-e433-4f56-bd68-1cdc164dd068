#!/bin/bash

# Script para ejecutar migraciones manualmente
# Enfoque simplificado usando Cloud Run Jobs

set -e

# Configuration
PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"
BUILD_ID="9c69ef41-ba7d-4568-9d6c-13558be58e58"
IMAGE_URL="us-central1-docker.pkg.dev/$PROJECT_ID/rayuela-repo/rayuela-backend:$BUILD_ID"

echo "🔄 EJECUTANDO MIGRACIONES MANUALMENTE"
echo "============================================================"
echo "Proyecto: $PROJECT_ID"
echo "Región: $REGION"
echo "Imagen: $IMAGE_URL"
echo ""

# Delete existing job if it exists
echo "🧹 Limpiando job anterior si existe..."
gcloud run jobs delete rayuela-migrations --region=$REGION --quiet 2>/dev/null || echo "No hay job anterior"

# Create the job with working directory set correctly
echo "🚀 Creando job para migraciones..."
gcloud run jobs create rayuela-migrations \
    --image=$IMAGE_URL \
    --region=$REGION \
    --task-timeout=1800 \
    --memory=2Gi \
    --cpu=1 \
    --max-retries=0 \
    --parallelism=1 \
    --set-env-vars=ENV=production,PYTHONPATH=/app/src,WORKER_TYPE=migration \
    --set-secrets=POSTGRES_USER=POSTGRES_USER:latest,POSTGRES_DB=POSTGRES_DB:latest,POSTGRES_SERVER=POSTGRES_SERVER:latest,POSTGRES_PORT=POSTGRES_PORT:latest,POSTGRES_PASSWORD=POSTGRES_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest \
    --vpc-connector=rayuela-vpc-connector \
    --command=bash \
    --args="-c,cd /app && /usr/local/bin/python -m alembic upgrade head"

if [ $? -eq 0 ]; then
    echo "✅ Job creado exitosamente"
else
    echo "❌ Error creando el job"
    exit 1
fi

# Execute the job
echo "🔄 Ejecutando migraciones..."
gcloud run jobs execute rayuela-migrations --region=$REGION --wait

if [ $? -eq 0 ]; then
    echo "🎉 MIGRACIONES COMPLETADAS EXITOSAMENTE"

    # Test backend health
    echo "🏥 Verificando salud del backend..."
    sleep 10
    curl -f https://rayuela-backend-lrw7xazcbq-uc.a.run.app/health || echo "Backend aún no responde"

else
    echo "❌ MIGRACIONES FALLARON"
    echo "📋 Ver logs en: https://console.cloud.google.com/run/jobs"
    exit 1
fi

echo ""
echo "🎉 PROCESO COMPLETADO"
echo ""
echo "📋 Próximos pasos:"
echo "   1. Verificar que el backend responde correctamente"
echo "   2. Habilitar servicios de Celery"
echo "   3. Ejecutar health checks completos"

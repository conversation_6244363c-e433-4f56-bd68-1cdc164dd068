# Troubleshooting Scripts

Scripts utilizados para resolver problemas de deployment del backend Rayuela.

## Scripts Disponibles

### `container-dependency-fix.sh`
**Propósito**: Arregla problemas de dependencias en el container Docker
- Instala librerías PostgreSQL necesarias (libpq-dev, libpq5)
- Verifica que FastAPI y otras dependencias críticas se instalen correctamente
- Reconstruye y despliega el container con las dependencias correctas

**Uso**: 
```bash
cd scripts/troubleshooting
./container-dependency-fix.sh
```

### `production-config-fix.sh`
**Propósito**: Corrige configuraciones de producción faltantes
- Configura SECRET_KEY, REDIS_URL, ALLOWED_ORIGINS
- Actualiza variables de entorno de Cloud Run
- Resuelve errores de validación de configuración

**Uso**:
```bash
cd scripts/troubleshooting  
./production-config-fix.sh
```

## Problemas Resueltos

1. **Container Dependencies**: FastAPI y otras dependencias no se instalaban correctamente
2. **Production Config**: Variables de entorno requeridas faltantes en producción
3. **Worker Timeouts**: Causados por imports fallidos de dependencias faltantes

## Backend Status

- ✅ Container build exitoso
- ✅ Dependencies correctamente instaladas
- ⚠️ Configuración de producción pendiente (ejecutar production-config-fix.sh)
- 🔗 URL: https://rayuela-backend-1002953244539-lrw7xazcbq-uc.a.run.app

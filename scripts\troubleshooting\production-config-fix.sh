#!/bin/bash

echo "=== FIXING PRODUCTION CONFIGURATION ISSUES ==="

PROJECT_ID="lateral-insight-461112-g9"
SERVICE_NAME="rayuela-backend-1002953244539"
REGION="us-central1"

echo "🔧 Updating Cloud Run service with correct production configuration..."

# Get Redis connection string (assuming it's been created)
REDIS_HOST=$(gcloud redis instances describe rayuela-redis --region=us-central1 --project=${PROJECT_ID} --format="value(host)" 2>/dev/null || echo "********")

gcloud run services update ${SERVICE_NAME} \
  --region=${REGION} \
  --update-env-vars="SECRET_KEY=rayuela-production-secret-key-$(date +%s),REDIS_URL=redis://${REDIS_HOST}:6379,ALLOWED_ORIGINS=https://rayuela-frontend.com,DATABASE_URL=********************************************/rayuela_production" \
  --project=${PROJECT_ID}

if [ $? -eq 0 ]; then
    echo "✅ Configuration updated successfully!"
    
    echo "🔄 Waiting for new revision to deploy..."
    sleep 20
    
    SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
      --region=${REGION} \
      --project=${PROJECT_ID} \
      --format='value(status.url)')
    
    echo "🧪 Testing the fixed service..."
    echo "Service URL: ${SERVICE_URL}"
    
    echo "Testing health endpoint..."
    curl -s -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
      "${SERVICE_URL}/health"
    
    echo ""
    echo "Testing root endpoint..."
    curl -s -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
      "${SERVICE_URL}/"
      
    echo ""
    echo "🎉 PRODUCTION CONFIGURATION FIX COMPLETED!"
    echo "✅ Backend URL: ${SERVICE_URL}"
    
else
    echo "❌ Configuration update failed"
    exit 1
fi 